//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Common.Lib.Model
{
    using System;
    using System.Collections.Generic;
    
    public  class RLT_ROUTES_DETAILS
    {
        public long PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        public Nullable<int> Route_Distance_KM { get; set; }
        public Nullable<int> Total_Paid_Tolls_In_Route { get; set; }
        public Nullable<int> Pickup_City_PKID { get; set; }
        public Nullable<int> Dropoff_City_PKID { get; set; }
        public Nullable<int> Car_Category_PKID { get; set; }
        public Nullable<int> Car_Trip_Type_PKID { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
    
        public virtual RLT_CAR_CATEGORY RLT_CAR_CATEGORY { get; set; }
        public virtual RLT_CITY RLT_CITY { get; set; }
        public virtual RLT_CITY RLT_CITY1 { get; set; }
        public virtual RLT_TRIP_TYPES RLT_TRIP_TYPES { get; set; }
    }
}

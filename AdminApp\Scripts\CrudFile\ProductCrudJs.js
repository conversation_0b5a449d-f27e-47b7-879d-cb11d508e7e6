﻿
$(document).ready(function ()
{
    //$('#ddlPackageDestination').multiSelect(
    //    {
    //        includeSelectAllOption: false,
    //        nonSelectedText :'--Select Destination--',
    //        enableCaseInsensitiveFiltering: true,
    //        enableFiltering: true,
          

    //    }
    //    );
    $('#ddlPackageDestination').multiselect({
        includeSelectAllOption: false,
        nonSelectedText: '--Select Destination--',
        enableCaseInsensitiveFiltering: true,
        enableFiltering: true,
    });
    $("#lblFileName").slideUp();
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function ()
    {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnSave").click(function ()
    {
        var cities = [];
        $('#ddlPackageDestination :checked').each(function () {
            cities.push($(this).val());
        });
        $('#PackageDestination').val(cities)
    });
    

    $("#btnReset").click(function ()
    {
        $("#btnSave").slideDown();
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        $("#btnUpdate").slideUp();
        resetForm();
    });

});


function Edit(Id)
{
    var cities = $("#tripLocation_" + Id).val();




    $("#PackageDestination").val(cities.split(","));

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
  
    $("#IsActive").val(true);

    $('#TourTypeID').val($("#tourtype_" + Id).val());
    $("#PackageID").val($("#package_" + Id).val());
    $("#StateID").val($("#state_" + Id).val());
    $("#ProductName").val($("#name_" + Id).val());
    $("#ProductCode").val($("#code_" + Id).val());
    $("#PackageDayStay").val($("#days_" + Id).val());
    $("#PackageNightStay").val($("#nights_" + Id).val());
   
 

    $("#lblFileName").slideDown();
    $("#lblFileName").show();
    $("#lblFileName").text($("#img_" + Id).val());
    $("#ProductImage").val($("#img_" + Id).val());
    $("#ProductURL").val($("#redirectURL_" + Id).val());

    $("#ProductMetaTag").val($("#tag_" + Id).val());
    $("#ProductMetaData").val($("#data_" + Id).val());
    $("#ProductMetaDescription").val($("#desc_" + Id).val());

    tinyMCE.get('ProductDescription').setContent($("#about_" + Id).val());
}

function Delete(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');

    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }
    $('#TourTypeID').val($("#tourtype_" + Id).val());
    $("#PackageID").val($("#package_" + Id).val());
    $("#StateID").val($("#state_" + Id).val());
    $("#ProductName").val($("#name_" + Id).val());
    $("#ProductCode").val($("#code_" + Id).val());
    $("#PackageDayStay").val($("#days_" + Id).val());
    $("#PackageNightStay").val($("#nights_" + Id).val());
    $("#PackageDestination").val($("#tripLocation_" + Id).val());
    $("#ProductURL").val($("#redirectURL_" + Id).val());
    $("#lblFileName").slideDown();
    $("#lblFileName").show();
    $("#lblFileName").text($("#img_" + Id).val());

    
    $("#ProductImage").val($("#img_" + Id).val());

    $("#ProductMetaTag").val($("#tag_" + Id).val());
    $("#ProductMetaData").val($("#data_" + Id).val());
    $("#ProductMetaDescription").val($("#desc_" + Id).val());

    tinyMCE.get('ProductDescription').setContent($("#about_" + Id).val());
}

function ActivateDeactivate(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');

    $("#ID").val(Id);

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $('#TourTypeID').val($("#tourtype_" + Id).val());
    $("#PackageID").val($("#package_" + Id).val());
    $("#StateID").val($("#state_" + Id).val());
    $("#ProductName").val($("#name_" + Id).val());
    $("#ProductCode").val($("#code_" + Id).val());
    $("#PackageDayStay").val($("#days_" + Id).val());
    $("#PackageNightStay").val($("#nights_" + Id).val());
    $("#PackageDestination").val($("#tripLocation_" + Id).val());
    $("#ProductImage").val($("#img_" + Id).val());
    $("#lblFileName").slideDown();
    $("#lblFileName").show();
    $("#lblFileName").text($("#img_" + Id).val());

    $("#ProductURL").val($("#redirectURL_" + Id).val());

    $("#ProductMetaTag").val($("#tag_" + Id).val());
    $("#ProductMetaData").val($("#data_" + Id).val());
    $("#ProductMetaDescription").val($("#desc_" + Id).val());

    tinyMCE.get('ProductDescription').setContent($("#about_" + Id).val());
}

function resetForm()
{
    $("#ID").val('');
    $("#hdnOperation").val('');
    $("#hdnStatus").val('');
    $("#IsActive").val('');
    $("#TourTypeID").val('');
    $("#PackageID").val('');
    $("#StateID").val('');
    $("#ProductName").val('');
    $("#ProductCode").val('');
    $("#PackageDayStay").val('');
    $("#PackageNightStay").val('');
    $("#PackageDestination").val('');
    $("#ProductImage").val('');
    $("#ProductDescription").val('');
    $("#lblFileName").text('');
    $("#ProductMetaTag").val('');
    $("#ProductMetaData").val('');
    $("#ProductMetaDescription").val('');
    tinyMCE.get('ProductDescription').setContent('');
}

function IsValidate()
{
   

   $("#IsActive").val(true);

    var selectedCities = [];
    $.each($("#ddlPackageDestination option:selected"), function () {
        selectedCities.push($(this).val());
    });


    $("#ddlPackageDestination option:selected").each(function ()
    {
        selectedCities.push($(this).val());
    });



    $("#PackageDestination").val(selectedCities.join());


    if ($("#TourTypeID").val() == "") {
        toastr.clear();
        toastr.error("Please select tour type !");
        return false;
    }
    if ($("#PackageID").val() == "") {
        toastr.clear();
        toastr.error("Please select package !");
        return false;
    }
    if ($("#StateID").val() == "") {
        toastr.clear();
        toastr.error("Please select state !");
        return false;

    }
    if ($("#ProductName").val() == "") {
        toastr.clear();
        toastr.error("Please enter product name !");
        return false;
    }
    if ($("#ProductCode").val() == "") {
        toastr.clear();
        toastr.error("Please enter product code !");
        return false;
    }
    if ($("#PackageDestination").val() == "")
    {
        toastr.clear();
        toastr.error("Please select destination !");
        return false;
    }
   
 
    return true;
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Role_Menu_Mapping
    {
        public int PKID { get; set; }
        public int Role_Id { get; set; }
        public string RoleName { get; set; }
        public int Menu_Id { get; set; }
        public string MenuName { get; set; }
        public string MenuIcon { get; set; }
        public string MenuURL { get; set; }
        public string PageId { get; set; }
        public bool Is_Add { get; set; }
        public bool Is_Edit { get; set; }
        public bool Is_Delete { get; set; }
        public bool Is_View { get; set; }
        public int ParentMenuId { get; set; }
        public double? OrderNumber { get; set; }
        public string ActionName { get; set; }
        public string ControllerName { get; set; }

        public string ActiveMenuClass { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public List<M_Role_Menu_Mapping> eRoleMenuMappingList { get; set; }

    }
}

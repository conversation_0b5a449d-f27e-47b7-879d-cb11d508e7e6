﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{AF2B2E0B-1826-4CFB-846B-7B582DD849DF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AdminApp</RootNamespace>
    <AssemblyName>AdminApp</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit, Version=4.1.50731.0, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <HintPath>..\packages\AjaxControlToolkit4.1.50731.4.1.50731.0\lib\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebDesign, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.WebDesign.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.314.76\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Razorpay, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Razorpay.3.1.4\lib\net45\Razorpay.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.2\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Unity.Abstractions, Version=5.11.7.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.11.10\lib\net46\Unity.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Container, Version=5.11.11.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.5.11.10\lib\net46\Unity.Container.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mvc5, Version=1.4.0.0, Culture=neutral, PublicKeyToken=43da31bc42a85347, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Mvc5.1.4.0\lib\net45\Unity.Mvc5.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\UnityConfig.cs" />
    <Compile Include="Controllers\AdminController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\BookingManagementController.cs" />
    <Compile Include="Controllers\CabYaariController.cs" />
    <Compile Include="Controllers\ErrorController.cs" />
    <Compile Include="Controllers\GoogleMapController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\CommonController.cs" />
    <Compile Include="Controllers\LoginController.cs" />
    <Compile Include="Controllers\BookingFareManagementController.cs" />
    <Compile Include="Controllers\MapMyIndiaController.cs" />
    <Compile Include="Controllers\RazorPayController.cs" />
    <Compile Include="Controllers\ReportController.cs" />
    <Compile Include="Controllers\MailSMSController.cs" />
    <Compile Include="Controllers\UserManagementController.cs" />
    <Compile Include="Controllers\VendorManagementController.cs" />
    <Compile Include="DATASET\ds_BookingReport.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ds_BookingReport.xsd</DependentUpon>
    </Compile>
    <Compile Include="DATASET\ds_Invoice.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ds_Invoice.xsd</DependentUpon>
    </Compile>
    <Compile Include="Filters\CustomAuthorizeAttribute.cs" />
    <Compile Include="Filters\CustomPrincipal.cs" />
    <Compile Include="Filters\ExceptionHandlerAttribute.cs" />
    <Compile Include="Filters\ExceptionLogging.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Mapper\ImageWaterMark.cs" />
    <Compile Include="Mapper\ObjectMapper.cs" />
    <Compile Include="Mapper\QueryStringEncoding.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\RoleBaseAuthentication.cs" />
    <Compile Include="Services\SMSService.cs" />
    <Compile Include="Services\UserServices.cs" />
    <Compile Include="CommonRdlcReport.aspx.cs">
      <DependentUpon>CommonRdlcReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CommonRdlcReport.aspx.designer.cs">
      <DependentUpon>CommonRdlcReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="SqlServerTypes\Loader.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\assets\css\RoleMenuMapping.css" />
    <Content Include="Content\assets\css\custom.css" />
    <Content Include="Content\assets\css\exrtapages\animate_page.css" />
    <Content Include="Content\assets\css\exrtapages\formlayout.css" />
    <Content Include="Content\assets\css\exrtapages\pages.css" />
    <Content Include="Content\assets\css\material_style.css" />
    <Content Include="Content\assets\css\plugins.min.css" />
    <Content Include="Content\assets\css\responsive.css" />
    <Content Include="Content\assets\css\style.css" />
    <Content Include="Content\assets\css\theme-color.css" />
    <Content Include="Content\assets\js\app.js" />
    <Content Include="Content\assets\js\layout.js" />
    <Content Include="Content\assets\js\pages\chart\morris\morris_home_data.js" />
    <Content Include="Content\assets\js\pages\material_select\getmdl-select.js" />
    <Content Include="Content\assets\js\pages\select2\select2-init.js" />
    <Content Include="Content\assets\js\pages\table\editable_table_data.js" />
    <Content Include="Content\assets\js\pages\table\table_data.js" />
    <Content Include="Content\assets\js\pages\ui\animations.js" />
    <Content Include="Content\assets\js\theme-color.js" />
    <Content Include="Content\assets\js\validation\login.js" />
    <Content Include="Content\assets\plugins\bootstrap\css\bootstrap.css" />
    <Content Include="Content\assets\plugins\bootstrap\css\bootstrap.min.css" />
    <Content Include="Content\assets\plugins\bootstrap\js\bootstrap.js" />
    <Content Include="Content\assets\plugins\bootstrap\js\bootstrap.min.js" />
    <Content Include="Content\assets\plugins\circle-progress\Circle-function.js" />
    <Content Include="Content\assets\plugins\circle-progress\circle-progress.min.js" />
    <Content Include="Content\assets\plugins\circle-progress\Circle.css" />
    <Content Include="Content\assets\plugins\datatables\datatables.all.min.js" />
    <Content Include="Content\assets\plugins\datatables\datatables.min.css" />
    <Content Include="Content\assets\plugins\datatables\datatables.min.js" />
    <Content Include="Content\assets\plugins\datatables\editabletable.js" />
    <Content Include="Content\assets\plugins\datatables\images\back_disabled.png" />
    <Content Include="Content\assets\plugins\datatables\images\back_enabled.png" />
    <Content Include="Content\assets\plugins\datatables\images\back_enabled_hover.png" />
    <Content Include="Content\assets\plugins\datatables\images\forward_disabled.png" />
    <Content Include="Content\assets\plugins\datatables\images\forward_enabled.png" />
    <Content Include="Content\assets\plugins\datatables\images\forward_enabled_hover.png" />
    <Content Include="Content\assets\plugins\datatables\images\sort_asc.png" />
    <Content Include="Content\assets\plugins\datatables\images\sort_asc_disabled.png" />
    <Content Include="Content\assets\plugins\datatables\images\sort_both.png" />
    <Content Include="Content\assets\plugins\datatables\images\sort_desc.png" />
    <Content Include="Content\assets\plugins\datatables\images\sort_desc_disabled.png" />
    <Content Include="Content\assets\plugins\datatables\jquery.dataTables.min.css" />
    <Content Include="Content\assets\plugins\datatables\jquery.dataTables.min.js" />
    <Content Include="Content\assets\plugins\datatables\plugins\bootstrap\datatables.bootstrap.css" />
    <Content Include="Content\assets\plugins\datatables\plugins\bootstrap\datatables.bootstrap.js" />
    <Content Include="Content\assets\plugins\datatables\plugins\bootstrap\dataTables.bootstrap4.min.css" />
    <Content Include="Content\assets\plugins\datatables\plugins\bootstrap\dataTables.bootstrap4.min.js" />
    <Content Include="Content\assets\plugins\dropzone\dropzone-call.js" />
    <Content Include="Content\assets\plugins\dropzone\dropzone.css" />
    <Content Include="Content\assets\plugins\dropzone\dropzone.js" />
    <Content Include="Content\assets\plugins\font-awesome\css\font-awesome.min.css" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="Content\assets\plugins\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="Content\assets\plugins\iconic\css\material-design-iconic-font.css" />
    <Content Include="Content\assets\plugins\iconic\css\material-design-iconic-font.min.css" />
    <Content Include="Content\assets\plugins\iconic\fonts\Material-Design-Iconic-Font.eot" />
    <Content Include="Content\assets\plugins\iconic\fonts\Material-Design-Iconic-Font.svg" />
    <Content Include="Content\assets\plugins\iconic\fonts\Material-Design-Iconic-Font.ttf" />
    <Content Include="Content\assets\plugins\iconic\fonts\Material-Design-Iconic-Font.woff" />
    <Content Include="Content\assets\plugins\iconic\fonts\Material-Design-Iconic-Font.woff2" />
    <Content Include="Content\assets\plugins\jquery-tags-input\jquery-tags-input-init.js" />
    <Content Include="Content\assets\plugins\jquery-tags-input\jquery-tags-input.css" />
    <Content Include="Content\assets\plugins\jquery-tags-input\jquery-tags-input.js" />
    <Content Include="Content\assets\plugins\jquery\jquery.min.js" />
    <Content Include="Content\assets\plugins\material-datetimepicker\bootstrap-material-datetimepicker.css" />
    <Content Include="Content\assets\plugins\material-datetimepicker\bootstrap-material-datetimepicker.js" />
    <Content Include="Content\assets\plugins\material-datetimepicker\bootstrap-material-design.min.css" />
    <Content Include="Content\assets\plugins\material-datetimepicker\datetimepicker.js" />
    <Content Include="Content\assets\plugins\material-datetimepicker\moment-with-locales.min.js" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\material-icons.css" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.svg" />
    <Content Include="Content\assets\plugins\material\material.min.css" />
    <Content Include="Content\assets\plugins\material\material.min.js" />
    <Content Include="Content\assets\plugins\material\morris\morris.min.js" />
    <Content Include="Content\assets\plugins\material\morris\raphael-min.js" />
    <Content Include="Content\assets\plugins\morris\morris.css" />
    <Content Include="Content\assets\plugins\popper\popper.min.js" />
    <Content Include="Content\assets\plugins\select2\css\select2-bootstrap.min.css" />
    <Content Include="Content\assets\plugins\select2\css\select2.css" />
    <Content Include="Content\assets\plugins\select2\img\select2-spinner.gif" />
    <Content Include="Content\assets\plugins\select2\img\select2.png" />
    <Content Include="Content\assets\plugins\select2\img\select2x2.png" />
    <Content Include="Content\assets\plugins\select2\js\select2-init.js" />
    <Content Include="Content\assets\plugins\select2\js\select2.js" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.dev.svg" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.svg" />
    <Content Include="Content\assets\plugins\simple-line-icons\icons-lte-ie7.js" />
    <Content Include="Content\assets\plugins\simple-line-icons\License.txt" />
    <Content Include="Content\assets\plugins\simple-line-icons\Readme.txt" />
    <Content Include="Content\assets\plugins\simple-line-icons\simple-line-icons.min.css" />
    <Content Include="Content\assets\TabWizard\jquery-1.9.1.min.js" />
    <Content Include="Content\assets\TabWizard\jquery.steps.css" />
    <Content Include="Content\assets\TabWizard\jquery.steps.js" />
    <Content Include="Content\assets\TabWizard\main.css" />
    <Content Include="Content\assets\TabWizard\modernizr-2.6.2.min.js" />
    <Content Include="Content\assets\TabWizard\normalize.css" />
    <Content Include="Content\assets\toastr\toastr.css" />
    <Content Include="Content\assets\toastr\toastr.min.css" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-grid.rtl.css" />
    <Content Include="Content\bootstrap-grid.rtl.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-reboot.rtl.css" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css" />
    <Content Include="Content\bootstrap-utilities.css" />
    <Content Include="Content\bootstrap-utilities.min.css" />
    <Content Include="Content\bootstrap-utilities.rtl.css" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\bootstrap.rtl.css" />
    <Content Include="Content\bootstrap.rtl.min.css" />
    <Content Include="Content\img\ajax-loader.gif" />
    <Content Include="Content\img\attachment.png" />
    <Content Include="Content\img\no-image.gif" />
    <Content Include="Content\img\no-user-image.gif" />
    <Content Include="Content\img\RLT TOURS.png" />
    <Content Include="Content\img\RLT TOURS2.png" />
    <Content Include="Content\img\Sign.jpg" />
    <Content Include="Docs\No_image_available.png" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.eot" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.ttf" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.woff" />
    <Content Include="Content\assets\plugins\simple-line-icons\fonts\Simple-Line-Icons.woff2" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\codepoints" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.eot" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.ijmap" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.ttf" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.woff" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\MaterialIcons-Regular.woff2" />
    <Content Include="Content\assets\plugins\material-icons\iconfont\README.md" />
    <Content Include="Content\assets\plugins\datatables\images\Sorting icons.psd" />
    <Content Include="Content\assets\toastr\toastr.less" />
    <Content Include="Content\assets\toastr\toastr.scss" />
    <Content Include="DATASET\ds_BookingReport.xsc">
      <DependentUpon>ds_BookingReport.xsd</DependentUpon>
    </Content>
    <Content Include="CommonRdlcReport.aspx" />
    <Content Include="Content\bootstrap.rtl.min.css.map" />
    <Content Include="Content\bootstrap.rtl.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.css.map" />
    <Content Include="Content\bootstrap-utilities.min.css.map" />
    <Content Include="Content\bootstrap-utilities.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.min.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <None Include="DATASET\ds_BookingReport.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>ds_BookingReport.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DATASET\ds_BookingReport.xss">
      <DependentUpon>ds_BookingReport.xsd</DependentUpon>
    </Content>
    <Content Include="DATASET\ds_Invoice.xsc">
      <DependentUpon>ds_Invoice.xsd</DependentUpon>
    </Content>
    <None Include="DATASET\ds_Invoice.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>ds_Invoice.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DATASET\ds_Invoice.xss">
      <DependentUpon>ds_Invoice.xsd</DependentUpon>
    </Content>
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.esm.js" />
    <Content Include="Scripts\bootstrap.esm.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\CrudFile\CabYaariServices.js" />
    <Content Include="Scripts\CrudFile\UserManagement-MenuList.js" />
    <Content Include="Scripts\CrudFile\CabYariHome.js" />
    <Content Include="Scripts\CrudFile\BookingRules.js" />
    <Content Include="Scripts\CrudFile\FAQCrud.js" />
    <Content Include="Scripts\CrudFile\SendMailMessageCrud.js" />
    <Content Include="Scripts\CrudFile\TemplateCrud.js" />
    <Content Include="Scripts\CrudFile\AboutCrudJs.js" />
    <Content Include="Scripts\CrudFile\BankNameCrudJs.js" />
    <Content Include="Scripts\CrudFile\BannerCrudJs.js" />
    <Content Include="Scripts\CrudFile\Booking.js" />
    <Content Include="Scripts\CrudFile\BookingAssignCab.js" />
    <Content Include="Scripts\CrudFile\BookingNew.js" />
    <Content Include="Scripts\CrudFile\BookingList.js" />
    <Content Include="Scripts\CrudFile\BookingStatusCrudJs.js" />
    <Content Include="Scripts\CrudFile\CarCategoryCrudJs.js" />
    <Content Include="Scripts\CrudFile\CarChargesFacilitiesDetailsCrudJs.js" />
    <Content Include="Scripts\CrudFile\CARCompanyCrudJs.js" />
    <Content Include="Scripts\CrudFile\CarDetailsCrud.js" />
    <Content Include="Scripts\CrudFile\EmployeeList.js" />
    <Content Include="Scripts\CrudFile\Employee.js" />
    <Content Include="Scripts\CrudFile\UserManager.js" />
    <Content Include="Scripts\CrudFile\CARDRIVERDETAILSCrudJs.js" />
    <Content Include="Scripts\CrudFile\CARModelCrudJs.js" />
    <Content Include="Scripts\CrudFile\CarOwnerBankDetailsCrudJs.js" />
    <Content Include="Scripts\CrudFile\CityCrudJs.js" />
    <Content Include="Scripts\CrudFile\ContactInfoCrudJs.js" />
    <Content Include="Scripts\CrudFile\ContactListCrudJs.js" />
    <Content Include="Scripts\CrudFile\DiscountCouponCrudJs.js" />
    <Content Include="Scripts\CrudFile\CountryCrudJs.js" />
    <Content Include="Scripts\CrudFile\DashboardCrudJs.js" />
    <Content Include="Scripts\CrudFile\DocNameCrud.js" />
    <Content Include="Scripts\CrudFile\DriveApproveStatusCrudJs.js" />
    <Content Include="Scripts\CrudFile\EnquiryCrudJs.js" />
    <Content Include="Scripts\CrudFile\VendorBankCrud.js" />
    <Content Include="Scripts\CrudFile\FuelTypeCrudJs.js" />
    <Content Include="Scripts\CrudFile\LOCATIONCODECrudJs.js" />
    <Content Include="Scripts\CrudFile\PackageCrudJs.js" />
    <Content Include="Scripts\CrudFile\PAYMENTMETHODCrudJs.js" />
    <Content Include="Scripts\CrudFile\PolicyCrudJs.js" />
    <Content Include="Scripts\CrudFile\PrivateDayTourCrudJs.js" />
    <Content Include="Scripts\CrudFile\ProductCrudJs.js" />
    <Content Include="Scripts\CrudFile\RouteDetailsCrudJs.js" />
    <Content Include="Scripts\CrudFile\ServieCrudJs.js" />
    <Content Include="Scripts\CrudFile\SocialMediaCrudJs.js" />
    <Content Include="Scripts\CrudFile\StateCrudJs.js" />
    <Content Include="Scripts\CrudFile\SubscriberCrudJs.js" />
    <Content Include="Scripts\CrudFile\TermCrudJs.js" />
    <Content Include="Scripts\CrudFile\TourTypeCrudJs.js" />
    <Content Include="Scripts\CrudFile\TripTypeCrudJs.js" />
    <Content Include="Scripts\CrudFile\UserFeedBackCrudJs.js" />
    <Content Include="Scripts\CrudFile\FareManagerCrud.js" />
    <Content Include="Scripts\CrudFile\CarDriverCrud.js" />
    <Content Include="Scripts\CrudFile\CarOwnerCrud.js" />
    <Content Include="Scripts\CrudFile\VendorCrudManager.js" />
    <Content Include="Scripts\CrudFile\CarDocsCrud.js" />
    <Content Include="Scripts\CrudFile\CarDriverDocsCrud.js" />
    <Content Include="Scripts\CrudFile\RoleMenuMapping.js" />
    <Content Include="Scripts\CrudFile\VendorDocsCrud.js" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.esm.min.js.map" />
    <Content Include="Scripts\bootstrap.esm.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <None Include="Scripts\jquery-3.5.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.5.1.js" />
    <Content Include="Scripts\jquery-3.5.1.min.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\RLTGeneral.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\toastr.js" />
    <Content Include="Scripts\toastr.min.js" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll" />
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll" />
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Login\AcessDenied.cshtml" />
    <Content Include="Views\Login\Index.cshtml" />
    <Content Include="Views\Login\VerifyTwoFactorAuthentication.cshtml" />
    <Content Include="Views\Shared\_MenuPartial.cshtml" />
    <Content Include="Views\Shared\_HeaderPartial.cshtml" />
    <Content Include="Views\Admin\AboutUsManager.cshtml" />
    <Content Include="Views\Admin\BankName.cshtml" />
    <Content Include="Views\Admin\BookingStatus.cshtml" />
    <Content Include="Views\Admin\CarBookingStatus.cshtml" />
    <Content Include="Views\Admin\CarCategory.cshtml" />
    <Content Include="Views\Admin\CarChargesFacilitiesDetails.cshtml" />
    <Content Include="Views\Admin\CARCompany.cshtml" />
    <Content Include="Views\Admin\CARDETAILS.cshtml" />
    <Content Include="Views\Admin\CARDRIVERDETAILS.cshtml" />
    <Content Include="Views\Admin\CARModel.cshtml" />
    <Content Include="Views\Admin\CarOwnerBankDetails.cshtml" />
    <Content Include="Views\Admin\CityManager.cshtml" />
    <Content Include="Views\Admin\ContactInformationManager.cshtml" />
    <Content Include="Views\Admin\CountryManager.cshtml" />
    <Content Include="Views\Admin\Dashboard.cshtml" />
    <Content Include="Views\Admin\DriveApproveStatus.cshtml" />
    <Content Include="Views\Admin\FuelType.cshtml" />
    <Content Include="Views\Admin\GenerateQRCODE.cshtml" />
    <Content Include="Views\Admin\LOCATIONCODE.cshtml" />
    <Content Include="Views\Admin\PAYMENTMETHOD.cshtml" />
    <Content Include="Views\Admin\PrivacyAndPolicy.cshtml" />
    <Content Include="Views\Admin\RouteDetails.cshtml" />
    <Content Include="Views\Admin\ServiceManager.cshtml" />
    <Content Include="Views\Admin\SocialMediaManager.cshtml" />
    <Content Include="Views\Admin\StateManager.cshtml" />
    <Content Include="Views\Admin\SubscriberManager.cshtml" />
    <Content Include="Views\Admin\TermsAndConditions.cshtml" />
    <Content Include="Views\Admin\TripType.cshtml" />
    <Content Include="Views\Admin\TwoFactorAuth.cshtml" />
    <Content Include="Views\Admin\UserFeedBack.cshtml" />
    <Content Include="Views\VendorManagement\VendorManager.cshtml" />
    <Content Include="Views\VendorManagement\VendorDocs.cshtml" />
    <Content Include="Views\Admin\DocumentName.cshtml" />
    <Content Include="Views\_DocumentUpload.cshtml" />
    <Content Include="Views\Admin\TempBookingResult.cshtml" />
    <Content Include="Views\BookingFareManagement\DiscountCouponManager.cshtml" />
    <Content Include="Views\BookingFareManagement\FareManager.cshtml" />
    <Content Include="Views\VendorManagement\VendorBankDetails.cshtml" />
    <Content Include="Views\VendorManagement\VendorManagerView.cshtml" />
    <Content Include="Views\VendorManagement\CarDriverManager.cshtml" />
    <Content Include="Views\VendorManagement\CarOwnerManager.cshtml" />
    <Content Include="Views\VendorManagement\CarManager.cshtml" />
    <Content Include="Views\VendorManagement\CarDocs.cshtml" />
    <Content Include="Views\VendorManagement\CarDriverDocs.cshtml" />
    <Content Include="Views\BookingManagement\BookingList.cshtml" />
    <Content Include="Views\BookingManagement\AssignCab.cshtml" />
    <Content Include="Views\BookingManagement\NewBooking.cshtml" />
    <Content Include="Views\BookingManagement\ViewBooking.cshtml" />
    <Content Include="Views\VendorManagement\CarDriverView.cshtml" />
    <Content Include="Views\VendorManagement\CarView.cshtml" />
    <Content Include="Views\BookingFareManagement\FareManagerView.cshtml" />
    <Content Include="Views\VendorManagement\CarOwnerView.cshtml" />
    <Content Include="Views\GoogleMap\Demo1.cshtml" />
    <Content Include="Views\UserManagement\UserManager.cshtml" />
    <Content Include="Views\UserManagement\RoleMenuMapping.cshtml" />
    <Content Include="Views\UserManagement\_RoleMenuMapping.cshtml" />
    <Content Include="Views\Report\BookingReport.cshtml" />
    <Content Include="Views\UserManagement\EmployeeList.cshtml" />
    <Content Include="Views\UserManagement\Employee.cshtml" />
    <Content Include="Views\UserManagement\EmployeeView.cshtml" />
    <Content Include="Views\MailSMS\TemplateManager.cshtml" />
    <Content Include="Views\MailSMS\SendMailMessageManager.cshtml" />
    <Content Include="Views\Report\BookingInvoiceReport.cshtml" />
    <Content Include="Views\Admin\FAQManager.cshtml" />
    <Content Include="Views\BookingFareManagement\BookingRulesManager.cshtml" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\RazorPay\RazorPayPayment.cshtml" />
    <Content Include="Views\BookingManagement\RazorPayPayment.cshtml" />
    <Content Include="Views\BookingManagement\RazorPayPayment_Success.cshtml" />
    <Content Include="Views\RazorPay\RazorPayPayment_Success.cshtml" />
    <Content Include="Views\VendorManagement\BecomeDriverRequest.cshtml" />
    <Content Include="Views\CabYaari\Home.cshtml" />
    <Content Include="Views\CabYaari\Services.cshtml" />
    <Content Include="Views\UserManagement\MenuList.cshtml" />
    <Content Include="Views\Error\Not-Authorized.cshtml" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.5.1.min.map" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Content\fonts\" />
    <Folder Include="Docs\CabYaariServices\" />
    <Folder Include="Docs\CarCategory\" />
    <Folder Include="Docs\CarDocs\" />
    <Folder Include="Docs\CarOwnerPhoto\" />
    <Folder Include="Docs\DiscountCouponManager\" />
    <Folder Include="Docs\DriverDocs\" />
    <Folder Include="Docs\DriverPhoto\" />
    <Folder Include="Docs\MostFavouriteCityImg\" />
    <Folder Include="Docs\UserPhoto\" />
    <Folder Include="Docs\VendorDocs\" />
    <Folder Include="Docs\VendorPhoto\" />
    <Folder Include="Models\" />
    <Folder Include="Views\MapMyIndia\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.Lib\Common.Lib.csproj">
      <Project>{2AC03764-E710-4585-9754-352A4F69D90B}</Project>
      <Name>Common.Lib</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBLinker.Lib\DBLinker.Lib.csproj">
      <Project>{839A2F95-B7C8-40F1-ACA8-3DE4E7D5E340}</Project>
      <Name>DBLinker.Lib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Report\rpt_BookingInvoiceReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Report\rpt_BookingReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>64506</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:64506/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>
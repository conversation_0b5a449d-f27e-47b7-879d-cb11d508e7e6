﻿using AdminApp.Mapper;
using AdminApp.Services;
using Common.Lib;
using Common.Lib.Impl;
using DBLinker.Lib;
using DBLinker.Lib.Repository;
using DBLinker.Lib.Repository.Impl;
using System.Web.Mvc;
using Unity;
using Unity.Mvc5;


namespace AdminApp
{
    public static class UnityConfig
    {

        public static void RegisterComponents()
        {
            var container = new UnityContainer();

            // register all your components with the container here
            // it is NOT necessary to register your controllers

            // e.g. container.RegisterType<ITestService, TestService>();
            container.RegisterType<IGenericRepository<RLT_ADMIN_USER>, GenericRepository<RLT_ADMIN_USER>>();
            container.RegisterType<IGenericRepository<RLT_COUNTRY>, GenericRepository<RLT_COUNTRY>>();
            container.RegisterType<IGenericRepository<RLT_STATE>, GenericRepository<RLT_STATE>>();
            container.RegisterType<IGenericRepository<RLT_CITY>, GenericRepository<RLT_CITY>>();
            container.RegisterType<IGenericRepository<RLT_BANK_NAMES>, GenericRepository<RLT_BANK_NAMES>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING_PAYMENT_DETAILS>, GenericRepository<RLT_BOOKING_PAYMENT_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_CAR_CATEGORY>, GenericRepository<RLT_CAR_CATEGORY>>();
            container.RegisterType<IGenericRepository<RLT_CAR_CHARGES_FECILITIES_DETAILS>, GenericRepository<RLT_CAR_CHARGES_FECILITIES_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_CAR_COMPANY>, GenericRepository<RLT_CAR_COMPANY>>();
            container.RegisterType<IGenericRepository<RLT_CAR_DETAILS>, GenericRepository<RLT_CAR_DETAILS>>();

            container.RegisterType<IGenericRepository<RLT_CAR_DRIVER_DETAILS>, GenericRepository<RLT_CAR_DRIVER_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_CAR_FUEL_TYPES>, GenericRepository<RLT_CAR_FUEL_TYPES>>();
            container.RegisterType<IGenericRepository<RLT_CAR_MODEL>, GenericRepository<RLT_CAR_MODEL>>();
            container.RegisterType<IGenericRepository<RLT_CAR_OWNER_BANK_DETAILS>, GenericRepository<RLT_CAR_OWNER_BANK_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_LOCATION_CODE>, GenericRepository<RLT_LOCATION_CODE>>();
            container.RegisterType<IGenericRepository<RLT_LOG>, GenericRepository<RLT_LOG>>();
            container.RegisterType<IGenericRepository<RLT_MENU_MASTER>, GenericRepository<RLT_MENU_MASTER>>();
            container.RegisterType<IGenericRepository<RLT_PAYMENT_METHOD>, GenericRepository<RLT_PAYMENT_METHOD>>();


            container.RegisterType<IGenericRepository<RLT_ROUTES_DETAILS>, GenericRepository<RLT_ROUTES_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_TRIP_TYPES>, GenericRepository<RLT_TRIP_TYPES>>();
            container.RegisterType<IGenericRepository<RLT_USER_LOGIN_DETAILS>, GenericRepository<RLT_USER_LOGIN_DETAILS>>();


            container.RegisterType<IGenericRepository<RLT_DRIVER_ENQUIRIES>, GenericRepository<RLT_DRIVER_ENQUIRIES>>();
            container.RegisterType<IGenericRepository<RLT_AboutUs>, GenericRepository<RLT_AboutUs>>();
            container.RegisterType<IGenericRepository<RLT_FAQ_Details>, GenericRepository<RLT_FAQ_Details>>();
            container.RegisterType<IGenericRepository<RLT_ContactInformation>, GenericRepository<RLT_ContactInformation>>();
            container.RegisterType<IGenericRepository<RLT_DRIVER_APPROVE_STATUS>, GenericRepository<RLT_DRIVER_APPROVE_STATUS>>();
            container.RegisterType<IGenericRepository<RLT_PrivacyPolicy>, GenericRepository<RLT_PrivacyPolicy>>();
            container.RegisterType<IGenericRepository<RLT_TermsConditions>, GenericRepository<RLT_TermsConditions>>();
            container.RegisterType<IGenericRepository<RLT_UserFeedBack>, GenericRepository<RLT_UserFeedBack>>();
            container.RegisterType<IGenericRepository<RLT_Services>, GenericRepository<RLT_Services>>();
            container.RegisterType<IGenericRepository<RLT_SUBSCRIBERS>, GenericRepository<RLT_SUBSCRIBERS>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING_STATUS>, GenericRepository<RLT_BOOKING_STATUS>>();
            container.RegisterType<ITwoFactorAuth, TwoFactorAuth>();
            container.RegisterType<IResizeImage, ResizeImage>();
            container.RegisterType<IGenericRepository<RLT_Vendor_Details>, GenericRepository<RLT_Vendor_Details>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING_MODE>, GenericRepository<RLT_BOOKING_MODE>>();
            container.RegisterType<IGenericRepository<RLT_Vendor_Docs>, GenericRepository<RLT_Vendor_Docs>>();
            container.RegisterType<IGenericRepository<RLT_DOCUMNETS_NAME>, GenericRepository<RLT_DOCUMNETS_NAME>>();
            container.RegisterType<IGenericRepository<ObjectMapper>, GenericRepository<ObjectMapper>>();
            container.RegisterType<IGenericRepository<RLT_VENDOR_CAR_DETAILS>, GenericRepository<RLT_VENDOR_CAR_DETAILS>>();
            container.RegisterType<IGenericRepository<Vendor_Car_Driver_Docs>, GenericRepository<Vendor_Car_Driver_Docs>>();
            container.RegisterType<IGenericRepository<Vendor_Car_Driver_Docs>, GenericRepository<Vendor_Car_Driver_Docs>>();
            container.RegisterType<IGenericRepository<RLT_Vendor_Bank_Details>, GenericRepository<RLT_Vendor_Bank_Details>>();
            container.RegisterType<IGenericRepository<TempBooking>, GenericRepository<TempBooking>>();
            container.RegisterType<IGenericRepository<RLT_DISCOUNT_COUPON>, GenericRepository<RLT_DISCOUNT_COUPON>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING_RULES>, GenericRepository<RLT_BOOKING_RULES>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING_FARE>, GenericRepository<RLT_BOOKING_FARE>>();

            container.RegisterType<IGenericRepository<RLT_CAR_OWNER_DETAILS>, GenericRepository<RLT_CAR_OWNER_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_CAR_DRIVER_DETAILS>, GenericRepository<RLT_CAR_DRIVER_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_CAR_DETAILS>, GenericRepository<RLT_CAR_DETAILS>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING>, GenericRepository<RLT_BOOKING>>();
            container.RegisterType<IGenericRepository<RLT_BOOKING>, GenericRepository<RLT_BOOKING>>();
            container.RegisterType<ISMSService, SMSService>();
            DependencyResolver.SetResolver(new UnityDependencyResolver(container));

        }
    }
}

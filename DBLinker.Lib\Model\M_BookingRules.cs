﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_BookingRules
    {  
        public int PKID { get; set; }
        public Nullable<int> Trip_Type_Id { get; set; }
        public string Trip_Type { get; set; }
        public Nullable<int> Car_Category_Id { get; set; }
        public string Car_Category { get; set; }
        public Nullable<int> Distance_From { get; set; }
        public Nullable<int> Distance_To { get; set; }
        public Nullable<int> NewDistance { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Updated_By { get; set; }

    }
}

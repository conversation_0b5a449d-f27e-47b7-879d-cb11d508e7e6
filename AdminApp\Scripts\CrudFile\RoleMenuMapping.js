﻿$('.selectDropdown').select2();
$(document).ready(function () {
    $("#RoleMenuMapping").parent().parent().find("a").next().slideToggle(500);
    $("#RoleMenuMapping").parent().parent().toggleClass('toggled');
    $("#RoleMenuMapping").find("a").addClass("selectedMenu");
    $("#Role_Id").change(function () {
        LoadGrid();
    })
});
function LoadGrid() {
    $.ajax(
        {
            url: '_RoleMenuMapping?RoleId=' + $("#Role_Id").val(),
            type: 'GET',
            data: "",
            contentType: 'application/json; charset=utf-8',
            success: function (data) {
                $("#partialDiv").html(data);
                onLoadCheckingAllCheckbox();
            }
        });
}
//Rajpal js
function searchInputTable(ele) {
    var value = ele.value.trim().toLowerCase();
    $("#ManuListTable tr").filter(function () {
        $(this).toggle($(this).text().trim().toLowerCase().indexOf(value) > -1)
    });
    var visibleRows = $('#ManuListTable tr:visible').length;
    var totalRows = $('#ManuListTable tr').length;
    (visibleRows < totalRows)
        ? $("#serchCount").html("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + visibleRows + " Result found")
        : $("#serchCount").html('');

    filterTable(value, "ManuListTable");
    $('#ManuListTable').html(function (i, html) {
        return html.replace(/amp;/g, '');
    });
}
function checkUncheckView(ele) {
    $("#ManuListTable tr td:nth-child(3)").find('input:checkbox').prop('checked', ele.checked)
}
function checkUncheckAdd(ele) {
    $("#ManuListTable tr td:nth-child(4)").find('input:checkbox').prop('checked', ele.checked)
}
function checkUncheckEdit(ele) {
    $("#ManuListTable tr td:nth-child(5)").find('input:checkbox').prop('checked', ele.checked)
}
function checkUncheckDelete(ele) {
    $("#ManuListTable tr td:nth-child(6)").find('input:checkbox').prop('checked', ele.checked)
}
$(document).on("change", ".isViewCheckbox", function () {
    var count = $("#ManuListTable tr td:nth-child(3)").find('input:checkbox:not(:checked)');
    if (count.length == 0) $("input[type='checkbox'][name='Is-View-Checkbok']").prop('checked', true);
    else $("input[type='checkbox'][name='Is-View-Checkbok']").prop('checked', false);
});
$(document).on("change", ".isAddCheckbox", function () {
    var count = $("#ManuListTable tr td:nth-child(4)").find('input:checkbox:not(:checked)');
    if (count.length == 0) $("input[type='checkbox'][name='Is-Add-Checkbok']").prop('checked', true);
    else $("input[type='checkbox'][name='Is-Add-Checkbok']").prop('checked', false);
});
$(document).on("change", ".isEditCheckbox", function () {
    var count = $("#ManuListTable tr td:nth-child(5)").find('input:checkbox:not(:checked)');
    if (count.length == 0) $("input[type='checkbox'][name='Is-Edit-Checkbok']").prop('checked', true);
    else $("input[type='checkbox'][name='Is-Edit-Checkbok']").prop('checked', false);
});
$(document).on("change", ".isDeleteCheckbox", function () {
    var count = $("#ManuListTable tr td:nth-child(6)").find('input:checkbox:not(:checked)');
    if (count.length == 0) $("input[type='checkbox'][name='Is-Delete-Checkbok']").prop('checked', true);
    else $("input[type='checkbox'][name='Is-Delete-Checkbok']").prop('checked', false);
});
$(document).on("click", ".table-parent-icon", function () {
    var index = $(this).attr("data-order") + ".";
    var rows = $("#ManuListTable  td:nth-child(7):contains(" + index + ")").closest("tr");
    var bodyInfo = "";
    for (var i = 0; i < rows.length; i++) {
        bodyInfo += "<tr>" + rows[i].innerHTML + "</tr>";
    }
    $("#modal-total-info").html(rows.length + " Child Found!")
    $("#model-body-info").html(bodyInfo);
});
$(document).on("click", ".table-child-icon", function () {
    var index = $(this).attr("data-order");
    var rows = $("td:nth-child(7)").filter(function () {
        return $(this).text() == index;
    }).closest("tr");
    var bodyInfo = "<tr>" + rows[0].innerHTML + "</tr>";
    $("#modal-total-info").html(" Parent Info !")
    $("#model-body-info").html(bodyInfo);
});
function onLoadCheckingAllCheckbox() {
    for (var i = 3; i < 7; i++) {
        var count = $("#ManuListTable tr td:nth-child(" + i + ")").find('input:checkbox:not(:checked)');
        if (i == 3) {
            if (count.length == 0) $("input[type='checkbox'][name='Is-View-Checkbok']").prop('checked', true);
            else $("input[type='checkbox'][name='Is-View-Checkbok']").prop('checked', false);
        }
        if (i == 4) {
            if (count.length == 0) $("input[type='checkbox'][name='Is-Add-Checkbok']").prop('checked', true);
            else $("input[type='checkbox'][name='Is-Add-Checkbok']").prop('checked', false);
        }
        if (i == 5) {
            if (count.length == 0) $("input[type='checkbox'][name='Is-Edit-Checkbok']").prop('checked', true);
            else $("input[type='checkbox'][name='Is-Edit-Checkbok']").prop('checked', false);
        }
        if (i == 6) {
            if (count.length == 0) $("input[type='checkbox'][name='Is-Delete-Checkbok']").prop('checked', true);
            else $("input[type='checkbox'][name='Is-Delete-Checkbok']").prop('checked', false);
        }
    }
}
function filterTable(Stxt, table) {
    dehighlight(document.getElementById(table));
    if (Stxt.length > 0)
        highlight(Stxt.toLowerCase(), document.getElementById(table));
}
function dehighlight(container) {
    for (var i = 0; i < container.childNodes.length; i++) {
        var node = container.childNodes[i];
        if (node.attributes && node.attributes['class']
            && node.attributes['class'].value == 'highlighted') {
            node.parentNode.parentNode.replaceChild(
                document.createTextNode(
                    node.parentNode.innerHTML.replace(/<[^>]+>/g, "")),
                node.parentNode);
            // Stop here and process next parent
            return;
        } else if (node.nodeType != 3) {
            // Keep going onto other elements
            dehighlight(node);
        }
    }
}
/*
* Create a
* <span>preText <span class="highlighted">Stxt</span> postText</span>
* around each search Stxt
*/
function highlight(Stxt, container) {
    for (var i = 0; i < container.childNodes.length; i++) {
        var node = container.childNodes[i];
        if (node.nodeType == 3) {
            // Text node
            var data = node.data;
            var data_low = data.toLowerCase();
            if (data_low.indexOf(Stxt) >= 0) {
                //Stxt found!
                var new_node = document.createElement('span');
                node.parentNode.replaceChild(new_node, node);
                var result;
                while ((result = data_low.indexOf(Stxt)) != -1) {
                    new_node.appendChild(document.createTextNode(
                        data.substr(0, result)));
                    new_node.appendChild(create_node(
                        document.createTextNode(data.substr(
                            result, Stxt.length))));
                    data = data.substr(result + Stxt.length);
                    data_low = data_low.substr(result + Stxt.length);
                }
                new_node.appendChild(document.createTextNode(data));
            }
        } else {
            // Keep going onto other elements
            highlight(Stxt, node);
        }
    }
}
function create_node(child) {
    var node = document.createElement('span');
    node.setAttribute('class', 'highlighted');
    node.attributes['class'].value = 'highlighted';
    node.appendChild(child);
    return node;
}
//End Rajpal js
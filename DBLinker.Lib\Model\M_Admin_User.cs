﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Admin_User
    {
        public int PKID { get; set; }
        public Nullable<int> RoleId { get; set; }
        public string RoleName { get; set; }
        public string UserName { get; set; }
        public string UserPWD { get; set; }
        public string UserPhoto { get; set; }
        public string UserFirstName { get; set; }
        public string UserMiddleName { get; set; }
        public string UserLastName { get; set; }
        public string UserEmailID { get; set; }
        public string UserMobileNo { get; set; }
        public Nullable<int> VendorId { get; set; }
        public Nullable<int> CarOwnerId { get; set; }
        public Nullable<int> EmployeeId { get; set; }
        public Nullable<int> DepartmentId { get; set; }
        public string AadharId { get; set; }
        public string Address { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<System.Guid> CreatedBy { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public Nullable<int> IsAdmin { get; set; }
        public Nullable<bool> IsDeleted { get; set; }
        public string Description { get; set; }
        public Nullable<bool> Is2TfaAuthentication { get; set; }

    }
}

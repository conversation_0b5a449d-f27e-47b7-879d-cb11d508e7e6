﻿$(document).ready(function () {

    $("#VendorManager").parent().parent().find("a").next().slideToggle(500);
    $("#VendorManager").parent().parent().toggleClass('toggled');
    $("#VendorManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
   

    $("#btnAdd").click(function ()
    {        
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

        $("#Vendor_Ref_PKID").val($("#Vendor_Ref_PKID_" + Id).val());
       
    });


    $("#btnSave").click(function ()
    {
        if (IsValidate())
        {

            $.ajax({
                url: 'AddVendorBankDetails',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Vendor Bank Informations Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function ()
    {
        if (IsValidate()) {

            $.ajax({
                url: 'AddVendorBankDetails',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Vendor Bank Informations Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

   
    
});

function Edit(Id)
{   
    showLoader();

    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

  

    $("#Bank_Name_PKID").val($("#Bank_Name_PKID_" + Id).val());
    $("#Account_Number").val($("#Account_Number_" + Id).val());
    $("#IFSC_CODE").val($("#IFSC_CODE_" + Id).val());
    $("#Account_Holder_Name").val($("#Account_Holder_Name_" + Id).val());

    
    if($("#Bank_Status_" + Id).val()=="True")
        $("#Bank_Status1").prop("checked", true);
    else
        $("#Bank_Status2").prop("checked", true);
   
    $("#Is_active").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
    setTimeout(function () {
        hideLoader();
    }, 1000);
}

function Delete(Id)
{

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    

    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_active").val(true);
    }
    else {
        $("#Is_active").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'AddVendorBankDetails',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Vendor Bank Informations Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id)
{
    $("#PKID").val('');
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_active").val(true);
    }
    else {
        $("#Is_active").val(false);
    }

    $("#hdnOperation").val('D');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'AddVendorBankDetails',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Vendor Bank Informations Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    $("#Bank_Name_PKID").val('');
    $("#Account_Number").val('');
    $("#IFSC_CODE").val('');
    $("#Account_Holder_Name").val('');
   

}

function IsValidate()
{
    if ($("#Bank_Name_PKID").val() == "")
    {
        toastr.clear();
        toastr.error("Please select Bank Name");
        return false;
    }
    if ($("#Account_Number").val() == "") {
        toastr.clear();
        toastr.error("Please enter Account Number");
        return false;
    }
    /*Repeat Validation*/
    if ($("#Account_Number").val() != "") {
        if ($("#Account_Number-error").html() !== undefined) {
            toastr.clear();
            toastr.error("Account Number already exists!", "Required!");
            return false;
        }
    }
    if ($("#IFSC_CODE").val() == "") {
        toastr.clear();
        toastr.error("Please enter IFSC CODE");
        return false;
    }
    if ($("#Account_Holder_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Account Holder Name");
        return false;
    }

    return true;
}







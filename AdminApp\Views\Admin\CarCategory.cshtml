﻿@model DBLinker.Lib.RLT_CAR_CATEGORY
@{
    ViewBag.Title = "Car Category";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Car Category</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Car Category </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Car Category</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("CarCategory", "Admin", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormCarCategory" }))
        {
            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Car Category</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Car_Category_Name, new { @class = "mdl-textfield__input", @maxlength = "55" })
                                    <label class="mdl-textfield__label">Car Category Name</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Car_Category_Abbr, new { @class = "mdl-textfield__input", @maxlength = "55" })
                                    <label class="mdl-textfield__label">Car Category Abbr</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Per_KM_fare, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label">Per KM fare</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.Capacity, new { @class = "mdl-textfield__input Inttxtbx", @maxlength = "2" })
                                    <label class="mdl-textfield__label">Capacity</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    <br />
                                    <img src="~/Content/img/no-user-image.gif" id="Car_Categroy_Image_viwer" height="80" width="100" />
                                    <label class="mdl-textfield__label"> car category image.</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    <input type="file" class="mdl-textfield__input" id="CarCategoryImage" name="CarCategoryImage" accept="image/png, image/jpeg" style="height: 60px !important;border: 1px dashed rgba(0, 0, 0, 0.3);"> 
                                    @Html.HiddenFor(x => x.Car_Categroy_Image)
                                    <label class="mdl-textfield__label"> Upload car category image.</label>
                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    @Html.TextBoxFor(x => x.Features, new { @class = "mdl-textfield__input", Style = "color:black;", @onblur = "isTextSpecialValid(this);", @maxlength = "300" })
                                    <label class="mdl-textfield__label"> Features</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20 text-center">


                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Car Category List</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Car Category</th>
                                        <th>Car Category Abbr</th>
                                        <th>Per KM fare</th>
                                        <th>Capacity</th>
                                        <th>Features</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (ViewBag.carCategortyRepo != null)
                                    {
                                        foreach (var item in ViewBag.carCategortyRepo)
                                        {
                                            <tr class="odd gradeX">
                                                <td width="25%">
                                                    @if (!string.IsNullOrEmpty(@item.Car_Categroy_Image))
                                                    {
                                                        <img src="~/Docs/CarCategory/@item.Car_Categroy_Image" id="categoryImage" height="60" width="60" />
                                                    }
                                                    else
                                                    {
                                                        <img src="~/Content/img/no-user-image.gif" id="VendorImage" height="60" width="60" />
                                                    }
                                                </td>
                                                <td width="25%">@item.Car_Category_Name</td>
                                                <td width="20%">@item.Car_Category_Abbr</td>
                                                <td width="10%">@item.Per_KM_fare</td>
                                                <td width="10%">@item.Capacity</td>
                                                <td width="25%">@item.Features</td>
                                                <td width="10%">
                                                    @{
                                                        if (item.Is_Active != null && item.Is_Active != false)
                                                        {
                                                            <span class="label label-sm label-success"> Active </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="label label-sm label-danger"> InActive </span>
                                                        }
                                                    }
                                                </td>
                                                <td class="valigntop" width="10%">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                <a onclick="ActivateDeactivate(@item.PKID)" title="Enable/Disable">
                                                                    <i class="material-icons">check</i> Enable/Disable
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a onclick="Edit(@item.PKID)" title="Edit">
                                                                    <i class="material-icons">mode_edit</i> Edit
                                                                </a>

                                                            </li>

                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/CrudFile/CarCategoryCrudJs.js"></script>


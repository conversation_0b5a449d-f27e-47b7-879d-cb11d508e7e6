﻿@model DBLinker.Lib.Model.M_Employee
@using AdminApp.Mapper;
@{
    ViewBag.Title = "Employee Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Employee Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Employee List</li>
                </ol>
            </div>
        </div>




        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Employee</header>

                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="../UserManagement/Employee" class="btn btn-info">
                                        Add New Employee<i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th width="10%">Photo</th>
                                        <th width="18%">Name</th>
                                        <th width="13%">DOB</th>
                                        <th width="10%">Mobile</th>
                                        <th width="13%">Employee ID</th>
                                        <th width="10%">Designation</th>
                                        <th width="8%">Status</th>
                                        <th width="12%" style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.EmployeeList != null)
                                    {
                                        foreach (var item in ViewBag.EmployeeList)
                                        {

                                            <tr class="odd gradeX">
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />
                                                <td>
                                                    @if (!string.IsNullOrEmpty(@item.Photo))
                                                    {
                                                        <img src="/Docs/EmployeePhoto/@item.Photo" id="EmployeePhoto" height="60" width="60" />
                                                    }
                                                    else
                                                    {
                                                        <img src="~/Content/img/no-user-image.gif" id="EmployeePhoto" height="60" width="60" />
                                                    }
                                                </td>
                                                <td>@item.Employee_Name</td>
                                                <td>
                                                    @if (@item.DOB != null)
                                                    {
                                                        @item.DOB.ToShortDateString()
                                                    }
                                                </td>
                                                <td>@item.Phone_1</td>
                                                <td>@item.Employee_ID</td>
                                                <td>@item.Designation</td>
                                                <td>
                                                    @{
                                                        if (item.Is_Active != null && item.Is_Active != false)
                                                        {
                                                            <span class="label label-sm label-success"> Active </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="label label-sm label-danger"> InActive </span>
                                                        }
                                                    }
                                                </td>

                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">

                                                            <li>
                                                                <a onclick="ActivateDeactivate(@item.PKID)" title="Enable/Disable">
                                                                    <i class="material-icons">check</i> Enable/Disable
                                                                </a>
                                                            </li>
                                                            <li>

                                                                <a href="../UserManagement/Employee?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Employee")" title="Edit Employee Info">
                                                                    <i class="material-icons">mode_edit</i>
                                                                    Edit
                                                                </a>
                                                            </li>

                                                            <li>
                                                                <a href="../UserManagement/EmployeeView?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Employee")" title="View Employee Info">
                                                                    <i class="material-icons">remove_red_eye</i>
                                                                    View
                                                                </a>
                                                            </li>


                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>


<script src="~/Scripts/CrudFile/EmployeeList.js"></script>

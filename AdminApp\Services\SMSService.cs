﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Text.RegularExpressions;

namespace AdminApp.Services
{
    public interface ISMSService
    {
        void SendSMS(SMSModel model);
    }
    public class SMSService : ISMSService
    {
        private readonly string _baseUrl;
        private readonly string _authKey;
        private readonly string _senderId;
        private readonly string _routeId;
        private readonly string _tmid;
        private readonly string _consentId;
        private readonly string _contentType;

        public SMSService()
        {
            _baseUrl = ConfigurationManager.AppSettings["urlSms.BaseUrl"];
            _authKey = ConfigurationManager.AppSettings["urlSms.AuthKey"];
            _senderId = ConfigurationManager.AppSettings["urlSms.SenderId"];
            _routeId = ConfigurationManager.AppSettings["urlSms.RouteId"];
            _tmid = ConfigurationManager.AppSettings["urlSms.Tmid"];
            _consentId = ConfigurationManager.AppSettings["urlSms.ConsentFailoverId"];
            _contentType = ConfigurationManager.AppSettings["urlSms.ContentType"];
        }

        public void SendSMS(SMSModel model)
        {
            string templateType = model.TemplateType.ToString();
            string template = SmsTemplateHelper.GetTemplateMessage(templateType);
            string templateId = SmsTemplateHelper.GetTemplateId(templateType);

            if (string.IsNullOrWhiteSpace(template))
                throw new Exception($"SMS template not found: {templateType}");

            string message = GenerateMessageFromTemplate(template, model.MessageVariables);

            string fullUrl = $"{_baseUrl}?AUTH_KEY={_authKey}" +
                             $"&senderId={_senderId}" +
                             $"&routeId={_routeId}" +
                             $"&mobileNos={model.MobileNumber}" +
                             $"&message={Uri.EscapeDataString(message)}" +
                             $"&smsContentType={_contentType}" +
                             $"&tmid={_tmid}" +
                             $"&concentFailoverId={_consentId}" +
                             $"&templateid={templateId}";

            using (var client = new WebClient())
            {
                client.DownloadString(fullUrl);
            }
        }

        private string GenerateMessageFromTemplate(string template, List<string> variables)
        {
            foreach (var value in variables)
            {
                int index = template.IndexOf("{#var#}", StringComparison.OrdinalIgnoreCase);
                if (index >= 0)
                {
                    template = template.Substring(0, index) + value + template.Substring(index + "{#var#}".Length);
                }
            } 
            return template;
        }
    }

    public static class SmsTemplateHelper
    {
        public static string GetTemplateMessage(string type)
        {
            return ConfigurationManager.AppSettings[$"urlSms.Template.{type}.Message"];
        }

        public static string GetTemplateId(string type)
        {
            return ConfigurationManager.AppSettings[$"urlSms.Template.{type}.TemplateId"];
        }
    }



    public class SMSModel
    {
        public string MobileNumber { get; set; }
        public List<string> MessageVariables { get; set; } // Replacing vars
        public SMSTemplateType TemplateType { get; set; }
    }

    public enum SMSTemplateType
    {
        NewBooking,
        CancelBooking,
        CompleteBooking,
        CustomerBookingConfirmed,
        DriverBookingConfirmed,
        AdminCancel,
        RideComplete,
        PaymentLink,
    }



}

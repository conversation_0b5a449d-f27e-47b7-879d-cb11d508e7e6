﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Vendor_Details
    {
        public int PKID { get; set; }
        [Required(ErrorMessage = "Please enter vendor name !")]
        public string Vendor_Name { get; set; }


        [Remote("Check_Vendor_Company_Name", "Common", ErrorMessage = "Vendor Company Name already exists!")]
        public string Vendor_Company_Name { get; set; }
        [Remote("Check_Vendor_MemberID", "Common", ErrorMessage = "Vendor Member ID already exists!")]

        public string Vendor_Member_ID { get; set; }

        [Required(ErrorMessage = "Please enter vendor name !")]
        public string Vendor_EmailId { get; set; }

        [Required(ErrorMessage = "Please enter vendor name !")]
        public string Vendor_Phone1 { get; set; }

        [Required(ErrorMessage = "Please enter vendor name !")]
        public string Vendor_Phone2 { get; set; }
        [Required(ErrorMessage = "Please enter vendor company address !")]
        public string Vendor_Company_Address { get; set; }
        public bool Is_Active { get; set; }
        public DateTime Created_Date { get; set; }

        public DateTime Updated_Date { get; set; }

        public int Last_Modified_By { get; set; }

        public bool Is_2FA_Activated { get; set; }
        [Required(ErrorMessage = "Please enter password !")]
        public string Vendor_PWD { get; set; }
        public string Vendor_Photo { get; set; }
        public string Vendor_Photo_EditFile { get; set; }
        
        public HttpPostedFileBase Vendor_Photo_UP { get; set; }
        public string Phone1_IsWhatsup { get; set; }

        public List<M_Vendor_Docs> M_Vendor_DocsList { get; set; }
        public List<M_Vendor_Bank_Details> M_Vendor_BankList { get; set; }

    }
}

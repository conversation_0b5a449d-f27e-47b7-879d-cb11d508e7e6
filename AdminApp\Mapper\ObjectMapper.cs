﻿using DBLinker.Lib;
using DBLinker.Lib.Model;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;

namespace AdminApp.Mapper
{
    public class ObjectMapper
    {

        public RLT_Vendor_Details VendorDetails(M_Vendor_Details obj2)
        {
            RLT_Vendor_Details obj1 = new RLT_Vendor_Details();
            obj1.Vendor_Owner_Name = obj2.Vendor_Name.Trim();
            obj1.Vendor_Company_Name = obj2.Vendor_Company_Name.Trim();
            obj1.Vendor_EmailID = obj2.Vendor_EmailId.Trim();
            obj1.Vendor_Member_Id = obj2.Vendor_Member_ID;
            obj1.Vendor_Pwd = obj2.Vendor_PWD;
            obj1.Is_2FA_Activated = obj2.Is_2FA_Activated;
            obj1.Is_Active = obj2.Is_Active;
            obj1.PKID = obj2.PKID;
            obj1.Vendor_Address = obj2.Vendor_Company_Address.Trim();
            obj1.Vendor_Phone1 = obj2.Vendor_Phone1.Trim();
            obj1.Phone1_IsWhatsup = obj2.Phone1_IsWhatsup;
            if (!string.IsNullOrEmpty(obj2.Vendor_Phone2))
                obj1.Vendor_Phone2 = obj2.Vendor_Phone2.Trim();
            obj1.Vendor_Photo = obj2.Vendor_Photo;
            
            return obj1;

        }

        public RLT_Vendor_Docs VendorDocs(M_Vendor_Docs obj2)
        {
            RLT_Vendor_Docs obj1 = new RLT_Vendor_Docs();
            obj1.PKID = obj2.PKID;
            obj1.Vendor_Doc_Id = obj2.Vendor_Doc_Id;
            obj1.Document_Path = obj2.Vendor_Doc_Path;
            obj1.Vendor_Ref_PKID = obj2.Vendor_Ref_PKID;
            obj1.Comments = obj2.Comments;
            obj1.Is_Verified = obj2.Is_Verified;
            obj1.Last_Modified_By = obj2.Last_Modified_By;
            obj1.Last_Modified_Date = obj2.Modified_Date;
            obj1.Last_Modified_By = obj2.Last_Modified_By;
            return obj1;

        }

        public RLT_DOCUMNETS_NAME DocsName(M_Document_Name obj2)
        {
            RLT_DOCUMNETS_NAME obj1 = new RLT_DOCUMNETS_NAME();
            obj1.PKID = obj2.PKID;
            obj1.Doc_For = obj2.Doc_For;
            obj1.Doc_Name = obj2.Doc_Name;
            obj1.Is_Doc_Req = obj2.Is_Doc_Req;
            obj1.Is_Doc_Expiry_Date = obj2.Is_Doc_Expiry_Date;
            obj1.Is_Active = obj2.Is_Active;
            obj1.Created_Date = obj2.Created_Date;
            obj1.Updated_Date = obj2.Updated_Date;
            return obj1;

        }
        public RLT_CAR_OWNER_DETAILS CarOwnerDetails(M_CarOwner obj2)
        {
            RLT_CAR_OWNER_DETAILS obj1 = new RLT_CAR_OWNER_DETAILS();
            obj1.Vendor_PKID = obj2.Vendor_PKID;
            obj1.Car_Owner_Name = obj2.Car_Owner_Name;
            obj1.Car_Owner_Email = obj2.Car_Owner_Email;
            obj1.Car_Owner_Phone1 = obj2.Car_Owner_Phone1;
            obj1.Car_Owner_Photo = obj2.Car_Owner_Photo;
            obj1.Phone1_IsWhatsup = obj2.Phone1_IsWhatsup != null ? obj2.Phone1_IsWhatsup.Trim() : "No";
            obj1.Car_Owner_Phone2 = obj2.Car_Owner_Phone2;
            obj1.Car_Owner_DL = obj2.Car_Owner_DL;
            obj1.Is_Active = obj2.Is_Active;
            obj1.PKID = obj2.PKID;
            obj1.Car_Owner_Address = obj2.Car_Owner_Address;
            obj1.Car_Owner_Aadhaar = obj2.Car_Owner_Aadhaar;
            return obj1;

        }
        public RLT_CAR_DRIVER_DETAILS CarDriverDetails(M_CarDriver_Details obj2)
        {
            RLT_CAR_DRIVER_DETAILS obj1 = new RLT_CAR_DRIVER_DETAILS();
            obj1.Vendor_PKID = obj2.Vendor_PKID;
            obj1.Car_Owner_PKID = obj2.Car_Owner_PKID;
            obj1.Car_PKID = obj2.Car_PKID;
            obj1.Driver_Name = obj2.Driver_Name.Trim();
            obj1.Driver_Photo = obj2.Driver_Photo;
            obj1.Driver_DOB = obj2.Driver_DOB;
            //if(!string.IsNullOrEmpty(obj2.Driver_DOB_String))
            //obj1.Driver_DOB = Convert.ToDateTime(obj2.Driver_DOB_String, getCulture);
            obj1.Drive_Phone_1 = obj2.Drive_Phone_1.Trim();
            obj1.Drive_Phone_2 = obj2.Drive_Phone_2 == null ? "" : obj2.Drive_Phone_2.Trim();
            obj1.Driver_DL = obj2.Driver_DL.Trim();
            obj1.Is_Active = obj2.Is_Active;
            obj1.PKID = obj2.PKID;
            obj1.Driver_Aadhaar = obj2.Driver_Aadhaar.Trim();
            obj1.Driver_Father_Name = obj2.Driver_Father_Name.Trim();
            obj1.Driver_Mother_Name = obj2.Driver_Mother_Name.Trim();
            obj1.Driver_Marrital_Status = obj2.Driver_Marrital_Status;
            obj1.Driver_Gender = obj2.Driver_Gender.Trim();
            obj1.Driver_Smoking_Status = obj2.Driver_Smoking_Status;
            obj1.Driver_Drinking_Status = obj2.Driver_Drinking_Status;
            obj1.Driver_Smoking_Status = obj2.Driver_Smoking_Status;
            obj1.Driver_Eating_Type = obj2.Driver_Eating_Type;
            obj1.Driver_Religion = obj2.Driver_Religion;

            obj1.Driver_Current_Address1 = obj2.Driver_Current_Address1.Trim();
            obj1.Driver_Current_Address2 = obj2.Driver_Current_Address2.Trim();
            obj1.Driver_Current_Address_City = obj2.Driver_Current_Address_City;
            obj1.Driver_Current_Address_State = obj2.Driver_Current_Address_State;

            obj1.Driver_Permanent_Address1 = obj2.Driver_Permanent_Address1.Trim();
            obj1.Driver_Permanent_Address2 = obj2.Driver_Permanent_Address2.Trim();
            obj1.Driver_Permanent_Address_City = obj2.Driver_Permanent_Address_City;
            obj1.Driver_Permanent_Address_State = obj2.Driver_Permanent_Address_State;

            obj1.Phone_1_IsWhatsup = obj2.Phone_1_IsWhatsup == null ? "No" : obj2.Phone_1_IsWhatsup;
            obj1.Driver_Current_Permanent_Same = obj2.Driver_Current_Permanent_Same == null ? false : obj2.Driver_Current_Permanent_Same;
            obj1.Special_Remarking = obj2.Special_Remarking;
            obj1.Driver_Email = obj2.Driver_Email;
            return obj1;

        }
        public RLT_CAR_DETAILS CarDetails(M_Car_Details obj2)
        {
            RLT_CAR_DETAILS obj1 = new RLT_CAR_DETAILS();
            obj1.Vendor_PKID = obj2.Vendor_PKID;
            obj1.Car_Owner_PKID = obj2.Car_Owner_PKID;
            obj1.Car_Number = obj2.Car_Number != null ? obj2.Car_Number.Trim() : obj2.Car_Number;
            obj1.Car_Purchase_Year = obj2.Car_Purchase_Year != null ? obj2.Car_Purchase_Year.Trim() : obj2.Car_Purchase_Year;
            obj1.Car_Manufacturing_Year = obj2.Car_Manufacturing_Year != null 
                ? obj2.Car_Manufacturing_Year.Trim() : obj2.Car_Manufacturing_Year;
            obj1.Car_Company_PKID = obj2.Car_Company_PKID;
            obj1.Car_Model_PKID = obj2.Car_Model_PKID;
            obj1.Car_Registered_City_PKID = obj2.Car_Registered_City_PKID;
            obj1.Car_Fuel_Type_Status = obj2.Car_Fuel_Type_Status;
            obj1.Is_Active = obj2.Is_Active != null ? obj2.Is_Active : false;
            obj1.PKID = obj2.PKID;

            return obj1;

        }
        public RLT_BOOKING Booking(M_Booking obj2)
        {
            RLT_BOOKING obj1 = new RLT_BOOKING();
            obj1.Booking_Id = obj2.Booking_Id;
            obj1.City_From_Id = obj2.City_From_Id;
            obj1.City_To_Id = obj2.City_To_Id;
            obj1.Trip_Type_Id = obj2.Trip_Type_Id;
            obj1.Car_Category_Id = obj2.Car_Category_Id;

            obj1.Duration = obj2.Duration;
            obj1.Distance = obj2.Distance;
            obj1.Basic_Fare = obj2.Basic_Fare;
            obj1.GST = obj2.GST_Fare;
            obj1.GST_Fare = obj2.GST_Fare;
            obj1.Fare = obj2.Fare;

            obj1.Coupon_Code = obj2.Coupon_Code;
            obj1.Coupon_Discount = obj2.Coupon_Discount;

            obj1.PickUp_Address = obj2.PickUp_Address.Trim();
            obj1.DropOff_Address = obj2.DropOff_Address.Trim();
            obj1.PickUpAddressLatitude = obj2.PickUpAddressLatitude;
            obj1.PickUpAddressLongitude = obj2.PickUpAddressLongitude;
            obj1.Booking_Date = DateTime.Now;
            obj1.PickUp_Date = obj2.PickUp_Date;
            obj1.PickUp_Time = obj2.PickUp_Time;
            obj1.Name = obj2.Name.Trim();
            obj1.Mail_Id = obj2.Mail_Id.Trim();
            obj1.Mobile_No1 = obj2.Mobile_No1.Trim();
            obj1.Mobile_No2 = obj2.Mobile_No2;
            obj1.Mode_Of_Payment_Id = obj2.Mode_Of_Payment_Id;
            obj1.Booking_Status_Id = obj2.Booking_Status_Id;
            obj1.Vendor_Id = obj2.Vendor_PKID;
            obj1.Car_Id = obj2.Car_PKID;
            obj1.Is_Active = obj2.Is_Active;
            //obj1.PKID = obj2.PKID;
            obj1.BookingEditRemark = obj2.BookingEditRemark;
            obj1.Booking_Remark = obj2.Booking_Remark;
            obj1.driver_id = obj2.driver_id;
            obj1.completepickupaddress = obj2.completepickupaddress;
            obj1.completedropoffpaddress = obj2.completedropoffpaddress;
            obj1.Updated_Date = DateTime.Now;

            obj1.Booking_Created_By = obj2.Booking_Created_By;
            obj1.IsAdminBooked = obj2.IsAdminBooked;
            return obj1;

        }
        public IFormatProvider getCulture
        {
            get
            {
                IFormatProvider culture = new CultureInfo("en-GB", true);
                return culture;
            }
        }

    }
}
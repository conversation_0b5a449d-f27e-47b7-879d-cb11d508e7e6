﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DBLinker.Lib.Model
{
   public class M_BookingMode
    {

         public int PKID { get; set; }
        [Required(ErrorMessage = "Please  enter booking mode name !")]
        public string Booking_Mode_Name { get; set; }
    
        public bool Is_Active { get; set; }
        public DateTime Created_Date { get; set; }

        public DateTime Updated_Date { get; set; }

        public int Last_Modified_By { get; set; }
    }
}

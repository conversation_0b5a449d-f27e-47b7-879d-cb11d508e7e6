﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();

    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#btnSave").click(function ()
    {
        if (IsValidate())
        {
          
          
            $.ajax({
                url: 'TourTypeManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    toastr.success("Succesfully Saved");
                    document.location.reload();
                },
                failure: function (response) {
                    alert(response.responseText);
                },
                error: function (response) {
                    alert(response.responseText);
                }
            });

        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'TourTypeManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0)
                    {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });
});

function Edit(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#IsActive").val(true);

    $("#TourTypeName").val($("#name_" + Id).val());
    $("#AboutTourType").val($("#about_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();

}

function Delete(Id) {


    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#TourTypeName").val($("#name_" + Id).val());
    $("#AboutTourType").val($("#about_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#ID").val() != "") {

        $.ajax({
            url: 'TourTypeManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#TourTypeName").val($("#name_" + Id).val());
    $("#AboutTourType").val($("#about_" + Id).val());

    $("#IsActive").val($("#status_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);

    if ($("#ID").val() != "") {

        $.ajax({
            url: 'TourTypeManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}
function resetForm() {
  
    $("#TourTypeName").val('');
    $("#AboutTourType").val('');
   
}

function IsValidate() {
    if ($("#TourTypeName").val() == "") {
        toastr.clear();
        toastr.error("Please enter tour type !");
        return false;
    }
    if ($("#AboutTourType").val() == "") {
        toastr.clear();
        toastr.error("Please enter about tour type !");
        return false;
    }
   
  
    return true;
}
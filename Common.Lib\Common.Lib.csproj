﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2AC03764-E710-4585-9754-352A4F69D90B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Common.Lib</RootNamespace>
    <AssemblyName>Common.Lib</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Google.Authenticator, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GoogleAuthenticator.3.2.0\lib\netstandard2.0\Google.Authenticator.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="QRCoder, Version=1.6.0.0, Culture=neutral, PublicKeyToken=c4ed5b9ae8358a28, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.6.0\lib\net40\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EncryptionLibrary.cs" />
    <Compile Include="Impl\CommonMethod.cs" />
    <Compile Include="Impl\TwoFactorAuth.cs" />
    <Compile Include="IResizeImage.cs" />
    <Compile Include="ITwoFactorAuth.cs" />
    <Compile Include="KeyGenerator.cs" />
    <Compile Include="Model\ExceptionLogger.cs" />
    <Compile Include="Model\RLT_AboutUs.cs" />
    <Compile Include="Model\RLT_ADMIN_USER.cs" />
    <Compile Include="Model\RLT_BANK_NAMES.cs" />
    <Compile Include="Model\RLT_BOOKING_PAYMENT_DETAILS.cs" />
    <Compile Include="Model\RLT_CAR_CATEGORY.cs" />
    <Compile Include="Model\RLT_CAR_CHARGES_FECILITIES_DETAILS.cs" />
    <Compile Include="Model\RLT_CAR_COMPANY.cs" />
    <Compile Include="Model\RLT_CAR_DETAILS.cs" />
    <Compile Include="Model\RLT_CAR_DRIVER_DETAILS.cs" />
    <Compile Include="Model\RLT_CAR_FUEL_TYPES.cs" />
    <Compile Include="Model\RLT_CAR_MODEL.cs" />
    <Compile Include="Model\RLT_CAR_OWNER_BANK_DETAILS.cs" />
    <Compile Include="Model\RLT_CITY.cs" />
    <Compile Include="Model\RLT_ContactInformation.cs" />
    <Compile Include="Model\RLT_COUNTRY.cs" />
    <Compile Include="Model\RLT_DRIVER_APPROVE_STATUS.cs" />
    <Compile Include="Model\RLT_DRIVER_ENQUIRIES.cs" />
    <Compile Include="Model\RLT_LOCATION_CODE.cs" />
    <Compile Include="Model\RLT_LOG.cs" />
    <Compile Include="Model\RLT_MENU_MASTER.cs" />
    <Compile Include="Model\RLT_PAYMENT_METHOD.cs" />
    <Compile Include="Model\RLT_PrivacyPolicy.cs" />
    <Compile Include="Model\RLT_ROUTES_DETAILS.cs" />
    <Compile Include="Model\RLT_Services.cs" />
    <Compile Include="Model\RLT_STATE.cs" />
    <Compile Include="Model\RLT_TermsConditions.cs" />
    <Compile Include="Model\RLT_TRIP_TYPES.cs" />
    <Compile Include="Model\RLT_UserFeedBack.cs" />
    <Compile Include="Model\RLT_USER_LOGIN_DETAILS.cs" />
    <Compile Include="Model\TwoFactorModel.cs" />
    <Compile Include="PasswordKeyGen.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ResizeImage.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DBLinker.Lib\DBLinker.Lib.csproj">
      <Project>{839a2f95-b7c8-40f1-aca8-3de4e7d5e340}</Project>
      <Name>DBLinker.Lib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
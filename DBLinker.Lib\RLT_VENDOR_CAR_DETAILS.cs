//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_VENDOR_CAR_DETAILS
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public RLT_VENDOR_CAR_DETAILS()
        {
            this.Vendor_Car_Driver_Docs = new HashSet<Vendor_Car_Driver_Docs>();
        }
    
        public int PKID { get; set; }
        public int Car_Company_Ref { get; set; }
        public int Car_Segment_Ref { get; set; }
        public int Car_Fuel_Type_Ref { get; set; }
        public string Car_Number { get; set; }
        public Nullable<System.DateTime> Car_Registration_Date { get; set; }
        public string Driver_Name { get; set; }
        public string Driver_Phone1 { get; set; }
        public string Driver_Phone2 { get; set; }
        public int Vendor_Ref_ID { get; set; }
        public bool Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Last_Modified_Date { get; set; }
        public int Last_Modified_By { get; set; }
        public Nullable<int> CreatedBy { get; set; }
    
        public virtual RLT_CAR_CATEGORY RLT_CAR_CATEGORY { get; set; }
        public virtual RLT_CAR_COMPANY RLT_CAR_COMPANY { get; set; }
        public virtual RLT_CAR_FUEL_TYPES RLT_CAR_FUEL_TYPES { get; set; }
        public virtual RLT_Vendor_Details RLT_Vendor_Details { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Vendor_Car_Driver_Docs> Vendor_Car_Driver_Docs { get; set; }
    }
}

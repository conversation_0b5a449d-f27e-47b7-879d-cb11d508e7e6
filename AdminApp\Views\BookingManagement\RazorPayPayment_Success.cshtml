﻿
@{
    ViewBag.Title = "RazorPayPayment_Success";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Payment Status</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../BookingManagement/BookingList">Booking List </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Payment Status</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-body row">
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                Your payment has been processed successfully and you booking is confirmed.
                                Please check your email for booking details."
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>


    </div>
</div>

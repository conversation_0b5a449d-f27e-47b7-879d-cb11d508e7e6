﻿using AdminApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace AdminApp.Controllers
{
    [RoleBaseAuthentication]
    public class ReportController : Controller
    {
        DBLinker.Lib.RLTDBContext entity;

        public ReportController()
        {
            entity = new DBLinker.Lib.RLTDBContext();
        }
        // GET: Report
        public ActionResult BookingReport()
        { 
            ViewBag.CityList = entity.RLT_CITY.ToList(); 
            ViewBag.BookingStatusList = entity.RLT_BOOKING_STATUS.ToList();
            return View();
        }
        public ActionResult BookingInvoiceReport()
        {           
            return View();
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Vendor_Bank_Details
    {
        public int PKID { get; set; }
        public int? Bank_Name_PKID { get; set; }
        public string Bank_Name { get; set; }
        public string Vendor_Company_Name { get; set; }        
        public string IFSC_CODE { get; set; }
        public string Account_Holder_Name { get; set; }
        [Remote("Check_Account_Number", "Common", ErrorMessage = "Vendor Account Number already exists!")]
        public string Account_Number { get; set; }
        public int Vendor_Ref_PKID { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> LastModified_Date { get; set; }
        public int Lastmodified_By { get; set; }
        public bool Is_active { get; set; }
        public Nullable<bool> Bank_Status { get; set; }

    }
}

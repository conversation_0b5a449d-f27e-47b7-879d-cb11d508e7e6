﻿$(document).ready(function () {

    $("#TemplateManager").parent().parent().find("a").next().slideToggle(500);
    $("#TemplateManager").parent().parent().toggleClass('toggled');
    $("#TemplateManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
   
    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {
         
            $.ajax({
                url: 'AddTemplateManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {
            $("#Message_Body").val(tinyMCE.get('Message_Body').getContent());
            $.ajax({
                url: 'AddTemplateManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#Template_Name").val($("#Template_Name_" + Id).val());
    $("#Subject").val($("#Subject_" + Id).val());

    if ($("#Mail_SMS_Type_" + Id).val() == "Mail")
        $("#Mail_SMS_Type1").prop("checked", true);
    else
        $("#Mail_SMS_Type2").prop("checked", true);

    tinyMCE.get('Message_Body').setContent($("#Message_Body_" + Id).val());

    $("#IsActive").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}



function ActivateDeactivate(Id) {
    $("#PKID").val('');
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('D');


    if ($("#PKID").val() != "") {
        
        $.ajax({
            url: 'AddTemplateManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {
    $("#Template_Name").val('');
    $("#Subject").val('');
    $("#Message_Body").val('');
    tinyMCE.get('Message_Body').setContent('');

}

function IsValidate() {
    if ($("#Mail_SMS_Type1").prop("checked") == false && $("#Mail_SMS_Type2").prop("checked") == false) {
        toastr.clear();
        toastr.error("Please Select Template For!", "Required!");
        return false;
    }
    if ($("#Template_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Template Name !", "Required!");
        return false;
    }
    if ($("#Subject").val() == "") {
        toastr.clear();
        toastr.error("Please enter Subject !", "Required!");
        return false;
    }
    if (tinyMCE.get('Message_Body').getContent() == "") {
        toastr.clear();
        toastr.error("Please enter Message Body !", "Required!");
        return false;
    }


    $("#Message_Body").val(tinyMCE.get('Message_Body').getContent());
    
    return true;
}
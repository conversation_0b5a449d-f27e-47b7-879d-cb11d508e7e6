﻿$('.selectDropdown').select2();
$("#dvAddUpdate").slideUp();
getCity();
getCarCategory();
getTripType();
$(document).ready(function () {
    FileUploader();
    // Events 
    $("#City_Image").change(function () {
        if (PhotoCheck(document.getElementById("City_Image"))) {
            $("#city-img-viwer").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
            FileUploader();
        }
    });
    $("#btnCalculateFair").click(function () {
        if (ValidateForm(false, true)) {
            toastr.clear();
            var createUrl = "../GoogleMap/GetDistanceTimeDetails?";
            createUrl += "City_From_Id=" + $("#PickUpCityID").val()
            createUrl += '&City_To_Id=' + $("#DropOffCityID").val()
            createUrl += '&Trip_Type_Id=' + $("#TripType").val()
            createUrl += '&Car_Category_Id=' + $("#CarCategoryID").val()
            calculateFare(createUrl);
            $("#fairCalculateBtnClickedOnDdlChanged").val(true);
        }
    });
    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });
    $("#btnSave").click(function () {
        if (ValidateForm(false, false)) {
            ValidateDublicateRecord($("#PickUpCityID").val(), $("#DropOffCityID").val(), $("#TripType").val(), $("#CarCategoryID").val(), false);
        }
    });
    $("#btnUpdate").click(function () {
        if (ValidateForm(true, false)) {
            ValidateDublicateRecord($("#PickUpCityID").val(), $("#DropOffCityID").val(), $("#TripType").val(), $("#CarCategoryID").val(), true);
        }
    });
    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
    $("#PickUpCityID , #DropOffCityID , #TripType ,  #CarCategoryID").change(function () {
        $("#fairCalculateBtnClickedOnDdlChanged").val(false);
    })
    //End Events
});

//Functions
function FileUploader() {
    var getCityImg = $("#city-img-viwer").attr("src")
    if (getCityImg == "#") {
        $("#city-img-viwer").hide();
        $("p.file-uploader-text").show();
    }
    else {
        $("#city-img-viwer").show();
        $("p.file-uploader-text").hide();
    }
}
function PhotoCheck(input) {
    if (input.files.length > 0) {
        for (var i = 0; i < input.files.length; i++) {
            var file = input.files[i];
            var fileType = file["type"];
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            if ($.inArray(fileType, validImageTypes) < 0) {
                toastr.clear();
                toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
                return false;
            }
            else return true;
        }
    }
    else return "no-file";
};
function ValidateForm(isEdit, isBtnCalculateFairCliked) {
    if ($("#PickUpCityID").val() == "") {
        toastr.clear();
        toastr.error("Please select City From!");
        return false;
    }
    else if ($("#DropOffCityID").val() == "") {
        toastr.clear();
        toastr.error("Please select City To!");
        return false;
    }
    else if ($("#TripType").val() == "") {
        toastr.clear();
        toastr.error("Please select Trip Type!");
        return false;
    }
    else if ($("#CarCategoryID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Category!");
        return false;
    }
    if (!isBtnCalculateFairCliked) {
        if (!isEdit) {
            var input = document.getElementById("City_Image");
            if (input.files.length == 0) {
                toastr.clear();
                toastr.error("Please select City Image!");
                return false;
            };
        }
        if ($("input[type=radio][name=IsActive]:checked").val() == undefined) {
            toastr.clear();
            toastr.error("Please Set Most Favourite Routs to active or Inactive!");
            return false;
        }

        var toReturn;
        $(".CalculatedFairFields  :input").each(function () {
            var input = $(this);
            var Value = input.val();
            if (Value == "") {
                toastr.clear();
                toastr.error("Please calculate the fair again! Because <strong>( '" + input.attr("data-msgName") + "' )</strong> cannot be blank.");
                toReturn = false;
                return false;
            };
        });
        if (toReturn !== undefined || toReturn == false) {
            return false;
        }
    }
    return true;
}
function ValidateDublicateRecord(fromCity, toCity, tripType, carCategory, isUpdate) {
    $.ajax({
        url: "ValidateDublicateRecord",
        type: "GET",
        data: {
            fromCity: fromCity,
            toCity: toCity,
            isUpdate: isUpdate,
            tripType: tripType,
            carCategory: carCategory,
            id: $("input#ID").val() == "" ? 0 : $("input#ID").val()
        },
        success: function (result) {
            if (result > 0) {
                toastr.error("Sorry This route already exits!", "Error");
                return data = false;
            }
            else if (result == 0) {
                if (isUpdate) updateData();
                else saveData();
            }
            else {
                toastr.error("Somthing went wrong please try again!", "Error");
                return data = false;
            }
        }
    });
}
function calculateFare(Url) {
    $.ajax({
        type: 'POST',
        url: Url,
        dataType: 'json',
        success: function (FareChart) {
            if (FareChart != "") {
                $("#TravelDuration").val(FareChart.Duration);
                $("#Distance").val(FareChart.Distance);
                $("#GSTFare").val(FareChart.GST_Fare);
                $("#Fare").val(Math.round(FareChart.Fare));
            }
            else {
                $("#TravelDuration").val("");
                $("#Distance").val("");
                $("#GSTFare").val("");
                $("#Fare").val("");
                toastr.clear();
                toastr.error("No fare record found!");
                return false;
            }
        },
        onerror: function (error) {
            toastr.clear();
            toastr.error("No data found!");
        }
    });
    return false;
}
function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'ActiveDeactiveMostFavouriteRoutes',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Most Favourite Routes Status successfully Changed!", "Most Favourite Routes");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.error("Sorry Status Not Updated,Please Try it Again !", "Most Favourite Routes");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}
function resetForm() {
    $("#PickUpCityID").val('');
    $("#PickUpCityID").trigger("change");
    $("#DropOffCityID").val('');
    $("#DropOffCityID").trigger("change");
    $("#TripType").val('');
    $("#TripType").trigger("change");
    $("#CarCategoryID").val('');
    $("#CarCategoryID").trigger("change");
    $("input[type=radio][name=IsActive]").prop("checked", false);
    $("#city-img-viwer").hide();
    $("p.file-uploader-text").show();
    $("#TravelDuration").val('');
    $("#Distance").val('');
    $("#Fare").val('');
    $("#GSTFare").val('');
}
function Edit(Id) {
    $.ajax({
        url: 'EditMostFavouriteRoute',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                // fill data 
                $("#ID").val(data.ID);
                $("#PickUpCityID").val(data.PickUpCityID);
                $("#PickUpCityID").trigger("change");
                $("#DropOffCityID").val(data.DropOffCityID);
                $("#DropOffCityID").trigger("change");
                $("#TripType").val(data.TripType);
                $("#TripType").trigger("change");
                $("#CarCategoryID").val(data.CarCategoryID);
                $("#CarCategoryID").trigger("change");
                if (data.IsActive != null && data.IsActive == true) $("#IsActiveTrue").prop("checked", true);
                else $("#IsActiveFalse").prop("checked", true);
                if (data.CityImage != null && data.CityImage != "") {
                    $("#city-img-viwer").attr('src', '../Docs/MostFavouriteCityImg/' + data.CityImage)
                    $("#city-img-viwer").show();
                    $("p.file-uploader-text").hide();
                }
                else {
                    $("#city-img-viwer").hide();
                    $("p.file-uploader-text").show();
                }
                $("#TravelDuration").val(data.TravelDuration);
                $("#Distance").val(data.Distance);
                $("#Fare").val(data.Fare);
                $("#GSTFare").val(data.GSTFare);
                //end checkboxes
                // set default values 
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}
function getCity() {
    $("td.getCity").each(function () {
        var Id = $(this).attr("data-id");
        if (Id != null && Id > 0) {
            $(this).html($("#PickUpCityID option[value='" + Id + "']").text());
        }
    });
}
function getCarCategory() {
    $("td > span.getCarCategory").each(function () {
        var Id = $(this).attr("data-id");
        if (Id != null && Id > 0) {
            $(this).html($("#CarCategoryID option[value='" + Id + "']").text());
        }
    });
}
function getTripType() {
    $("td > span.getTripType").each(function () {
        var Id = $(this).attr("data-id");
        if (Id != null && Id > 0) {
            $(this).html($("#TripType option[value='" + Id + "']").text());
        }
    });
}
function saveData() {
    var rechaked = $("#fairCalculateBtnClickedOnDdlChanged").val();
    if (rechaked == "true") {
        var formData = new FormData($("#FormCabYariHome")[0]);
        formData.append("City_Image", $("#City_Image").get(0).files[0]);
        $.ajax({
            type: 'POST',
            url: 'Home',
            data: formData,
            contentType: false,
            processData: false,
            success: function (result) {
                if (result) {
                    resetForm();
                    toastr.clear();
                    toastr.success("Most Favourite Route Added successfully!");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.clear();
                    toastr.error("Record Not added successfully please try again!");
                }
            },
            onerror: function (error) {
                toastr.clear();
                toastr.error("Somthing went wrong please try again!");
            }
        });
    }
    else {
        toastr.clear();
        toastr.error("Please Calculate fair Again !", "Error");
    }
}
function updateData() {
    var rechaked = $("#fairCalculateBtnClickedOnDdlChanged").val();
    if (rechaked == "true") {
        var formData = new FormData($("#FormCabYariHome")[0]);
        formData.append("City_Image", $("#City_Image").get(0).files[0]);
        $.ajax({
            url: 'EditMostFavouriteRoute',
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            success: function (response) {
                if (response > 0) {
                    toastr.success("Most Favourite Route successfully Updated!", "Favourite Route");
                    window.setTimeout(function () { location.reload() }, 1000)
                } else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");
                }
            },
            failure: function (response) {
            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
    else {
        toastr.clear();
        toastr.error("Please Calculate fair Again !", "Error");
    }
}

//End Function


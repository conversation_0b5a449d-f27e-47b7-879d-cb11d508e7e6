﻿
$(document).ready(function () {

    $("#VendorManager").parent().parent().find("a").next().slideToggle(500);
    $("#VendorManager").parent().parent().toggleClass('toggled');
    $("#VendorManager").find("a").addClass("selectedMenu");
});

function checkfile() {
    var file = getNameFromPath($("#Vendor_Doc_Path").val());
    if (file != null) {
        var extension = file.substr((file.lastIndexOf('.') + 1));
        // alert(extension);
        switch (extension) {
            case 'jpg':
            case 'png':
            case 'gif':
            case 'pdf':
                flag = true;
                break;
            default:
                flag = false;
        }
    }
    if (flag == false) {
        toastr.warning("You can upload only jpg,png,gif,pdf extension file!!!");

        return false;
    }
    else {
        //    var size = GetFileSize('Vendor_Doc_Path');
        //if (size > 3)
        //{
        //    toastr.warning("You can upload file up to 3 MB!!!");

        //return false;
        //}

    }
}

$(function () {
    $("#Vendor_Doc_Path").change(function () {
        checkfile();
    });
});


function popupSubWindow(mylink) {
    if (!window.focus) return true; var href;
    if (typeof (mylink) == 'string') href = mylink;
    else href = mylink.href; window.open(href, 'HSMS', 'left=300,top=80,width=850,height=500,toolbar=1,scrollbars=yes'); return false;
}

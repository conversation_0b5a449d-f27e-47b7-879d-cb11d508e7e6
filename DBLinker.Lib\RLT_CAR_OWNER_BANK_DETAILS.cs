//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_CAR_OWNER_BANK_DETAILS
    {
        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        public Nullable<int> Car_Owner_PKID { get; set; }
        public Nullable<int> Bank_Name_PKID { get; set; }
        public string Bank_IFSC_Code { get; set; }
        public string Car_Owner_Bank_Acc_Name { get; set; }
        public string Car_Owner_Bank_Acc_Number { get; set; }
        public string Bank_Acc_Branch_Address { get; set; }
        public string Car_Owner_Bank_Registered_Phone { get; set; }
        public string Car_Owner_Bank_Cancelled_Chk_Photo { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Creatde_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }
        public string Updater_Comment { get; set; }
    
        public virtual RLT_BANK_NAMES RLT_BANK_NAMES { get; set; }
    }
}

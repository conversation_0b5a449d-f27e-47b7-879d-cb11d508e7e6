﻿$(document).ready(function () {

    $("#FareManager").parent().parent().find("a").next().slideToggle(500);
    $("#FareManager").parent().parent().toggleClass('toggled');
    $("#FareManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();


    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'AddFareManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'EditFareManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        //resetForm();
    });

    $("input").change(function () {
        calculateFare();
    });

});

function Edit(Id) {
    $.ajax({
        url: 'EditFareManager',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                // fill data 
                $("#PKID").val(data.PKID);
                $("#Is_Active").val(data.Is_Active);
                $("#City_From").val(data.City_From);
                $("#City_To").val(data.City_To);
                $("#Trip_Type_Id").val(data.Trip_Type_Id);
                $("#Car_Category_Id").val(data.Car_Category_Id);
                $("#Basic_Fare").val(data.Basic_Fare);
                $("#Driver_Charge").val(data.Driver_Charge);
                $("#Toll_Charge").val(data.Toll_Charge);
                $("#Total_Fare").val(data.Total_Fare);
                $("#Final_Fare").val(data.Final_Fare);
                $("#GST").val(data.GST);
                $("#GST_Amount").val(data.GST_Amount);
                $("#Remark").val(data.Remark);
                // set default values 
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}

function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
     

    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'AddFareManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'ActiveDeactiveFareManager',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Fare Manager Status successfully Changed!", "Fare Manager");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.error("Sorry Status Not Updated,Please Try it Again !", "Fare Manager");
                }
            },
            failure: function (response) {
                toastr.error(response.responseText, "Fare Manager");
            },
            error: function (response) {
                toastr.error(response.responseText, "Fare Manager");
            }
        });
    }
}

function resetForm() {

    $("#City_From").val('');


}

function IsValidate() {
    if ($("#City_From").val() == "") {
        toastr.clear();
        toastr.error("Please select city from");
        return false;
    }

    return true;
}

function calculateFare() {
    var val1 = (isNaN(parseFloat($('#Basic_Fare').val()))) ? 0 : parseFloat($('#Basic_Fare').val());
    var val2 = (isNaN(parseFloat($('#Driver_Charge').val()))) ? 0 : parseFloat($('#Driver_Charge').val());
    var val3 = (isNaN(parseFloat($('#Toll_Charge').val()))) ? 0 : parseFloat($('#Toll_Charge').val());
    var val4 = val1 + val2 + val3;

    $('#Total_Fare').val(val4);


    if ($("#GST").val() != "") {
        var val5 = (isNaN(parseFloat($('#GST').val()))) ? 0 : parseFloat($('#GST').val());
        var val6 = (val4 * val5) / 100;
        $('#GST_Amount').val(val6);
        $('#Final_Fare').val(val4 + val6);
    }


}

function GetCouponDiscount() {

    $.ajax({
        url: 'GetCouponDiscount',
        type: "POST",
        traditional: true,
        data: $("form").serialize(),
        dataType: "json",
        success: function (response) {
            alert(response);
            $("#CouponDiscountPer").val(response.responseText);
        },
        failure: function (response) {

        },
        error: function (response) {
            alert(response.responseText);
        }
    });
}


﻿using DBLinker.Lib;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Common.Lib.Impl
{
    public static class CommonMethods
    {

      

        public static void MailSend(string strMailTo, string strMailSubject, string strMailBody, bool blnAllowMultipleBCC, bool blnSMTPGMAIL)
        {
            string strEmailDisplayName = ConfigurationManager.AppSettings.Get("PrimaryDomain");
            MailMessage MyMailMessage = new MailMessage();
            MyMailMessage.From = new MailAddress(ConfigurationManager.AppSettings.Get("fromEmail"), ConfigurationManager.AppSettings.Get("CompanyName"));
            MyMailMessage.To.Add(strMailTo);

            string strBCCEmail = ConfigurationManager.AppSettings.Get("bccEmail");
            string strMultipleBCCEmail = ConfigurationManager.AppSettings.Get("MultipleBCCEmail");
            string[] strBCCEmails = strMultipleBCCEmail.Split(',');
            if (strBCCEmails != null && strBCCEmails.Length > 0)
            {
                strBCCEmail = strBCCEmails[0].Trim();
            }
            if (blnAllowMultipleBCC == true)
            {
                if (!string.IsNullOrEmpty(strMultipleBCCEmail))
                {
                    strBCCEmails = strMultipleBCCEmail.Split(',');
                    if (strBCCEmails != null && strBCCEmails.Length > 0)
                    {
                        foreach (string strItem in strBCCEmails)
                        {
                            MyMailMessage.Bcc.Add(strItem.Trim());
                        }
                    }
                }
                else
                {
                    MyMailMessage.Bcc.Add(strBCCEmail);
                }
            }
            else
            {
                MyMailMessage.Bcc.Add(strBCCEmail);
            }

            MyMailMessage.Subject = strMailSubject;
            MyMailMessage.IsBodyHtml = true;
            MyMailMessage.Body = strMailBody;
            SmtpClient SMTPServer = new SmtpClient(ConfigurationManager.AppSettings.Get("smtpServer"));
            string smtpPort = ConfigurationManager.AppSettings.Get("smtpPort");
            SMTPServer.Port = Convert.ToInt32(smtpPort);
            SMTPServer.EnableSsl = true;
            SMTPServer.UseDefaultCredentials = false;
            SMTPServer.Credentials = new NetworkCredential(ConfigurationManager.AppSettings.Get("smtpAuthEmail"), ConfigurationManager.AppSettings.Get("smtpAuthPassword"));
            SMTPServer.Send(MyMailMessage);
        }

        public static int SB_TryParseInt32(string value)
        {
            int i;
            Int32.TryParse(value, out i);
            return i;
        }

        public static decimal SB_TryParseDecimal(string value)
        {
            decimal i;
            decimal.TryParse(value, out i);
            return i;
        }

    }
}

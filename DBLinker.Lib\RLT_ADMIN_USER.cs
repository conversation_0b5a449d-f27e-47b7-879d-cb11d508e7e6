//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_ADMIN_USER
    {
        public int PKID { get; set; }
        public Nullable<int> RoleId { get; set; }
        public string UserName { get; set; }
        public string UserPWD { get; set; }
        public string UserPhoto { get; set; }
        public string UserFirstName { get; set; }
        public string UserMiddleName { get; set; }
        public string UserLastName { get; set; }
        public string UserEmailID { get; set; }
        public string UserMobileNo { get; set; }
        public Nullable<int> VendorId { get; set; }
        public Nullable<int> CarOwnerId { get; set; }
        public Nullable<int> EmployeeId { get; set; }
        public string AadharId { get; set; }
        public Nullable<int> DepartmentId { get; set; }
        public string Address { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public Nullable<int> IsAdmin { get; set; }
        public Nullable<bool> IsDeleted { get; set; }
        public string Description { get; set; }
        public Nullable<bool> Is2TfaAuthentication { get; set; }
        public Nullable<System.Guid> ID { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<int> UpdatedBy { get; set; }
        public Nullable<int> CreatedBy { get; set; }
    }
}

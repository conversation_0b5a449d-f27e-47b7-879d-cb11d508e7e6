﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ds_Invoice" targetNamespace="http://tempuri.org/ds_Invoice.xsd" xmlns:mstns="http://tempuri.org/ds_Invoice.xsd" xmlns="http://tempuri.org/ds_Invoice.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ds_Invoice" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="ds_Invoice" msprop:Generator_UserDSName="ds_Invoice">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="dt_Invoice" msprop:Generator_TableClassName="dt_InvoiceDataTable" msprop:Generator_TableVarName="tabledt_Invoice" msprop:Generator_RowChangedName="dt_InvoiceRowChanged" msprop:Generator_TablePropName="dt_Invoice" msprop:Generator_RowDeletingName="dt_InvoiceRowDeleting" msprop:Generator_RowChangingName="dt_InvoiceRowChanging" msprop:Generator_RowEvHandlerName="dt_InvoiceRowChangeEventHandler" msprop:Generator_RowDeletedName="dt_InvoiceRowDeleted" msprop:Generator_RowClassName="dt_InvoiceRow" msprop:Generator_UserTableName="dt_Invoice" msprop:Generator_RowEvArgName="dt_InvoiceRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Invoice_No" msprop:Generator_ColumnVarNameInTable="columnInvoice_No" msprop:Generator_ColumnPropNameInRow="Invoice_No" msprop:Generator_ColumnPropNameInTable="Invoice_NoColumn" msprop:Generator_UserColumnName="Invoice_No" type="xs:string" minOccurs="0" />
              <xs:element name="Invoice_Date" msprop:Generator_ColumnVarNameInTable="columnInvoice_Date" msprop:Generator_ColumnPropNameInRow="Invoice_Date" msprop:Generator_ColumnPropNameInTable="Invoice_DateColumn" msprop:Generator_UserColumnName="Invoice_Date" type="xs:string" minOccurs="0" />
              <xs:element name="Basic_Fare" msprop:Generator_ColumnVarNameInTable="columnBasic_Fare" msprop:Generator_ColumnPropNameInRow="Basic_Fare" msprop:Generator_ColumnPropNameInTable="Basic_FareColumn" msprop:Generator_UserColumnName="Basic_Fare" type="xs:string" minOccurs="0" />
              <xs:element name="Driver_Charge" msprop:Generator_ColumnVarNameInTable="columnDriver_Charge" msprop:Generator_ColumnPropNameInRow="Driver_Charge" msprop:Generator_ColumnPropNameInTable="Driver_ChargeColumn" msprop:Generator_UserColumnName="Driver_Charge" type="xs:string" minOccurs="0" />
              <xs:element name="Toll_Charge" msprop:Generator_ColumnVarNameInTable="columnToll_Charge" msprop:Generator_ColumnPropNameInRow="Toll_Charge" msprop:Generator_ColumnPropNameInTable="Toll_ChargeColumn" msprop:Generator_UserColumnName="Toll_Charge" type="xs:string" minOccurs="0" />
              <xs:element name="GST" msprop:Generator_ColumnVarNameInTable="columnGST" msprop:Generator_ColumnPropNameInRow="GST" msprop:Generator_ColumnPropNameInTable="GSTColumn" msprop:Generator_UserColumnName="GST" type="xs:string" minOccurs="0" />
              <xs:element name="Fare" msprop:Generator_ColumnVarNameInTable="columnFare" msprop:Generator_ColumnPropNameInRow="Fare" msprop:Generator_ColumnPropNameInTable="FareColumn" msprop:Generator_UserColumnName="Fare" type="xs:string" minOccurs="0" />
              <xs:element name="GST_Fare" msprop:Generator_ColumnVarNameInTable="columnGST_Fare" msprop:Generator_ColumnPropNameInRow="GST_Fare" msprop:Generator_ColumnPropNameInTable="GST_FareColumn" msprop:Generator_UserColumnName="GST_Fare" type="xs:string" minOccurs="0" />
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" type="xs:string" minOccurs="0" />
              <xs:element name="Mobile_No1" msprop:Generator_ColumnVarNameInTable="columnMobile_No1" msprop:Generator_ColumnPropNameInRow="Mobile_No1" msprop:Generator_ColumnPropNameInTable="Mobile_No1Column" msprop:Generator_UserColumnName="Mobile_No1" type="xs:string" minOccurs="0" />
              <xs:element name="PickUp_Address" msprop:Generator_ColumnVarNameInTable="columnPickUp_Address" msprop:Generator_ColumnPropNameInRow="PickUp_Address" msprop:Generator_ColumnPropNameInTable="PickUp_AddressColumn" msprop:Generator_UserColumnName="PickUp_Address" type="xs:string" minOccurs="0" />
              <xs:element name="DropOff_Address" msprop:Generator_ColumnVarNameInTable="columnDropOff_Address" msprop:Generator_ColumnPropNameInRow="DropOff_Address" msprop:Generator_ColumnPropNameInTable="DropOff_AddressColumn" msprop:Generator_UserColumnName="DropOff_Address" type="xs:string" minOccurs="0" />
              <xs:element name="Car_Number" msprop:Generator_ColumnVarNameInTable="columnCar_Number" msprop:Generator_ColumnPropNameInRow="Car_Number" msprop:Generator_ColumnPropNameInTable="Car_NumberColumn" msprop:Generator_UserColumnName="Car_Number" type="xs:string" minOccurs="0" />
              <xs:element name="Car_Model_Name" msprop:Generator_ColumnVarNameInTable="columnCar_Model_Name" msprop:Generator_ColumnPropNameInRow="Car_Model_Name" msprop:Generator_ColumnPropNameInTable="Car_Model_NameColumn" msprop:Generator_UserColumnName="Car_Model_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Driver_Name" msprop:Generator_ColumnVarNameInTable="columnDriver_Name" msprop:Generator_ColumnPropNameInRow="Driver_Name" msprop:Generator_ColumnPropNameInTable="Driver_NameColumn" msprop:Generator_UserColumnName="Driver_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Driver_Photo" msprop:Generator_ColumnVarNameInTable="columnDriver_Photo" msprop:Generator_ColumnPropNameInRow="Driver_Photo" msprop:Generator_ColumnPropNameInTable="Driver_PhotoColumn" msprop:Generator_UserColumnName="Driver_Photo" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>
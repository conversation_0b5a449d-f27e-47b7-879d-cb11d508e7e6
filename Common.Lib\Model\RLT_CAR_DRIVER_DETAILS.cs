//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Common.Lib.Model
{
    using System;
    using System.Collections.Generic;
    
    public  class RLT_CAR_DRIVER_DETAILS
    {
        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        public Nullable<int> Car_Owner_PKID { get; set; }
        public string Driver_Name { get; set; }
        public string Driver_Photo { get; set; }
        public Nullable<System.DateTime> Driver_DOB { get; set; }
        public string Driver_FName { get; set; }
        public string Driver_MName { get; set; }
        public string Driver_LName { get; set; }
        public string Drive_Phone_1 { get; set; }
        public string Drive_Phone_2 { get; set; }
        public string Driver_DL { get; set; }
        public string Driver_Aadhaar { get; set; }
        public string Driver_Father_Name { get; set; }
        public string Driver_Mother_Name { get; set; }
        public string Driver_Marrital_Status { get; set; }
        public string Driver_Gender { get; set; }
        public string Driver_Current_Address { get; set; }
        public string Driver_Permanent_Address { get; set; }
        public string Driver_Smoking_Status { get; set; }
        public string Driver_Drinking_Status { get; set; }
        public string Driver_Eating_Type { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }
        public string Will_Owner_Drive_Car_Status { get; set; }
    
      
    }
}

﻿@model DBLinker.Lib.Model.M_Admin_User
@using AdminApp.Mapper;

@{
    ViewBag.Title = "User Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
    var AdditionPermittedAction = AdminApp.Services.RoleBaseAuthentication.getAddtionalPermittedAction("EmployeeList", "UserManagement");
}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">User Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add New User</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddUserManager", "UserManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormUserManager" }))
        {
            @Html.AntiForgeryToken()
            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate" style="display:none">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add User</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-8 p-t-20 row">
                                <div class="col-lg-12 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @if (ViewBag.RoleList != null)
                                        {
                                            @Html.DropDownListFor(x => x.RoleId, new SelectList(ViewBag.RoleList, "RoleId", "Role"), "-- Select Role --",
                                           new { @class = "mdl-textfield__input selectDropdown", @style = "padding-top: 25px;" })
                                        }
                                        <label class="mdl-textfield__label required"><span>Role Name</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.UserName, new { @class = "mdl-textfield__input", @maxlength = "100" })
                                        <label class="mdl-textfield__label required"><span>User Name</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.UserPWD, new { @class = "mdl-textfield__input" })
                                        <label class="mdl-textfield__label required"><span>User Password</span> </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <label for="UserPhoto" class="file-uploader-lable">
                                        <div id="photo-div" class="file-uploader">
                                            <img src="#" class="file-uploder-img" id="img-viwer" style="display: none;" />
                                            <p class="file-uploader-text"><strong>Click to<br />Choose Image</strong></p>
                                            @Html.TextBoxFor(x => x.UserPhoto, new { @class = "mdl-textfield__input", @type = "file", @accept = "image/x-png,image/gif,image/jpeg", @style = "display:none" })
                                        </div>
                                    </label>
                                    <label class="mdl-textfield__label required"><span>User Image</span></label>
                                </div>
                            </div>






                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.UserFirstName, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>First Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.UserMiddleName, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Middle Name</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.UserLastName, "Last Name", new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Last Name</label>
                                </div>
                            </div>


                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.UserEmailID, new { @class = "mdl-textfield__input", @maxlength = "30" })
                                    <label class="mdl-textfield__label required"><span>Email</span></label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.UserMobileNo, new { @class = "mdl-textfield__input txtbx", @maxlength = "12" })
                                    <label class="mdl-textfield__label required"><span>Mobile</span></label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input" style="padding-bottom: 8px !important; ">
                                        <div style="float:left;width:30%">
                                            <input id="IsActiveTrue" name="IsActive" type="radio" value="true">
                                            <label for="IsActive"><span><span></span></span>Active</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            <input id="IsActiveFalse" name="IsActive" type="radio" value="false">
                                            <label for="IsActive"><span><span></span></span>Inactive</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Set User Active</span> </label>
                                </div>
                            </div>

                        </div>

                        <div class="col-lg-12 p-t-20 text-center">
                            <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                            <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                        </div>
                    </div>
                </div>
            </div>


        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Users</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Photo</th>
                                        <th>Role</th>
                                        <th>User Name</th>
                                        <th>Full Name</th>
                                        <th>Mail Id</th>
                                        <th>Phone</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{ int index = 1; }
                                    @if (ViewBag.AdminUserList != null)
                                    {
                                        foreach (var item in ViewBag.AdminUserList as List<DBLinker.Lib.Model.M_Admin_User>)
                                        {
                                            <tr class="odd gradeX">
                                                <td> @index @{index = @index + 1;}</td>
                                                <td>
                                                    <img id="UserPhoto" @(string.IsNullOrWhiteSpace(item.UserPhoto) ? "src=../Docs/No_image_available.png" : Url.Content("src=/Docs/UserPhoto/" + item.UserPhoto)) height="60" width="60" onerror="this.onerror=null;this.src='../Docs/No_image_available.png';" />
                                                </td>
                                                <td>@item.RoleName</td>
                                                <td>@item.UserName</td>
                                                <td>@(item.UserFirstName + " " + item.UserMiddleName +" " + item.UserLastName)</td>
                                                <td>@item.UserEmailID</td>
                                                <td>@(item.UserMobileNo == null ? "n/a" : item.UserMobileNo)</td>
                                                <td>
                                                    @Html.Raw(item.IsActive == null
                                                ? "<span class='label label-sm label-warning'> Not Set </span>" : item.IsActive == true
                                                ? "<span class='label label-sm label-success'> Active </span>"
                                                : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID + ")' title='Enable/Disable'> <i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Edit? Html.Raw("<a onclick='Edit("+item.PKID+")' title='Edit Employee Info'><i class='material-icons'>mode_edit</i>Edit Basic Info</a>") :Html.Raw(""))

                                                            </li>
                                                            <li>
                                                                @(item.RoleName == "Employee" && AdditionPermittedAction.Is_Edit && PermittedAction.Is_Edit ? Html.Raw("<a href='../UserManagement/Employee?id="+QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Employee")+ "' title='Edit Employee Info'> <i class='material-icons'>mode_edit</i>Edit Addition Info</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(AdditionPermittedAction.Is_View && PermittedAction.Is_View ? Html.Raw("<a href='../UserManagement/EmployeeView?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Employee")+ "' title='Edit Employee Info'> <i class='material-icons'>remove_red_eye</i>View User Profile</a>") : Html.Raw(""))
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>

<script src="~/Scripts/CrudFile/UserManager.js"></script>


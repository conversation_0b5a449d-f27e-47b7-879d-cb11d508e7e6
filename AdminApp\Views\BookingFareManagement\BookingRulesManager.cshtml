﻿@model DBLinker.Lib.Model.M_BookingRules

@{
    ViewBag.Title = "Discount Coupon Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Rules Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    
                    <li class="active">Add Booking Rules</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {
            @*@Html.AntiForgeryToken()*@

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Booking Rules Details</header>

                        </div>
                        <div class="card-body row">

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.TripTypeList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Trip_Type_Id, new SelectList(ViewBag.TripTypeList, "PKID", "Trip_Type"), "-- Select Trip Type --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Trip Type</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CarCategory != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Category_Id, new SelectList(ViewBag.CarCategory, "PKID", "Car_Category"), "-- Select Car Category --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>CAR Category</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Distance_From, new { @class = "mdl-textfield__input txtbx" })
                                    <label class="mdl-textfield__label required"><span>Distance From</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Distance_To, new { @class = "mdl-textfield__input txtbx" })
                                    <label class="mdl-textfield__label required"><span>Distance To</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.NewDistance, new { @class = "mdl-textfield__input txtbx" })
                                    <label class="mdl-textfield__label required"><span>Fix Fare</span></label>
                                </div>
                            </div>


                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Booking Rules</header>
                        
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a>") : Html.Raw(""))
                                </div>
                            </div>

                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>Trip Type</th>
                                        <th>Car Category</th>
                                        <th>Distance From</th>
                                        <th>Distance To</th>
                                        <th>Fix Fare</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @if (ViewBag.BookingRulesList != null)
                                    {
                                        foreach (var item in ViewBag.BookingRulesList)
                                        {

                                            <tr class="odd gradeX">  
                                                <td>@item.Trip_Type</td>
                                                <td>@item.Car_Category</td>
                                                <td>@item.Distance_From</td>
                                                <td>@item.Distance_To</td>
                                                <td>@item.NewDistance</td>
                                                <td>
                                                    @Html.Raw(item.Is_Active == null
                                                  ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                                  ? "<span class='label label-sm label-success'> Active </span>"
                                                  : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>



                                                <td align="center">
                                                    @(PermittedAction.Is_Edit ? Html.Raw("<a class='btn btn-tbl-edit btn-xs' onclick='ActivateDeactivate(" + item.PKID + ")' title='Enable/Disable'><i class='fa fa-check'></i></a>") : Html.Raw(""))
                                                    @(PermittedAction.Is_Edit ? Html.Raw("<a class='btn btn-tbl-delete btn-xs' onclick='Edit(" + item.PKID + ")' title='Edit'><i class='fa fa-pencil'></i></a>") : Html.Raw(""))
                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Scripts/CrudFile/BookingRules.js"></script>

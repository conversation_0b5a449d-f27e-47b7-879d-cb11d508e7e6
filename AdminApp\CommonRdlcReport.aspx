﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CommonRdlcReport.aspx.cs" Inherits="AdminApp.CommonRdlcReport" %>

<%@ Register Assembly="Microsoft.ReportViewer.WebForms" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajax" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js" type="text/javascript"></script>
    <title>Report</title>
    <style>
        body:nth-of-type(1) img[src*="Blank.gif"] {
            display: none;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">
        <ajax:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
        </ajax:ToolkitScriptManager>

        <input id="printreport" runat="server" type="button" value="Print" /><br />

        <br />
        <div style="position: relative; left: 20%; width: 60%;">
            <div style="overflow-x: auto;">
                <rsweb:ReportViewer ID="rvList" runat="server" SizeToReportContent="True" Height=""
                    ShowCredentialPrompts="False" ShowParameterPrompts="False" Width="100%" DocumentMapCollapsed="True">
                </rsweb:ReportViewer>
            </div>

        </div>

        <script type="text/javascript">
            $(function () {
                $('#printreport').click(function () {
                    printReport('rvList');
                })
            });

            // Print function (require the reportviewer client ID)
            function printReport(report_ID) {
                var rv1 = $('#' + report_ID);
                var iDoc = rv1.parents('html');

                // Reading the report styles
                var styles = iDoc.find("head style[id$='ReportControl_styles']").html();
                if ((styles == undefined) || (styles == '')) {
                    iDoc.find('head script').each(function () {
                        var cnt = $(this).html();
                        var p1 = cnt.indexOf('ReportStyles":"');
                        if (p1 > 0) {
                            p1 += 15;
                            var p2 = cnt.indexOf('"', p1);
                            styles = cnt.substr(p1, p2 - p1);
                        }
                    });
                }
                if (styles == '') { alert("Cannot generate styles, Displaying without styles.."); }
                styles = '<style type="text/css">' + styles + "</style>";

                // Reading the report html
                var table = rv1.find("div[id$='_oReportDiv']");
                if (table == undefined) {
                    alert("Report source not found.");
                    return;
                }

                // Generating a copy of the report in a new window
                var docType = '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/loose.dtd">';
                var docCnt = styles + table.parent().html();
                var docHead = '<head><title>Printing ...</title><style>body{margin:5;padding:0;}</style></head>';
                var winAttr = "location=yes,statusbar=no,directories=no,menubar=no,titlebar=no,toolbar=no,dependent=no,width=720,height=600,resizable=yes,screenX=200,screenY=200,personalbar=no,scrollbars=yes";;
                var newWin = window.open("", "_blank", winAttr);
                writeDoc = newWin.document;
                writeDoc.open();
                writeDoc.write(docType + '<html>' + docHead + '<body onload="window.print();">' + docCnt + '</body></html>');
                writeDoc.close();

                // The print event will fire as soon as the window loads
                newWin.focus();
                // uncomment to autoclose the preview window when printing is confirmed or canceled.
                // newWin.close();
            };

        </script>
    </form>
</body>
</html>

﻿@model DBLinker.Lib.Model.M_Vendor_Details
@using AdminApp.Mapper;
@{
    ViewBag.Title = "Vendor Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Vendor Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Vendors Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Vendors</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddVendor", "VendorManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormVendorAdd" }))
        {

            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)

            //div pannel for add update form
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Vendors</header>

                        </div>
                        <div class="card-body row">


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Member_ID, new { @class = "mdl-textfield__input", @maxlength = "25" })
                                    @Html.ValidationMessageFor(model => model.Vendor_Member_ID, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label required"><span>Vendor Memebr ID</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_PWD, new { @class = "mdl-textfield__input", @maxlength = "15" })
                                    <label class="mdl-textfield__label required"><span>Vendor Password</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Company_Name, new { @class = "mdl-textfield__input", @onblur = "isTextSpecialValid(this);", @maxlength = "150" })
                                    @Html.ValidationMessageFor(model => model.Vendor_Company_Name, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label required"><span>Vendor Company Name</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input", @onblur = "isTextValid(this);", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Vendor Name</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <img src="~/Content/img/no-user-image.gif" id="Vendor_Photo_View" height="80" width="100" />


                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Photo, new { @class = "mdl-textfield__input", @type = "file", @style = "height: 60px !important;border: 1px dashed rgba(0, 0, 0, 0.3);*/: ; " })
                                    @Html.HiddenFor(x => x.Vendor_Photo_EditFile)
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Phone1, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label required"><span>Mobile Number</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Phone1_IsWhatsup, "Yes", new { @id = "Phone1_IsWhatsup1" })
                                            <label for="radio1"><span><span></span></span> Yes</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Phone1_IsWhatsup, "No", new { @id = "Phone1_IsWhatsup2" })
                                            <label for="radio2"><span><span></span></span>No</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label"><span>Is Whatsup No</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Phone2, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label">Alternative Number</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_EmailId, new { @class = "mdl-textfield__input", @onblur = "isEmail(this);", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Vendor Email ID</span></label>
                                </div>
                            </div>
                            <div class="col-lg-8 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Company_Address, new { @class = "mdl-textfield__input", @onblur = "isTextSpecialValid(this);", @maxlength = "100" })
                                    <label class="mdl-textfield__label required"><span>Vendor Address</span> </label>
                                </div>
                            </div>


                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw("")) 
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            //End div pannel for add update form
        }
        @*//div list pannel for vendor`s *@
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Vendors</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a>") : Html.Raw(""))
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <td>Photo</td>
                                        <th>Owner</th>
                                        <th>Company</th>
                                        <th>Member ID</th>
                                        <th>Email Id</th>
                                        <th>Phone 1</th>

                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.vendorList != null)
                                    {
                                        foreach (var item in ViewBag.vendorList)
                                        {

                                            <tr class="odd gradeX">
                                                <td>
                                                    @if (!string.IsNullOrEmpty(@item.Vendor_Photo))
                                                    {
                                                        <img src="/Docs/VendorPhoto/@item.Vendor_Photo" id="stuImage" height="60" width="60" />
                                                    }
                                                    else
                                                    {
                                                        <img src="~/Content/img/no-user-image.gif" id="VendorImage" height="60" width="60" />
                                                    }
                                                </td>
                                                <td>@item.Vendor_Owner_Name</td>
                                                <td>@item.Vendor_Company_Name</td>
                                                <td>@item.Vendor_Member_Id</td>
                                                <td>@item.Vendor_EmailID</td>
                                                <td>@item.Vendor_Phone1</td>
                                                <td>
                                                    @Html.Raw(item.Is_Active == null
                                                ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                                ? "<span class='label label-sm label-success'> Active </span>"
                                                : "<span class='label label-sm label-danger'> InActive </span>") 
                                                </td>


                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID+ ")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit("+item.PKID + ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw("")) 
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_View ? Html.Raw("<a href='../VendorManagement/VendorManagerView?id="+ QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Vendor") + "' title='View Vendor Info'><i class='material-icons'>remove_red_eye</i> View</a>") : Html.Raw(""))
                                                            </li>

                                                            <li>
                                                                @(PermittedAction.Is_Add ? Html.Raw("<a href='../VendorManagement/VendorDocs?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Vendor") + "' title='Upload Documents'><i class='material-icons'>attachment</i> Upload Docs</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Add ? Html.Raw("<a href='../VendorManagement/VendorBankDetails?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Vendor") + "' title='Upload Documents'><i class='material-icons'>attachment</i> Upload Bank Details</a>") : Html.Raw(""))
                                                              
                                                            </li>



                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @*//End div list pannel for vendor`s *@



    </div>
</div>

<script src="~/Scripts/CrudFile/VendorCrudManager.js"></script>






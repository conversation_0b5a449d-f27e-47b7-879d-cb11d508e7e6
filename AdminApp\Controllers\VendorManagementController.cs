﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using AdminApp.Mapper;
using AdminApp.Services;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
using Microsoft.Ajax.Utilities;

namespace AdminApp.Controllers
{

    [RoleBaseAuthentication]
    public class VendorManagementController : Controller
    {
        // GET: VendorManagement

        DBLinker.Lib.RLTDBContext entity;


        IGenericRepository<RLT_LOG> logsRepo;
        IGenericRepository<RLT_Vendor_Details> vendorDTLSRepo;
        IGenericRepository<RLT_BOOKING_MODE> bookingModeRepo;
        IGenericRepository<RLT_Vendor_Docs> vendorDocsRepo;
        IGenericRepository<RLT_Vendor_Bank_Details> VendorBankDetailsRepo;

        IGenericRepository<RLT_CAR_OWNER_DETAILS> CarOwnerDetailsRepo;
        IGenericRepository<RLT_CAR_DRIVER_DETAILS> CarDriverDetailsRepo;
        IGenericRepository<RLT_CAR_DETAILS> CarDetailsRepo;
        ITwoFactorAuth auth;
        IResizeImage imageResize;
        ObjectMapper map;
        public VendorManagementController(ITwoFactorAuth _auth, IResizeImage _imageResize, IGenericRepository<RLT_Vendor_Details> _vendorDTLSRepo,
            IGenericRepository<RLT_BOOKING_MODE> _bookingModeRepo, IGenericRepository<RLT_Vendor_Docs> _vendorDocsRepo, IGenericRepository<RLT_Vendor_Bank_Details> _VendorBankDetailsRepo,
             IGenericRepository<RLT_CAR_OWNER_DETAILS> _CarOwnerDetailsRepo,
        IGenericRepository<RLT_CAR_DRIVER_DETAILS> _CarDriverDetailsRepo,
        IGenericRepository<RLT_CAR_DETAILS> _CarDetailsRepo)
        {

            imageResize = _imageResize;
            auth = _auth;
            vendorDTLSRepo = _vendorDTLSRepo;
            bookingModeRepo = _bookingModeRepo;
            map = new ObjectMapper();
            vendorDocsRepo = _vendorDocsRepo;
            VendorBankDetailsRepo = _VendorBankDetailsRepo;

            CarOwnerDetailsRepo = _CarOwnerDetailsRepo;
            CarDriverDetailsRepo = _CarDriverDetailsRepo;
            CarDetailsRepo = _CarDetailsRepo;

            entity = new DBLinker.Lib.RLTDBContext();
        }

        #region 1 Vendor add/edit/delete/activeDeactive/view 

        public ActionResult VendorManager()
        {
            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;
            var model = new M_Vendor_Details();
            return View(model);
        }


        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult VendorManagerAdd(M_Vendor_Details obj)
        {
            var vdtls = map.VendorDetails(obj);
            if (!string.IsNullOrEmpty(obj.Vendor_Photo))
            {
                HttpPostedFileBase file = Request.Files[0];
                string path = Guid.NewGuid() + "_" + file.FileName;
                string Savepath = Server.MapPath("~/Docs/VendorPhoto/") + path;
                ImageWaterMark ob = new ImageWaterMark();
                ob.AddImageWaterMark(Savepath, file);
                vdtls.Vendor_Photo = path;
            }
            vdtls.CreatedBy = UserServices.getCurrentUserId();
            vdtls.Created_Date = DateTime.Now;
            vendorDTLSRepo.Add(vdtls);
            vendorDTLSRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }


        [HttpGet]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult VendorManagerEdit(int pkid)
        {
            bool status = false;
            var vendor = entity.RLT_Vendor_Details.Where(x => x.PKID == pkid).Select(x => new M_Vendor_Details
            {
                Vendor_Member_ID = x.Vendor_Member_Id,
                Vendor_PWD = x.Vendor_Pwd,
                Vendor_Company_Name = x.Vendor_Company_Name,
                Vendor_Name = x.Vendor_Owner_Name,
                Vendor_Photo = x.Vendor_Photo,
                Vendor_Phone1 = x.Vendor_Phone1,
                Phone1_IsWhatsup = x.Phone1_IsWhatsup,
                Vendor_Phone2 = x.Vendor_Phone2,
                Vendor_EmailId = x.Vendor_EmailID,
                Vendor_Company_Address = x.Vendor_Address,
                Is_2FA_Activated = x.Is_2FA_Activated != true ? false : true,
                Vendor_Photo_EditFile = x.Vendor_Photo,
                Is_Active = x.Is_Active != true ? false : true,
                PKID = x.PKID
            }).FirstOrDefault();
            if (vendor != null) return Json(vendor, JsonRequestBehavior.AllowGet);
            return Json(status, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult VendorManagerEdit(M_Vendor_Details obj)
        {
            var retriveVdtls = entity.RLT_Vendor_Details.Where(x => x.PKID == obj.PKID).FirstOrDefault();
            var vdtls = map.VendorDetails(obj);

            if (!string.IsNullOrEmpty(obj.Vendor_Photo))
            {
                HttpPostedFileBase file = Request.Files[0];
                string path = Guid.NewGuid() + "_" + file.FileName;
                string Savepath = Server.MapPath("~/Docs/VendorPhoto/") + path;
                ImageWaterMark ob = new ImageWaterMark();
                ob.AddImageWaterMark(Savepath, file);
                vdtls.Vendor_Photo = path;
            }
            if (vdtls.Vendor_Photo == null) vdtls.Vendor_Photo = obj.Vendor_Photo_EditFile;
            vdtls.CreatedBy = retriveVdtls.CreatedBy;
            vdtls.Created_Date = retriveVdtls.Created_Date;
            vdtls.Updated_Date = DateTime.Now;
            vdtls.Last_Modified_By = UserServices.getCurrentUserId();
            vendorDTLSRepo.Edit(vdtls);
            vendorDTLSRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult VendorManagerActivedDeactivate(int pkid)
        {
            bool status = false;
            var vdtls = entity.RLT_Vendor_Details.Where(x => x.PKID == pkid).FirstOrDefault();
            if (vdtls != null)
            {
                vdtls.Is_Active = vdtls.Is_Active != null ? (vdtls.Is_Active == true ? false : true) : false;
                vdtls.Updated_Date = DateTime.Now;
                vdtls.Last_Modified_By = UserServices.getCurrentUserId();
                entity.SaveChanges();
                status = true;
            }
            return Json(status, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult VendorManagerView()
        {
            M_Vendor_Details Vendor_Details = new M_Vendor_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Vendor");
                int VendorId = Convert.ToInt16(DecData);

                //1 Bind Vendor Basic Details
                Vendor_Details = (from a in entity.RLT_Vendor_Details
                                  where a.PKID == VendorId
                                  select new M_Vendor_Details
                                  {
                                      Vendor_Company_Name = a.Vendor_Company_Name,
                                      Vendor_Photo = a.Vendor_Photo,
                                      Vendor_Member_ID = a.Vendor_Member_Id,
                                      Vendor_Name = a.Vendor_Owner_Name,
                                      Vendor_Phone1 = a.Vendor_Phone1,
                                      Vendor_Phone2 = a.Vendor_Phone2,
                                      Vendor_EmailId = a.Vendor_EmailID,
                                      Vendor_Company_Address = a.Vendor_Address
                                  }).FirstOrDefault();

                //2 Bind Vendor Documents
                Vendor_Details.M_Vendor_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                                    join b in entity.RLT_Vendor_Docs on
                                                     new
                                                     { Vendor_Doc_Id = a.PKID, VendorIdNew = VendorId } equals
                                               new { Vendor_Doc_Id = b.Vendor_Doc_Id, VendorIdNew = b.Vendor_Ref_PKID }

                                                   into ps
                                                    from b in ps.DefaultIfEmpty()
                                                    where a.Doc_For == "Vendor"
                                                    select new M_Vendor_Docs
                                                    {
                                                        Vendor_Doc_Id = a.PKID,
                                                        Vendor_Doc_Name = a.Doc_Name,
                                                        Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                        Vendor_Doc_Path = b.Document_Path
                                                    }).ToList();

                //3 Bind Vendor Bank Details
                Vendor_Details.M_Vendor_BankList = (from a in entity.RLT_Vendor_Bank_Details
                                                    join c in entity.RLT_BANK_NAMES on a.Bank_Name_PKID equals c.PKID
                                                    where a.Vendor_Ref_PKID == VendorId
                                                    select new M_Vendor_Bank_Details
                                                    {
                                                        Bank_Name = c.Bank_Name,
                                                        Account_Number = a.Account_Number,
                                                        IFSC_CODE = a.IFSC_CODE,
                                                        Account_Holder_Name = a.Account_Holder_Name,
                                                        Bank_Status = a.Bank_Status

                                                    }).ToList();

            }
            return View(Vendor_Details);
        }

        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult VendorDocs()
        {
            M_Vendor_Details Vendor_Details = new M_Vendor_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Vendor");
                int VendorId = Convert.ToInt16(DecData);

                Vendor_Details = (from a in entity.RLT_Vendor_Details
                                  where a.PKID == VendorId
                                  select new M_Vendor_Details
                                  {
                                      Vendor_Company_Name = a.Vendor_Company_Name,
                                      PKID = a.PKID
                                  }).FirstOrDefault();


                Vendor_Details.M_Vendor_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                                    join b in entity.RLT_Vendor_Docs on
                                                     new
                                                     { Vendor_Doc_Id = a.PKID, VendorIdNew = VendorId } equals
                                               new { Vendor_Doc_Id = b.Vendor_Doc_Id, VendorIdNew = b.Vendor_Ref_PKID }

                                                   into ps
                                                    from b in ps.DefaultIfEmpty()
                                                    where a.Doc_For == "Vendor"
                                                    select new M_Vendor_Docs
                                                    {
                                                        Vendor_Doc_Id = a.PKID,
                                                        Vendor_Doc_Name = a.Doc_Name,
                                                        Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                        Vendor_Doc_Path = b.Document_Path
                                                    }).ToList();

            }
            return View(Vendor_Details);
        }


        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddVendorDocs(M_Vendor_Details obj)
        {
            byte[] uploadedFile;
            string strFileExt = "";
            foreach (var Items in obj.M_Vendor_DocsList)
            {

                #region Vendor docs
                String Document_Path = Items.Vendor_Doc_Path;
                if (Items.Vendor_Doc_UP != null)
                {
                    uploadedFile = new byte[Items.Vendor_Doc_UP.InputStream.Length];
                    Items.Vendor_Doc_UP.InputStream.Read(uploadedFile, 0, uploadedFile.Length);
                    strFileExt = Items.Vendor_Doc_UP.FileName.Substring(Items.Vendor_Doc_UP.FileName.LastIndexOf(".")).Trim();
                    string path = obj.PKID + "_" + DateTime.Now.ToString("dd-MM-yyyy-HH-mm-ss-fff").Trim() + strFileExt.Trim();
                    string Savepath = Server.MapPath("~/Docs/VendorDocs/") + path;
                    // string path = Path.Combine(Server.MapPath("~/Docs/VendorDocs"), Path.GetFileName(Items.Vendor_Doc_UP.FileName));
                    System.IO.File.WriteAllBytes(Savepath.Trim(), uploadedFile);
                    Document_Path = path;

                }
                #endregion

                var v = (from a in entity.RLT_Vendor_Docs where a.Vendor_Doc_Id == Items.Vendor_Doc_Id && a.Vendor_Ref_PKID == obj.PKID select a);
                if (v.Count() > 0)
                {
                    //var vdtlsList = (from a in entity.RLT_Vendor_Docs where a.Vendor_Doc_Id == Items.Vendor_Doc_Id && a.Vendor_Ref_PKID == obj.PKID select a);

                    foreach (RLT_Vendor_Docs vdtls in v)
                    {
                        vdtls.Is_Verified = Items.Is_Verified;
                        vdtls.Document_Path = Document_Path;
                        vdtls.Last_Modified_By = UserServices.getCurrentUserId();
                        vdtls.Last_Modified_Date = DateTime.Now;
                    }
                    entity.SaveChanges();
                }
                else
                {
                    RLT_Vendor_Docs vdtls = new RLT_Vendor_Docs();
                    vdtls.Is_Verified = Items.Is_Verified;
                    vdtls.Vendor_Doc_Id = Items.Vendor_Doc_Id;
                    vdtls.Vendor_Ref_PKID = obj.PKID;
                    vdtls.Document_Path = Document_Path;
                    vdtls.CreatedBy = UserServices.getCurrentUserId();
                    vdtls.Created_Date = DateTime.Now;
                    vendorDocsRepo.Add(vdtls);
                    vendorDocsRepo.Save();
                }

            }
            return RedirectToAction("VendorManager");
        }

        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult VendorBankDetails()
        {
            var BankList = entity.RLT_BANK_NAMES.ToList();
            ViewBag.BankList = BankList;

            M_Vendor_Bank_Details Vendor_Bank_Details = new M_Vendor_Bank_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Vendor");
                int VendorId = Convert.ToInt16(DecData);

                Vendor_Bank_Details = (from a in entity.RLT_Vendor_Details
                                       where a.PKID == VendorId
                                       select new M_Vendor_Bank_Details
                                       {
                                           Vendor_Company_Name = a.Vendor_Company_Name,
                                           Vendor_Ref_PKID = a.PKID
                                       }).FirstOrDefault();


                var VendorBankList = (from a in entity.RLT_Vendor_Bank_Details
                                      join c in entity.RLT_BANK_NAMES on a.Bank_Name_PKID equals c.PKID
                                      where a.Vendor_Ref_PKID == VendorId
                                      select new M_Vendor_Bank_Details
                                      {
                                          PKID = a.PKID,
                                          Vendor_Ref_PKID = a.Vendor_Ref_PKID,
                                          Bank_Name_PKID = a.Bank_Name_PKID,
                                          Bank_Name = c.Bank_Name,
                                          Account_Number = a.Account_Number,
                                          IFSC_CODE = a.IFSC_CODE,
                                          Account_Holder_Name = a.Account_Holder_Name,
                                          Bank_Status = a.Bank_Status,
                                          Is_active = a.Is_active
                                      }).ToList();
                ViewBag.VendorBankList = VendorBankList;
            }
            return View(Vendor_Bank_Details);
        }

        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult AddVendorBankDetails(M_Vendor_Bank_Details Items)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {

                var vdtlsList = (from a in entity.RLT_Vendor_Bank_Details where a.PKID == Items.PKID select a);
                foreach (RLT_Vendor_Bank_Details vdtls in vdtlsList)
                {
                    // vdtls.Vendor_Ref_PKID = Items.Vendor_Ref_PKID;
                    vdtls.Bank_Name_PKID = Items.Bank_Name_PKID;
                    vdtls.IFSC_CODE = Items.IFSC_CODE;
                    vdtls.Account_Number = Items.Account_Number;
                    vdtls.Account_Holder_Name = Items.Account_Holder_Name;
                    vdtls.Bank_Status = Items.Bank_Status;
                    vdtls.Is_active = Items.Is_active;
                    vdtls.Lastmodified_By = UserServices.getCurrentUserId();
                    vdtls.LastModified_Date = DateTime.Now;
                }
                entity.SaveChanges();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                var vdtlsList = (from a in entity.RLT_Vendor_Bank_Details where a.PKID == Items.PKID select a);
                foreach (RLT_Vendor_Bank_Details vdtls in vdtlsList)
                {
                    vdtls.Is_active = Items.Is_active;
                    vdtls.Lastmodified_By = UserServices.getCurrentUserId();
                    vdtls.LastModified_Date = DateTime.Now;
                }
                entity.SaveChanges();
                Id = 3;
            }
            else
            {
                RLT_Vendor_Bank_Details vdtls = new RLT_Vendor_Bank_Details();
                vdtls.Vendor_Ref_PKID = Items.Vendor_Ref_PKID;
                vdtls.Bank_Name_PKID = Items.Bank_Name_PKID;
                vdtls.IFSC_CODE = Items.IFSC_CODE;
                vdtls.Account_Number = Items.Account_Number;
                vdtls.Account_Holder_Name = Items.Account_Holder_Name;
                vdtls.Bank_Status = Items.Bank_Status;
                vdtls.Is_active = true;
                vdtls.CreatedBy = UserServices.getCurrentUserId();
                vdtls.Created_Date = DateTime.Now;
                entity.RLT_Vendor_Bank_Details.Add(vdtls);
                entity.SaveChanges();
                Id = 1;

            }

            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion end 1 Vendor add/edit/delete/activeDeactive/view 


        #region 2 Car Owner Page
        public ActionResult CarOwnerManager()
        {
            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;

            var CarOwnerList = (from a in entity.RLT_CAR_OWNER_DETAILS
                                join c in entity.RLT_Vendor_Details on a.Vendor_PKID equals c.PKID
                                select new M_CarOwner
                                {
                                    PKID = a.PKID,
                                    Vendor_PKID = a.Vendor_PKID,
                                    Vendor_Name = c.Vendor_Company_Name,
                                    Car_Owner_Name = a.Car_Owner_Name,
                                    Car_Owner_Email = a.Car_Owner_Email,
                                    Car_Owner_Photo = a.Car_Owner_Photo,
                                    Car_Owner_Phone1 = a.Car_Owner_Phone1,
                                    Car_Owner_Phone2 = a.Car_Owner_Phone2,
                                    Car_Owner_DL = a.Car_Owner_DL,
                                    Car_Owner_Aadhaar = a.Car_Owner_Aadhaar,
                                    Car_Owner_Address = a.Car_Owner_Address,
                                    Is_Active = a.Is_Active

                                }).ToList();

            ViewBag.CarOwnerList = CarOwnerList;

            var model = new M_CarOwner();
            return View(model);
        }


        [HttpPost]
        [RoleBaseAuthentication("CarOwnerManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddCarOwner(M_CarOwner obj, HttpPostedFileBase CarOwnerPhoto)
        {
            var vdtls = map.CarOwnerDetails(obj);
            int Id = 0;
            if (CarOwnerPhoto != null)
            {
                string filename = Guid.NewGuid() + "_" + CarOwnerPhoto.FileName;
                string Savepath = Server.MapPath("~/Docs/CarOwnerPhoto/" + filename);
                CarOwnerPhoto.SaveAs(Savepath);
                vdtls.Car_Owner_Photo = filename;
            }
            vdtls.Created_BY = UserServices.getCurrentUserId();
            vdtls.Created_Date = DateTime.Now;
            CarOwnerDetailsRepo.Add(vdtls);
            CarOwnerDetailsRepo.Save();
            Id = 1;
            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("CarOwnerManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult CarOwnerView()
        {
            M_CarOwner CarOwner = new M_CarOwner();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "CarOwner");
                int CarOwnerId = Convert.ToInt16(DecData);

                CarOwner = (from a in entity.RLT_CAR_OWNER_DETAILS
                            join c in entity.RLT_Vendor_Details on a.Vendor_PKID equals c.PKID
                            where a.PKID == CarOwnerId
                            select new M_CarOwner
                            {
                                Vendor_Name = c.Vendor_Company_Name,
                                Car_Owner_Name = a.Car_Owner_Name,
                                Car_Owner_Email = a.Car_Owner_Email,
                                Car_Owner_Photo = a.Car_Owner_Photo,
                                Car_Owner_Phone1 = a.Car_Owner_Phone1,
                                Car_Owner_Phone2 = a.Car_Owner_Phone2,
                                Car_Owner_DL = a.Car_Owner_DL,
                                Car_Owner_Aadhaar = a.Car_Owner_Aadhaar,
                                Car_Owner_Address = a.Car_Owner_Address,
                            }).FirstOrDefault();


            }
            return View(CarOwner);

        }

        [HttpGet]
        [RoleBaseAuthentication("CarOwnerManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarOwnerEdit(int pkid)
        {
            bool status = false;
            var CarOwnerDetail = (from a in entity.RLT_CAR_OWNER_DETAILS
                                  join c in entity.RLT_Vendor_Details on a.Vendor_PKID equals c.PKID
                                  where a.PKID == pkid
                                  select new M_CarOwner
                                  {
                                      PKID = a.PKID,
                                      Vendor_PKID = a.Vendor_PKID,
                                      Vendor_Name = c.Vendor_Company_Name,
                                      Car_Owner_Name = a.Car_Owner_Name,
                                      Car_Owner_Email = a.Car_Owner_Email,
                                      Car_Owner_Photo = a.Car_Owner_Photo,
                                      Car_Owner_Phone1 = a.Car_Owner_Phone1,
                                      Phone1_IsWhatsup = a.Phone1_IsWhatsup == null ? "no" : "yes",
                                      Car_Owner_Phone2 = a.Car_Owner_Phone2,
                                      Car_Owner_DL = a.Car_Owner_DL,
                                      Car_Owner_Aadhaar = a.Car_Owner_Aadhaar,
                                      Car_Owner_Address = a.Car_Owner_Address,
                                      Is_Active = a.Is_Active
                                  }).FirstOrDefault();
            if (CarOwnerDetail != null) return Json(CarOwnerDetail, JsonRequestBehavior.AllowGet);
            return Json(status, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarOwnerManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarOwnerEdit(M_CarOwner obj, HttpPostedFileBase CarOwnerPhoto)
        {
            var retriveVdtls = entity.RLT_CAR_OWNER_DETAILS.Where(x => x.PKID == obj.PKID).FirstOrDefault();
            var vdtls = map.CarOwnerDetails(obj);
            int Id = 0;
            if (CarOwnerPhoto != null)
            {
                string filename = Guid.NewGuid() + "_" + CarOwnerPhoto.FileName;
                string Savepath = Server.MapPath("~/Docs/CarOwnerPhoto/" + filename);
                CarOwnerPhoto.SaveAs(Savepath);
                vdtls.Car_Owner_Photo = filename;
            }
            vdtls.Created_BY = retriveVdtls.Created_BY;
            vdtls.Created_Date = retriveVdtls.Created_Date;
            vdtls.Updated_BY = UserServices.getCurrentUserId();
            vdtls.Update_Date = DateTime.Now;
            CarOwnerDetailsRepo.Edit(vdtls);
            CarOwnerDetailsRepo.Save();
            Id = 2;
            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarOwnerManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarOwnerActiveDeactive(int id = 0)
        {
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLT_CAR_OWNER_DETAILS.Where(x => x.PKID == id).FirstOrDefault();
                if (data != null)
                {
                    data.Is_Active = data.Is_Active != null ? data.Is_Active == true ? false : true : false;
                    data.Updated_BY = UserServices.getCurrentUserId();
                    data.Update_Date = DateTime.Now;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region 3 Car Page

        public ActionResult CarManager()
        {
            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;


            var CarCompanyList = entity.RLT_CAR_COMPANY.ToList();
            ViewBag.CarCompanyList = CarCompanyList;

            var CarModelList = entity.RLT_CAR_MODEL.ToList();
            ViewBag.CarModelList = CarModelList;

            var CityList = entity.RLT_CITY.ToList();
            ViewBag.CityList = CityList;



            var CarList = (from a in entity.RLT_CAR_DETAILS
                           join c in entity.RLT_CAR_OWNER_DETAILS on a.Car_Owner_PKID equals c.PKID
                           join d in entity.RLT_CAR_COMPANY on a.Car_Company_PKID equals d.PKID
                           join e in entity.RLT_CAR_MODEL on a.Car_Model_PKID equals e.PKID
                           join f in entity.RLT_CITY on a.Car_Registered_City_PKID equals f.PKID
                           join g in entity.RLT_Vendor_Details on a.Vendor_PKID equals g.PKID
                           select new M_Car_Details
                           {
                               PKID = a.PKID,
                               Vendor_PKID = a.Vendor_PKID,
                               Car_Owner_PKID = a.Car_Owner_PKID,
                               Car_Number = a.Car_Number,
                               Car_Purchase_Year = a.Car_Purchase_Year,
                               Car_Manufacturing_Year = a.Car_Manufacturing_Year,
                               Car_Company_PKID = a.Car_Company_PKID,
                               Car_Model_PKID = a.Car_Model_PKID,
                               Car_Registered_Document = a.Car_Registered_Document,
                               Car_Registered_City_PKID = a.Car_Registered_City_PKID,
                               Car_Fuel_Type_Status = a.Car_Fuel_Type_Status,
                               Car_Owner_Name = c.Car_Owner_Name,
                               Company_Name = d.Company_Name,
                               Car_Model_Name = e.Car_Model_Name,
                               City_Name = f.City_Name,
                               Vendor_Name = g.Vendor_Company_Name,
                               Is_Active = a.Is_Active

                           }).ToList();
            ViewBag.CarList = CarList;

            var model = new M_Car_Details();
            return View(model);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddCar(M_Car_Details obj)
        {
            var vdtls = map.CarDetails(obj);
            vdtls.Created_BY = UserServices.getCurrentUserId();
            vdtls.Created_Date = DateTime.Now;
            CarDetailsRepo.Add(vdtls);
            CarDetailsRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult CarDocs()
        {
            M_Car_Details Car_Details = new M_Car_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Car");
                int CarId = Convert.ToInt16(DecData);
                Car_Details = (from a in entity.RLT_CAR_DETAILS
                               where a.PKID == CarId
                               select new M_Car_Details
                               {
                                   Car_Number = a.Car_Number,
                                   PKID = a.PKID
                               }).FirstOrDefault();


                Car_Details.M_Car_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                              join b in entity.RLT_CAR_DOCS on
                                             new { Car_Doc_Id = a.PKID, CarId = CarId } equals
                                             new { Car_Doc_Id = b.Car_Doc_Id, CarId = b.Car_PKID }
                                             into ps
                                              from b in ps.DefaultIfEmpty()
                                              where a.Doc_For == "Car"
                                              select new M_Car_Docs
                                              {
                                                  Car_Doc_Id = a.PKID,
                                                  Car_Doc_Name = a.Doc_Name,
                                                  Car_Doc_EndDate = b.Car_Doc_EndDate,
                                                  Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                  Car_Doc_Path = b.Document_Path
                                              }).ToList();


                //if (Int16.TryParse(QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Car"), out Int16 carId))
                //{
                //    var carDetails = entity.RLT_CAR_DETAILS.Where(x => x.PKID == carId).Select(x => new M_Car_Details
                //    {
                //        Car_Number = x.Car_Number,
                //        PKID = x.PKID
                //    }).FirstOrDefault();
                //    if (carDetails != null)
                //    {
                //        var docsForCar = entity.RLT_DOCUMNETS_NAME.Where(x => x.Doc_For.ToLower() == "car").ToList();
                //        carDetails.M_Car_DocsList = entity.RLT_CAR_DOCS.Where(x => x.Car_PKID == carId)
                //            .Join(entity.RLT_DOCUMNETS_NAME, x => x.Car_Doc_Id, d => d.PKID, (x, d) => new M_Car_Docs
                //            {
                //                Car_Doc_Id = x.Car_Doc_Id,
                //                Car_Doc_Name = d.Doc_Name,
                //                Car_Doc_EndDate = x.Car_Doc_EndDate,
                //                Is_Verified = x.Is_Verified,
                //                Car_Doc_Path = x.Document_Path
                //            }).ToList();

                //    }
                //}
            }
            return View(Car_Details);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddCarDocs(M_Car_Details obj)
        {
            byte[] uploadedFile;
            string strFileExt = "";
            foreach (var Items in obj.M_Car_DocsList)
            {

                #region Vendor docs
                String Document_Path = Items.Car_Doc_Path;
                if (Items.Car_Doc_UP != null)
                {
                    uploadedFile = new byte[Items.Car_Doc_UP.InputStream.Length];
                    Items.Car_Doc_UP.InputStream.Read(uploadedFile, 0, uploadedFile.Length);
                    strFileExt = Items.Car_Doc_UP.FileName.Substring(Items.Car_Doc_UP.FileName.LastIndexOf(".")).Trim();
                    string path = obj.PKID + "_" + DateTime.Now.ToString("dd-MM-yyyy-HH-mm-ss-fff").Trim() + strFileExt.Trim();
                    string Savepath = Server.MapPath("~/Docs/CarDocs/") + path;
                    System.IO.File.WriteAllBytes(Savepath.Trim(), uploadedFile);
                    Document_Path = path;

                }
                #endregion

                var v = (from a in entity.RLT_CAR_DOCS where a.Car_Doc_Id == Items.Car_Doc_Id && a.Car_PKID == obj.PKID select a);
                var v1 = entity.RLT_CAR_DOCS.Where(x => x.Car_Doc_Id == Items.Car_Doc_Id && x.Car_PKID == obj.PKID).ToList();
                if (v.Count() > 0)
                {
                    var vdtlsList = (from a in entity.RLT_CAR_DOCS where a.Car_Doc_Id == Items.Car_Doc_Id && a.Car_PKID == obj.PKID select a);

                    foreach (RLT_CAR_DOCS vdtls in vdtlsList)
                    {
                        vdtls.Is_Verified = Items.Is_Verified;
                        vdtls.Document_Path = Document_Path;
                        vdtls.Car_Doc_EndDate = Items.Car_Doc_EndDate;
                        vdtls.CreatedBy = UserServices.getCurrentUserId();
                        vdtls.Created_Date = DateTime.Now;
                    }
                    entity.SaveChanges();
                }
                else
                {
                    RLT_CAR_DOCS vdtls = new RLT_CAR_DOCS();
                    vdtls.Is_Verified = Items.Is_Verified;
                    vdtls.Car_Doc_Id = Items.Car_Doc_Id;
                    vdtls.Car_PKID = obj.PKID;
                    vdtls.Document_Path = Document_Path;
                    vdtls.Car_Doc_EndDate = Items.Car_Doc_EndDate;
                    vdtls.CreatedBy = UserServices.getCurrentUserId();
                    vdtls.Created_Date = DateTime.Now;
                    entity.RLT_CAR_DOCS.Add(vdtls);
                    entity.SaveChanges();
                }

            }
            return RedirectToAction("CarManager");
        }

        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult CarView()
        {
            M_Car_Details Car_Details = new M_Car_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Car");
                int CarId = Convert.ToInt16(DecData);

                Car_Details = (from a in entity.RLT_CAR_DETAILS
                               join c in entity.RLT_CAR_OWNER_DETAILS on a.Car_Owner_PKID equals c.PKID
                               join d in entity.RLT_CAR_COMPANY on a.Car_Company_PKID equals d.PKID
                               join e in entity.RLT_CAR_MODEL on a.Car_Model_PKID equals e.PKID
                               join f in entity.RLT_CITY on a.Car_Registered_City_PKID equals f.PKID
                               join g in entity.RLT_Vendor_Details on a.Vendor_PKID equals g.PKID
                               where a.PKID == CarId
                               select new M_Car_Details
                               {
                                   Vendor_Name = g.Vendor_Company_Name,
                                   Car_Owner_Name = c.Car_Owner_Name,
                                   Company_Name = d.Company_Name,
                                   Car_Model_Name = e.Car_Model_Name,
                                   Car_Number = a.Car_Number,
                                   Car_Purchase_Year = a.Car_Purchase_Year,
                                   Car_Manufacturing_Year = a.Car_Manufacturing_Year,
                                   City_Name = f.City_Name,
                                   Car_Fuel_Type_Status = a.Car_Fuel_Type_Status,

                               }).FirstOrDefault();




                //2 Bind Car Driver Documents
                Car_Details.M_Car_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                              join b in entity.RLT_CAR_DOCS on
                                             new { Car_Doc_Id = a.PKID, CarId = CarId } equals
                                             new { Car_Doc_Id = b.Car_Doc_Id, CarId = b.Car_PKID }
                                             into ps
                                              from b in ps.DefaultIfEmpty()
                                              where a.Doc_For == "Car"
                                              select new M_Car_Docs
                                              {
                                                  Car_Doc_Id = a.PKID,
                                                  Car_Doc_Name = a.Doc_Name,
                                                  Car_Doc_EndDate = b.Car_Doc_EndDate,
                                                  Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                  Car_Doc_Path = b.Document_Path
                                              }).ToList();
            }
            return View(Car_Details);

        }

        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarEdit(int pkid)
        {
            var carDetails = entity.RLT_CAR_DETAILS.Where(x => x.PKID == pkid).FirstOrDefault();
            return carDetails != null ? Json(carDetails, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarEdit(M_Car_Details obj)
        {
            var vdtls = map.CarDetails(obj);
            var carDetails = entity.RLT_CAR_DETAILS.Where(x => x.PKID == obj.PKID).FirstOrDefault();
            vdtls.Created_BY = carDetails.Created_BY;
            vdtls.Created_Date = carDetails.Created_Date;
            vdtls.Updated_BY = UserServices.getCurrentUserId();
            vdtls.Update_Date = DateTime.Now;
            CarDetailsRepo.Edit(vdtls);
            CarDetailsRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarActivateDeactivate(int pkid)
        {
            var carDetails = entity.RLT_CAR_DETAILS.Where(x => x.PKID == pkid).FirstOrDefault();
            if (carDetails != null)
            {
                carDetails.Is_Active = carDetails.Is_Active != null ? (carDetails.Is_Active == true ? false : true) : false;
                carDetails.Update_Date = DateTime.Now;
                carDetails.Updated_BY = UserServices.getCurrentUserId();
                CarDetailsRepo.Edit(carDetails);
                CarDetailsRepo.Save();
                return Json(true, JsonRequestBehavior.AllowGet);
            }
            return Json(false, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 4 Car Driver Page

        public ActionResult CarDriverManager()
        {
            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;

            var carOwnerList = entity.RLT_CAR_OWNER_DETAILS.ToList();
            ViewBag.carOwnerList = carOwnerList;


            var carList = entity.RLT_CAR_DETAILS.ToList();
            ViewBag.carList = carList;

            var StateList = entity.RLT_STATE.ToList();
            ViewBag.StateList = StateList;

            var CarDriverList = (from a in entity.RLT_CAR_DRIVER_DETAILS
                                 join b in entity.RLT_Vendor_Details on a.Vendor_PKID equals b.PKID


                                 select new M_CarDriver_Details
                                 {
                                     PKID = a.PKID,
                                     Vendor_Name = b.Vendor_Company_Name,
                                     Vendor_PKID = a.Vendor_PKID,
                                     Car_Owner_PKID = a.Car_Owner_PKID,
                                     Car_PKID = a.Car_PKID,
                                     Driver_Name = a.Driver_Name,
                                     Driver_Photo = a.Driver_Photo,
                                     Driver_DOB = a.Driver_DOB,
                                     Drive_Phone_1 = a.Drive_Phone_1,
                                     Drive_Phone_2 = a.Drive_Phone_2,
                                     Driver_DL = a.Driver_DL,
                                     Driver_Aadhaar = a.Driver_Aadhaar,
                                     Driver_Father_Name = a.Driver_Father_Name,
                                     Driver_Mother_Name = a.Driver_Mother_Name,
                                     Driver_Marrital_Status = a.Driver_Marrital_Status,
                                     Driver_Gender = a.Driver_Gender,
                                     Driver_Current_Address = a.Driver_Current_Address,
                                     Driver_Permanent_Address = a.Driver_Permanent_Address,
                                     Driver_Smoking_Status = a.Driver_Smoking_Status,
                                     Driver_Drinking_Status = a.Driver_Drinking_Status,
                                     Driver_Eating_Type = a.Driver_Eating_Type,
                                     Is_Active = a.Is_Active,
                                     Driver_Current_Address1 = a.Driver_Current_Address1,
                                     Driver_Current_Address2 = a.Driver_Current_Address2,
                                     Driver_Current_Address_State = a.Driver_Current_Address_State,
                                     Driver_Current_Address_City = a.Driver_Current_Address_City,

                                     Driver_Permanent_Address1 = a.Driver_Permanent_Address1,
                                     Driver_Permanent_Address2 = a.Driver_Permanent_Address2,
                                     Driver_Permanent_Address_State = a.Driver_Permanent_Address_State,
                                     Driver_Permanent_Address_City = a.Driver_Permanent_Address_City,
                                     Special_Remarking = a.Special_Remarking,

                                     Phone_1_IsWhatsup = a.Phone_1_IsWhatsup,
                                     Driver_Current_Permanent_Same = a.Driver_Current_Permanent_Same,
                                     Driver_Religion = a.Driver_Religion,
                                     Driver_Email = a.Driver_Email
                                 }).ToList();
            ViewBag.CarDriverList = CarDriverList;
            var model = new M_CarDriver_Details();
            return View(model);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddCarDriver(M_CarDriver_Details obj)
        {
            var vdtls = map.CarDriverDetails(obj);
            if (!string.IsNullOrEmpty(obj.Driver_Photo))
            {
                var file = Request.Files[0];
                if (file != null && file.ContentLength > 0)
                {
                    string path = Guid.NewGuid() + "_" + file.FileName;
                    string Savepath = Server.MapPath("~/Docs/DriverPhoto/") + path;
                    file.SaveAs(Savepath);
                    vdtls.Driver_Photo = path;
                }
            }
            vdtls.Created_BY = UserServices.getCurrentUserId();
            vdtls.Created_Date = DateTime.Now;
            CarDriverDetailsRepo.Add(vdtls);
            CarDriverDetailsRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarDriverManagerEdit(int pkid)
        {
            var carDriverDetails = entity.RLT_CAR_DRIVER_DETAILS.Where(x => x.PKID == pkid).FirstOrDefault();
            return carDriverDetails != null
                ? Json(carDriverDetails, JsonRequestBehavior.AllowGet)
                : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarDriverManagerEdit(M_CarDriver_Details obj)
        {
            var retriveVdtls = entity.RLT_CAR_DRIVER_DETAILS.Where(x => x.PKID == obj.PKID).FirstOrDefault();
            var vdtls = map.CarDriverDetails(obj);
            if (!string.IsNullOrEmpty(obj.Driver_Photo))
            {
                HttpPostedFileBase file = Request.Files[0];
                if (file != null && file.ContentLength > 0)
                {
                    string path = Guid.NewGuid() + "_" + file.FileName;
                    string Savepath = Server.MapPath("~/Docs/DriverPhoto/") + path;
                    ImageWaterMark ob = new ImageWaterMark();
                    ob.AddImageWaterMark(Savepath, file);
                    vdtls.Driver_Photo = path;
                }
            }
            vdtls.Driver_Photo = vdtls.Driver_Photo == null ? retriveVdtls.Driver_Photo : vdtls.Driver_Photo;
            vdtls.Created_BY = retriveVdtls.Created_BY;
            vdtls.Created_Date = retriveVdtls.Created_Date;
            vdtls.Updated_Date = DateTime.Now;
            vdtls.Updated_BY = UserServices.getCurrentUserId();
            CarDriverDetailsRepo.Edit(vdtls);
            CarDriverDetailsRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult CarDriverActivateDeactivated(int pkid)
        {
            bool status = false;
            var carDriverDetails = entity.RLT_CAR_DRIVER_DETAILS.Where(x => x.PKID == pkid).FirstOrDefault();
            if (carDriverDetails != null)
            {
                carDriverDetails.Is_Active = carDriverDetails.Is_Active != null ? (carDriverDetails.Is_Active == true ? false : true) : false;
                entity.SaveChanges();
                status = true;
            }
            return Json(status, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult CarDriverDocs()
        {
            M_CarDriver_Details CarDriver_Details = new M_CarDriver_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "CarDriver");
                int CarDriverId = Convert.ToInt16(DecData);

                CarDriver_Details = (from a in entity.RLT_CAR_DRIVER_DETAILS
                                     where a.PKID == CarDriverId
                                     select new M_CarDriver_Details
                                     {
                                         Driver_Name = a.Driver_Name,
                                         PKID = a.PKID
                                     }).FirstOrDefault();
                CarDriver_Details.M_CarDriver_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                                          join b in entity.RLT_CAR_DRIVER_DOCS on
                                                           new
                                                           { CarDriver_Doc_Id = a.PKID, CarDriverIdNew = CarDriverId } equals
                                                     new { CarDriver_Doc_Id = b.CarDriver_Doc_Id, CarDriverIdNew = b.CarDriver_PKID }

                                                         into ps
                                                          from b in ps.DefaultIfEmpty()
                                                          where a.Doc_For == "Car Driver"
                                                          select new M_CarDriver_Docs
                                                          {
                                                              CarDriver_Doc_Id = a.PKID,
                                                              CarDriver_Doc_Name = a.Doc_Name,
                                                              Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                              CarDriver_Doc_Path = b.Document_Path
                                                          }).ToList();

            }
            return View(CarDriver_Details);
        }
        [HttpPost]

        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddCarDriverDocs(M_CarDriver_Details obj)
        {
            byte[] uploadedFile;
            string strFileExt = "";
            foreach (var Items in obj.M_CarDriver_DocsList)
            {
                #region Car Driver docs
                String Document_Path = Items.CarDriver_Doc_Path;
                if (Items.CarDriver_Doc_UP != null)
                {
                    uploadedFile = new byte[Items.CarDriver_Doc_UP.InputStream.Length];
                    Items.CarDriver_Doc_UP.InputStream.Read(uploadedFile, 0, uploadedFile.Length);
                    strFileExt = Items.CarDriver_Doc_UP.FileName.Substring(Items.CarDriver_Doc_UP.FileName.LastIndexOf(".")).Trim();
                    string path = obj.PKID + "_" + DateTime.Now.ToString("dd-MM-yyyy-HH-mm-ss-fff").Trim() + strFileExt.Trim();
                    string Savepath = Server.MapPath("~/Docs/DriverDocs/") + path;
                    System.IO.File.WriteAllBytes(Savepath.Trim(), uploadedFile);
                    Document_Path = path;
                }
                #endregion 
                var v = (from a in entity.RLT_CAR_DRIVER_DOCS where a.CarDriver_Doc_Id == Items.CarDriver_Doc_Id && a.CarDriver_PKID == obj.PKID select a);
                if (v.Count() > 0)
                {
                    var vdtlsList = (from a in entity.RLT_CAR_DRIVER_DOCS where a.CarDriver_Doc_Id == Items.CarDriver_Doc_Id && a.CarDriver_PKID == obj.PKID select a);
                    foreach (RLT_CAR_DRIVER_DOCS vdtls in vdtlsList)
                    {
                        vdtls.Is_Verified = Items.Is_Verified;
                        vdtls.Document_Path = Document_Path;
                        vdtls.CarDriver_Doc_EndDate = Items.CarDriver_Doc_EndDate;
                    }
                    entity.SaveChanges();
                }
                else
                {
                    RLT_CAR_DRIVER_DOCS vdtls = new RLT_CAR_DRIVER_DOCS();
                    vdtls.Is_Verified = Items.Is_Verified;
                    vdtls.CarDriver_Doc_Id = Items.CarDriver_Doc_Id;
                    vdtls.CarDriver_PKID = obj.PKID;
                    vdtls.CarDriver_Doc_EndDate = Items.CarDriver_Doc_EndDate;
                    vdtls.Document_Path = Document_Path;
                    entity.RLT_CAR_DRIVER_DOCS.Add(vdtls);
                    entity.SaveChanges();
                }
            }
            return RedirectToAction("CarDriverManager");
        }

        [RoleBaseAuthentication("CarDriverManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult CarDriverView()
        {
            M_CarDriver_Details CarDriver_Details = new M_CarDriver_Details();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "CarDriver");
                int CarDriverId = Convert.ToInt16(DecData);

                CarDriver_Details = (from a in entity.RLT_CAR_DRIVER_DETAILS
                                     join b in entity.RLT_Vendor_Details on a.Vendor_PKID equals b.PKID
                                     join c in entity.RLT_CAR_OWNER_DETAILS on a.Car_Owner_PKID equals c.PKID
                                     join d in entity.RLT_CAR_DETAILS on a.Car_PKID equals d.PKID

                                     join e in entity.RLT_STATE on a.Driver_Current_Address_State equals e.PKID into e1
                                     from e in e1.DefaultIfEmpty()
                                     join f in entity.RLT_CITY on a.Driver_Current_Address_City equals f.PKID into f1
                                     from f in f1.DefaultIfEmpty()

                                     join g in entity.RLT_STATE on a.Driver_Permanent_Address_State equals g.PKID into g1
                                     from g in g1.DefaultIfEmpty()
                                     join h in entity.RLT_CITY on a.Driver_Permanent_Address_City equals h.PKID into h1
                                     from h in h1.DefaultIfEmpty()




                                     where a.PKID == CarDriverId
                                     select new M_CarDriver_Details
                                     {
                                         Vendor_Name = b.Vendor_Company_Name,
                                         Car_Owner_Name = c.Car_Owner_Name,
                                         Car_Name = d.Car_Number,
                                         Driver_Name = a.Driver_Name,
                                         Driver_Photo = a.Driver_Photo,
                                         Driver_DOB = a.Driver_DOB,
                                         Drive_Phone_1 = a.Drive_Phone_1,
                                         Drive_Phone_2 = a.Drive_Phone_2,
                                         Driver_DL = a.Driver_DL,
                                         Driver_Aadhaar = a.Driver_Aadhaar,
                                         Driver_Father_Name = a.Driver_Father_Name,
                                         Driver_Mother_Name = a.Driver_Mother_Name,
                                         Driver_Marrital_Status = a.Driver_Marrital_Status,
                                         Driver_Gender = a.Driver_Gender,
                                         //Driver_Current_Address = a.Driver_Current_Address,
                                         //Driver_Permanent_Address = a.Driver_Permanent_Address,
                                         Driver_Smoking_Status = a.Driver_Smoking_Status,
                                         Driver_Drinking_Status = a.Driver_Drinking_Status,
                                         Driver_Eating_Type = a.Driver_Eating_Type,
                                         Driver_Religion = a.Driver_Religion,

                                         Driver_Current_Address1 = a.Driver_Current_Address1,
                                         Driver_Current_Address2 = a.Driver_Current_Address2,
                                         Driver_Permanent_Address1 = a.Driver_Permanent_Address1,
                                         Driver_Permanent_Address2 = a.Driver_Permanent_Address2,
                                         Driver_Current_Address_State_Name = e.State_Name,
                                         Driver_Current_Address_City_Name = f.City_Name,
                                         Driver_Permanent_Address_State_Name = g.State_Name,
                                         Driver_Permanent_Address_City_Name = h.City_Name,
                                         Special_Remarking = a.Special_Remarking


                                     }).FirstOrDefault();




                //2 Bind Car Driver Documents
                CarDriver_Details.M_CarDriver_DocsList = (from a in entity.RLT_DOCUMNETS_NAME
                                                          join b in entity.RLT_CAR_DRIVER_DOCS on
                                                           new
                                                           { Vendor_Doc_Id = a.PKID, CarDriverIdNew = CarDriverId } equals
                                                     new { Vendor_Doc_Id = b.CarDriver_Doc_Id, CarDriverIdNew = b.CarDriver_PKID }

                                                         into ps
                                                          from b in ps.DefaultIfEmpty()
                                                          where a.Doc_For == "Car Driver"
                                                          select new M_CarDriver_Docs
                                                          {
                                                              CarDriver_Doc_Id = a.PKID,
                                                              CarDriver_Doc_Name = a.Doc_Name,
                                                              CarDriver_Doc_EndDate = b.CarDriver_Doc_EndDate,
                                                              Is_Verified = b.Is_Verified == null ? false : b.Is_Verified,
                                                              CarDriver_Doc_Path = b.Document_Path
                                                          }).ToList();
            }
            return View(CarDriver_Details);

        }

        #endregion

        #region 5 BecomeDriverRequest

        public ActionResult BecomeDriverRequest()
        {
            M_Driver_Enquiries m_Driver_Enquiries = new M_Driver_Enquiries();

            if (Request.QueryString["id"] != null)
            {

                string strId = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Request");
                int id = Convert.ToInt32(strId);
                m_Driver_Enquiries = (from a in entity.RLT_DRIVER_ENQUIRIES
                                      join f in entity.RLT_DRIVER_APPROVE_STATUS on a.Driver_Approving_Status equals f.PKID
                                      orderby a.RequestDate descending
                                      select new M_Driver_Enquiries
                                      {
                                          PKID = a.PKID,
                                          Driver_Name = a.Driver_Name,
                                          RequestDate = a.RequestDate,
                                          Driver_Address = a.Driver_Address,
                                          Driver_Mail = a.Driver_Mail,
                                          Driver_Phone_1 = a.Driver_Phone_1,
                                          IsWhatsAppNumber = a.IsWhatsAppNumber
                                      }).FirstOrDefault();

            }

            return View(m_Driver_Enquiries);
        }

        #endregion

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult GetOwner(int id = 0)
        {
            //id = id == null ? 0 : id;
            var CarOwnerList = entity.RLT_CAR_OWNER_DETAILS.Where(m => m.Vendor_PKID == id).ToList();
            var CarOwner = CarOwnerList.Select(m => new SelectListItem()
            {
                Text = m.Car_Owner_Name,
                Value = m.PKID.ToString(),
            });
            return Json(CarOwner, JsonRequestBehavior.AllowGet);
        }
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult GetCarByOwner(int id = 0)
        {
            var CarList = entity.RLT_CAR_DETAILS.Where(m => m.Car_Owner_PKID == id).ToList();
            var Car = CarList.Select(m => new SelectListItem()
            {
                Text = m.Car_Number,
                Value = m.PKID.ToString(),
            });
            return Json(Car, JsonRequestBehavior.AllowGet);
        }
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult GetCarByVendor(int id = 0)
        {
            var CarList = entity.RLT_CAR_DETAILS.Where(m => m.Vendor_PKID == id).ToList();
            var Car = CarList.Select(m => new SelectListItem()
            {
                Text = m.Car_Number,
                Value = m.PKID.ToString(),
            });
            return Json(Car, JsonRequestBehavior.AllowGet);
        }
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult GetCityByState(int id = 0)
        {
            var CityList = entity.RLT_CITY.Where(m => m.State_PKID == id).ToList();
            var City = CityList.Select(m => new SelectListItem()
            {
                Text = m.City_Name,
                Value = m.PKID.ToString(),
            });
            return Json(City, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("VendorManager", RoleBaseAuthentication.ActionType.View)]
        public JsonResult GenerateMemberIDPwd()
        {
            List<string> strIdPwd = new List<string>();

            strIdPwd.Add(GenerateMemberId());
            strIdPwd.Add(GenerateMemberPwd());

            return Json(strIdPwd, JsonRequestBehavior.AllowGet);
        }

        [NonAction]
        public string GenerateMemberId()
        {
            string memberId = string.Empty;
            memberId = Common.Lib.KeyGenerator.RandomVendorIDGenerator();
            return memberId;
        }
         
        [NonAction]
        public string GenerateMemberPwd()
        {
            PasswordKeyGen pwdKey = new PasswordKeyGen();
            string memberPWD = string.Empty;
            memberPWD = pwdKey.GeneratePassword();
            return memberPWD;
        }
         
    }
}
﻿ 
$(document).ready(function () {
    $("#CityManager").parent().parent().find("a").next().slideToggle(500);
    $("#CityManager").parent().parent().toggleClass('toggled');
    $("#CityManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();

    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'CityManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("City Successfully Saved!","City Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'CityManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("City Successfully Updated!","City Update !");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnSearch").click(function () {


        if ($("#State_PKID").val() == "") {
            toastr.clear();
            toastr.error("Please  select state !", "Required!");
            return false;
        }

        if ($("#City_Name").val() == "") {
            toastr.clear();
            toastr.error("Please select City Name!");
            return false;
        }

        else {
            showLoader();
            GetCityDetails();
            setTimeout(function () {
                hideLoader();
            }, 1000);
        }


    });
});

function Edit(Id)
{
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#Is_Active").val(true);
    

    $("#State_PKID").val($("#stateID_" + Id).val());
    $("#City_Name").val($("#name_" + Id).val());
    $("#latitude").val($("#latitude_" + Id).val());
    $("#longitude").val($("#longitude_" + Id).val());
    $("#eLoc").val($("#eLoc_" + Id).val());
    $("#score").val($("#score_" + Id).val());
    
    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();

}

function Delete(Id)
{

  
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);



    $("#State_PKID").val($("#stateID_" + Id).val());
    $("#City_Name").val($("#name_" + Id).val());
    $("#City_Abbr").val($("#code_" + Id).val());
    $("#Is_Active").val($("#status_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'CityManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("City Successfully Updated!","City Update");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id)
{
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#State_PKID").val($("#stateID_" + Id).val());
    $("#City_Name").val($("#name_" + Id).val());
    $("#City_Abbr").val($("#code_" + Id).val());
    $("#Is_Active").val($("#status_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);

    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'CityManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("City Status Successfully Updated!","City Status Update");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {
   

    $("#City_Name").val('');
    $("#City_Abbr").val('');
    $("#State_PKID").val('');
    $("#Is_Active").val('');
   
}

function IsValidate() {
    
    if ($("#State_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please  select state !", "Required!");
        return false;
    }
    if ($("#City_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter city name !", "Required!");
        return false;

    }
    /*Repeat Validation*/
    if ($("#City_Name").val() != "") {
        if ($("#City_Name-error").html() !== undefined) {
            toastr.clear();
            toastr.error("City Name already exists!", "Required!");
            return false;
        }
    }
    if ($("#latitude").val() == "") {
        toastr.clear();
        toastr.error("Please enter latitude !", "Required!");
        return false;
    }
    if ($("#longitude").val() == "") {
        toastr.clear();
        toastr.error("Please enter longitude !", "Required!");
        return false;
    }

    return true;
}



function GetCityDetails() {
    $.ajax({
        type: 'POST',
        url: '../MapMyIndia/GetCityDetails?CityName=' + $("#City_Name").val()+"&StateName=" + $("#State_PKID").find("option:selected").text(),
        dataType: 'json',
        success: function (CityData) {
            if (CityData != "" && CityData.latitude != "") {
                    $("#latitude").val(CityData.latitude);
                    $("#longitude").val(CityData.longitude);
                    $("#eLoc").val(CityData.eLoc);
                    $("#score").val(CityData.score);
                

            }
            else {
                $("#latitude").val("");
                $("#longitude").val("");
                $("#eLoc").val("");
                $("#score").val("");

                toastr.clear();
                toastr.error("No city found!");
                return false;
            }
        }
    });
    return false;
}
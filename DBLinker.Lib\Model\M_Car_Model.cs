﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
    public class M_Car_Model
    {




        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        [Remote("Check_Car_Model_Name", "Common", ErrorMessage = "Car Model already exists!")]
        public string Car_Model_Name { get; set; }
        public Nullable<int> Car_Category_PKID { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Car_Fuel_Type_Status { get; set; }
        public Nullable<int> Car_Company_PKID { get; set; }
        public Nullable<int> Car_Segment_PKID { get; set; }

        public string Company_Name { get; set; }

        public string Car_Category_Name { get; set; }

        public string Car_Segment_Name { get; set; }
        public string CAR_FUEL_TYPE { get; set; }

    }
}

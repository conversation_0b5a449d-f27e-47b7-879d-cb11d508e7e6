﻿@model DBLinker.Lib.Model.M_Role_Menu_Mapping
@{
    ViewBag.Title = "Role Wise Menu";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Add Role Wise Menu</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add Role Wise Menu</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddRoleMenuMapping", "UserManagement", FormMethod.Post, new { enctype = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()

            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-12">
                    <div class="card-box">
                        <div class="card-head">
                            <div class="col-1"></div>
                            <div class="col-2"><label><span>Role Name</span></label></div>
                            <div class="col-5">
                                @if (ViewBag.RoleList != null)
                                {
                                    @Html.DropDownListFor(x => x.Role_Id, new SelectList(ViewBag.RoleList, "RoleId", "Role"), "-- Select Role --", new { @style = "margin-left: 25px;width:300px", @class = "selectDropdown" })
                                }
                            </div>
                            <button type="submit" class="col-2 mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect btn-pink" id="btnSave">Save</button>
                        </div>

                        <div class="row" id="partialDiv">
                            <br /><br /><br />
                            @Html.Partial("_RoleMenuMapping")
                        </div>

                        <div class="row" style="text-align:center;">
                            <div class="col-md-12">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

    </div>

</div>

<script src="~/Scripts/CrudFile/RoleMenuMapping.js"></script>

<div class="modal fade" id="js-model" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <label id="modal-total-info"></label>
            </div>
            <div class="modal-body">
                <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="27%">Menu Name</th>
                            <th width="5%">View</th>
                            <th width="5%">Add</th>
                            <th width="5%">Edit</th>
                            <th width="5%">Delete</th>
                            <th width="3%">Order<br />Number</th>
                            <th width="10%">Action<br />Name</th>
                            <th width="10%">Controller<br />Name</th>
                            <th width="15%">Date<br />Created</th>
                        </tr>
                    </thead>
                    <tbody id="model-body-info">

                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>
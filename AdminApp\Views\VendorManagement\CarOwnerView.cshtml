﻿@model DBLinker.Lib.Model.M_CarOwner

@{
    ViewBag.Title = "Car Owner View";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $("#CarOwnerManager").parent().parent().find("a").next().slideToggle(500);
        $("#CarOwnerManager").parent().parent().toggleClass('toggled');
        $("#CarOwnerManager").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">View Car Owner Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/CarOwnerManager">Car Owner Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Car Owner Details</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Car Owner Profile :@Model.Car_Owner_Name</header>

                    </div>
                    <div class="card-body row">
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @if (!string.IsNullOrEmpty(Model.Car_Owner_Photo))
                            {
                                <img src="~/Docs/CarOwnerPhoto/@Model.Car_Owner_Photo" id="CarOwnerPhoto" height="80" width="100" />
                        }
                        else
                        {
                            <img src="~/Content/img/no-user-image.gif" id="Driver_Photo" height="80" width="100" />
                    }
                                <label class="mdl-textfield__label"><span>Photo</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })

                                <label class="mdl-textfield__label required"><span>Vendor Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                <label class="mdl-textfield__label required"><span>Owner Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Email, new { @class = "mdl-textfield__input", @maxlength = "40" })
                                <label class="mdl-textfield__label required"><span>Email</span></label>
                            </div>
                        </div>



                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Phone1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>Phone Number 1</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Phone2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Phone Number 2</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_DL, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Owner DL</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Aadhaar, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>Owner Aadhaar</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Address, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>Owner Address</span> </label>
                            </div>
                        </div>


                    </div>



                </div>
            </div>
        </div>


        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/CarOwnerManager">Back</a>
            </div>
        </div>

    </div>
</div>


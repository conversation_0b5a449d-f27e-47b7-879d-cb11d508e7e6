﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Lib.Model
{
    public class ExceptionLogger
    {
        public int ExceptionId { get; set; }
        public string ExceptionMessage { get; set; }
        public Nullable<int> LineNumber { get; set; }
        public string MethodName { get; set; }
        public string ControllerName { get; set; }
        public Nullable<System.DateTime> LogTime { get; set; }
        public string StackTrace { get; set; }
        public Nullable<int> LoginUserId { get; set; }
        public string IpAddress { get; set; }
    }
}

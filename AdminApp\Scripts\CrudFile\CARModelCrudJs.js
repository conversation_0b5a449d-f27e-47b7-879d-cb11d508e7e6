﻿$(document).ready(function () {

    $("#CARModel").parent().parent().find("a").next().slideToggle(500);
    $("#CARModel").parent().parent().toggleClass('toggled');
    $("#CARModel").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'CARModel',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Model Successfully Saved!","Car Model Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'CARModel',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Model Successfully Updated!", "Car Model Update");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Car_Model_Name").val($("#name_" + Id).val());
    $("#Car_Category_PKID").val($("#cat_" + Id).val());
    $("#Car_Fuel_Type_Status").val($("#fuel_" + Id).val());
    $("#Car_Company_PKID").val($("#campany_" + Id).val());
    $("#Car_Segment_PKID").val($("#Car_Segment_" + Id).val());
    $("#Is_Active").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

//function Delete(Id) {

//    $("#PKID").val('');
//    $("#PKID").val(Id);
//    $("#hdnOperation").val('U');
//    $("#hdnStatus").val(true);


//    $("#Car_Model_Name").val($("#name_" + Id).val());
//    $("#Car_Category_PKID").val($("#cat_" + Id).val());
//    $("#Car_Fuel_Type_Status").val($("#fuel_" + Id).val());
//    $("#Car_Company_PKID").val($("#campany_" + Id).val());
//    $("#IsActive").val($("#status_" + Id).val());


//    $("#PKID").val(Id);
//    if ($("#status_" + Id).val() == false) {
//        $("#IsActive").val(true);
//    }
//    else {
//        $("#IsActive").val(false);
//    }

//    $("#hdnOperation").val('U');
//    $("#hdnStatus").val(false);


//    if ($("#PKID").val() != "") {

//        $.ajax({
//            url: 'CARModel',
//            type: "POST",
//            traditional: true,
//            data: $("form").serialize(),
//            dataType: "json",
//            success: function (response) {
//                if (response > 0) {
//                    toastr.success("Car Model Successfully Updated!");
//                    document.location.reload();
//                }
//                else {
//                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

//                }
//            },
//            failure: function (response) {

//            },
//            error: function (response) {
//                alert(response.responseText);
//            }
//        });

//    }


//}

function ActivateDeactivate(Id) {
    $("#PKID").val('');
    $("#Car_Model_Name").val($("#name_" + Id).val());
    $("#Car_Category_PKID").val($("#cat_" + Id).val());
    $("#Car_Fuel_Type_Status").val($("#fuel_" + Id).val());
    $("#Car_Company_PKID").val($("#campany_" + Id).val());
    $("#Car_Segment_PKID").val($("#Car_Segment_" + Id).val());
    $("#Is_Active").val($("#status_" + Id).val());
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'CARModel',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Model Status Successfully Updated!","Status Update");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    $("#Car_Model_Name").val('');
    $("#Car_Category_PKID").val('');
    $("#Car_Fuel_Type_Status").val('');
    $("#Car_Company_PKID").val('');
    $("#Car_Segment_PKID").val('');
}

function IsValidate() {
    if ($("#Car_Model_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter car model name !", "Required!");
        return false;
    }
    /*Repeat Validation*/
    if ($("#Car_Model_Name").val() != "") {
        if ($("#Car_Model_Name-error").html() !== undefined) {
            toastr.clear();
            toastr.error("Car Model Name already exists!", "Required!");
            return false;
        }
    }
    if ($("#Car_Company_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select company name !","Required!");
        return false;
    }
    if ($("#Car_Category_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select category name !", "Required!");
        return false;
    }
    if ($("#Car_Segment_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Segment!", "Required!");
        return false;
    }
    if ($("#Car_Fuel_Type_Status").val() == "") {
        toastr.clear();
        toastr.error("Please select fuel type !", "Required!");
        return false;
    }
    
    
    return true;
}
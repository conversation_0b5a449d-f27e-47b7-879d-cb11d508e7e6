﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Driver_Enquiries
    {
        public int PKID { get; set; }
        public string Driver_Name { get; set; }
        public string Driver_Phone_1 { get; set; }
        public string Driver_Car_No { get; set; }
        public string Driver_Mail { get; set; }
        public string Driver_Address { get; set; }
        public Nullable<System.DateTime> RequestDate { get; set; }
        public Nullable<int> Driver_Approving_Status { get; set; }
        public string Remark { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }
        public Nullable<bool> IsWhatsAppNumber { get; set; }

    }
}

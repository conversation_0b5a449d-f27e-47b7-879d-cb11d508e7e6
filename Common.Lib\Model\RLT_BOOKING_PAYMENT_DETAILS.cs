//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Common.Lib.Model
{
    using System;
    using System.Collections.Generic;
    
    public  class RLT_BOOKING_PAYMENT_DETAILS
    {
        public int PKID { get; set; }
        public string Passenger_Name { get; set; }
        public string Passenger_Email { get; set; }
        public string Passenger_Phone { get; set; }
        public string Passenger_Alt_Phone { get; set; }
        public string Passenger_Special_Request_Comment { get; set; }
        public string Passenger_Trip_Purpose { get; set; }
        public Nullable<int> Passenger_Trip_Type_PKID { get; set; }
        public Nullable<int> Passenger_Pickup_City_PKID { get; set; }
        public string Passenger_Pickup_Address { get; set; }
        public Nullable<int> Passenger_Dropoff_City_PKID { get; set; }
        public string Passenger_Dropoff_Address { get; set; }
        public Nullable<long> Booking_Login_PKID { get; set; }
        public Nullable<double> Base_Fare { get; set; }
        public Nullable<double> Driver_Allowance { get; set; }
        public Nullable<double> Service_Tax { get; set; }
        public Nullable<double> Discount { get; set; }
        public string Discount_Coupon { get; set; }
        public Nullable<double> Total_Booking_Amount { get; set; }
        public Nullable<int> Payment_Mode_PKID { get; set; }
        public Nullable<double> Payment_Amount_Recieved { get; set; }
        public Nullable<double> Payment_Need_to_Collect { get; set; }
        public string Payment_Issue_Comment { get; set; }
        public string Payment_TransactionId { get; set; }
        public string Payment_Transaction_Status { get; set; }
    
      
    }
}

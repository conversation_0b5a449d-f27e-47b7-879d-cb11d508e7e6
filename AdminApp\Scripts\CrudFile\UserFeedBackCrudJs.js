﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $("#lblFileName").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#lblFileName").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });



    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#IsActive").val(true);
    $("#lblFileName").slideDown();
    $("#lblFileName").show();
    $("#Name").val($("#name_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());
    $("#EmailID").val($("#email_" + Id).val());
    $("#Photo").val($("#imagePath_" + Id).val());
    $("#lblFileName").text($("#imagePath_" + Id).val());
    $("#Message").val($("#comment_" + Id).val());

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id) {

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#Name").val($("#name_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());
    $("#EmailID").val($("#email_" + Id).val());
    $("#Photo").val($("#imagePath_" + Id).val());
    $("#lblFileName").text($("#imagePath_" + Id).val());
    $("#Message").val($("#comment_" + Id).val());


    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);
}

function ActivateDeactivate(Id) {

    $("#Name").val($("#name_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());
    $("#EmailID").val($("#email_" + Id).val());
    $("#Photo").val($("#imagePath_" + Id).val());
    $("#lblFileName").text($("#imagePath_" + Id).val());
    $("#Message").val($("#comment_" + Id).val());

    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
}


function resetForm() {
   
    $("#Name").val('');
    $("#PhoneNo").val('');
    $("#EmailID").val('');
    $("#Message").val('');
   

}

function IsValidate() {
    if ($("#Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter name!");
        return false;
    }
    if ($("#PhoneNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter phone no !");
        return false;
    }
    if ($("#EmailID").val() == "") {
        toastr.clear();
        toastr.error("Please enter email id !");
        return false;

    }
    if ($("#Message").val() == "") {
        toastr.clear();
        toastr.error("Please enter comment !");
        return false;

    }

    return true;
}
﻿using DBLinker.Lib;
using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
namespace AdminApp.Filters
{
    public class CustomAuthorizeAttribute : AuthorizeAttribute
    {
         RLTDBContext entity;
        protected virtual CustomPrincipal CurrentUser
        {
            get { return HttpContext.Current.User as CustomPrincipal; }
        }

        public override void OnAuthorization(AuthorizationContext filterContext)
        {
            if (filterContext.HttpContext.Request.IsAuthenticated)
            {
                entity = new RLTDBContext();
                var UserDetails = entity.RLT_ADMIN_USER.Where(x => x.UserName == CurrentUser.UserName).FirstOrDefault();
                // var authorizedRoles = ConfigurationManager.AppSettings[RolesConfigKey];
                var authorizedUsers = UserDetails.UserName;
                Users = String.IsNullOrEmpty(authorizedUsers) ? Users : authorizedUsers;
                // Roles = String.IsNullOrEmpty(Roles) ? authorizedRoles : Roles;



                if (!String.IsNullOrEmpty(Users))
                {
                    if (!Users.Contains(CurrentUser.UserName.ToString()))
                    {
                        filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "Login", action = "AcessDenied" }));

                        // base.OnAuthorization(filterContext); //returns to login url
                    }
                }
            }
            else
            {

                base.OnAuthorization(filterContext); //returns to login url


            }

        }
    }
}
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_MENU_MASTER
    {
        public int PKID { get; set; }
        public string MenuName { get; set; }
        public string MenuIcon { get; set; }
        public string MenuURL { get; set; }
        public string PageId { get; set; }
        public int ParentMenuId { get; set; }
        public Nullable<int> IsAdminMenu { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public string CreatedBy { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public string ActiveMenuClass { get; set; }
        public Nullable<double> OrderNumber { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<int> UpdatedBy { get; set; }
        public string ActionName { get; set; }
        public string ControllerName { get; set; }
    }
}

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_USER_LOGIN_DETAILS
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public RLT_USER_LOGIN_DETAILS()
        {
            this.RLT_BOOKING_PAYMENT_DETAILS = new HashSet<RLT_BOOKING_PAYMENT_DETAILS>();
        }
    
        public long PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        public string RLT_User_Name { get; set; }
        public string RLT_User_Phone { get; set; }
        public string RLT_User_Email { get; set; }
        public string RLT_User_Login_Signup_Method { get; set; }
        public string RLT_User_Aggreed { get; set; }
        public string RLT_User_Pwd { get; set; }
        public string RLT_User_Gender { get; set; }
        public Nullable<System.DateTime> RLT_User_DOB { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<System.DateTime> Last_Login_Date_Time { get; set; }
        public Nullable<bool> RLT_User_2FA_Auth_Status { get; set; }
        public Nullable<int> OTPNumber { get; set; }
        public Nullable<System.DateTime> OTPUpdatedDateTime { get; set; }
        public string UserID { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RLT_BOOKING_PAYMENT_DETAILS> RLT_BOOKING_PAYMENT_DETAILS { get; set; }
    }
}

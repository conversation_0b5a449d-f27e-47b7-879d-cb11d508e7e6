﻿
$(document).ready(function () {

    $("#CarManager").parent().parent().find("a").next().slideToggle(500);
    $("#CarManager").parent().parent().toggleClass('toggled');
    $("#CarManager").find("a").addClass("selectedMenu");
});




$(document).ready(function () {
    $(".datefiled").load(fillDate());
});


function fillDate() {
    var inputs = $(".dateFields");
    if (inputs.length > 0) {
        for (var i = 0; i < inputs.length; i++) {
            if (Date.parse(inputs[i].attributes.value.nodeValue)) {
                var getDate = new Date(inputs[i].attributes.value.nodeValue);
                var setDate = getDate.getFullYear() + "-" + ("0" + (getDate.getMonth() + 1)).slice(-2) + "-" + ("0" + (getDate.getDate())).slice(-2);
                $("#" + inputs[i].attributes.id.nodeValue).val(setDate);
            }
        }
    }
}
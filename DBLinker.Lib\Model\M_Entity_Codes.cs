﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DBLinker.Lib.Model
{
  public  class M_Entity_Codes
    {

        public long PKID { get; set; }
        [Required(ErrorMessage = "Please enter entity code unique!")]
        public int Entity_Code { get; set; }
        [Required(ErrorMessage = "Please enter entity unique code name!")]
        public string Entity_Desc { get; set; }
        public DateTime Created_Date { get; set; }

        public int Created_By { get; set; }

        public bool Is_Active { get; set; }
    }
}

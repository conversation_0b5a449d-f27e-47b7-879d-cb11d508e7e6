﻿@model DBLinker.Lib.Model.M_Employee

@{
    ViewBag.Title = "Employee Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $("#EmployeeList").parent().parent().find("a").next().slideToggle(500);
        $("#EmployeeList").parent().parent().toggleClass('toggled');
        $("#EmployeeList").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Employee Detail of @Model.Employee_Name</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../UserManagement/EmployeeList">Employee List </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">View Employee</li>
                </ol>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Personal Information</header>
                    </div>
                    <div class="card-body row" id="divPersonal">
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Employee_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Employee Name</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width"> 
                                <img id="UserPhoto" @(string.IsNullOrWhiteSpace(Model.Photo) ? "src=../Docs/No_image_available.png" : Url.Content("src=/Docs/UserPhoto/" + Model.Photo)) height="80" width="120" onerror="this.onerror=null;this.src='../Docs/No_image_available.png';" /> 
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Gender, "Male", new { @id = "Gender1" })
                                        <label for="radio1"><span><span></span></span> Male</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Gender, "Female", new { @id = "Gender2" })
                                        <label for="radio2"><span><span></span></span>Female</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label"><span>Gender</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Marrital_Status, "Single", new { @id = "Marrital_Status1" })
                                        <label for="radio1"><span><span></span></span> Single</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Marrital_Status, "Married", new { @id = "Marrital_Status2" })
                                        <label for="radio2"><span><span></span></span>Married</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label"><span>Marrital Status</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Spouses_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Spouses Name</span> </label>
                            </div>
                        </div>

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.DOB, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>DOB</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Phone_1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Mobile No</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Phone_1_IsWhatsup, "Yes", new { @id = "Phone_1_IsWhatsup1" })
                                        <label for="radio1"><span><span></span></span> Yes</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Phone_1_IsWhatsup, "No", new { @id = "Phone_1_IsWhatsup2" })
                                        <label for="radio2"><span><span></span></span>No</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label"><span>Whatsup No</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Phone_2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Alternate Mobile No</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Email, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Email</span> </label>
                            </div>
                        </div>


                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Father_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Father Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Mother_Name, new { @class = "mdl-textfield__input " })
                                <label class="mdl-textfield__label">Mother Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-head">
                        <header>Job Information</header>
                    </div>
                    <div class="card-body row" id="divJob">
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Employee_ID, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Employee ID</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Designation, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Designation</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Department, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Department</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Supervisor, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Supervisor</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Work_Phone, new { @class = "mdl-textfield__input txtbx" })
                                <label class="mdl-textfield__label">Office Phone No</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Work_Email, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Office Email Id</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Work_StartDate, "{0:yyyy-MM-dd}", new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Start Date</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Work_City_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Work City Name</span> </label>
                            </div>
                        </div>
                    </div>


                    <div class="card-head">
                        <header>Education Information</header>
                    </div>
                    <div class="card-body row" id="divEducation">
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">10th(Secondary) </span>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C10th_School, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> School Name</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C10th_PassingYear, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Passing Year</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C10th_Percentage, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Percentage</label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">12th(Senior Secondary) </span>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C12th_School, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> School Name</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C12th_PassingYear, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">PassingYear</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.C12th_Percentage, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Percentage</label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Degree </span>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Degree_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Degree Name</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Degree_College, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">College</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Degree_PassingYear, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> PassingYear</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Degree_Percentage, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Percentage</label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Master Degree </span>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Master_Degree_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Master Degree Name</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Master_Degree_College, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">College</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Master_Degree_PassingYear, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> PassingYear</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Master_Degree_Percentage, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Percentage</label>
                            </div>
                        </div>
                    </div>



                    <div class="card-head">
                        <header>Contact Information</header>
                    </div>
                    <div class="card-body row" id="divContact">
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Current Address </span>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Current_Address1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Address 1</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Current_Address2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Address 2</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Current_Address_State_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>State</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Current_Address_City_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>City</span> </label>
                            </div>
                        </div>




                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Permanent Address </span>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Permanent_Address1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Address 1</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Permanent_Address2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Address 2</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Permanent_Address_State_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>State</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Permanent_Address_City_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>City</span> </label>
                            </div>
                        </div>
                    </div>





                </div>
            </div>
        </div>

        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default"
                   href="../UserManagement/UserManager">Back</a>
            </div>
        </div>

    </div>
</div>



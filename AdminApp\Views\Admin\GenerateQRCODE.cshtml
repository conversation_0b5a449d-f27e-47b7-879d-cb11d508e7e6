﻿@model Common.Lib.Model.TwoFactorModel

@{
    ViewBag.Title = "Generate QR Code";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<script>

        $(document).ready(function () {

           
                $("#btnVerfiy").click(function () {
                    if(IsValidate())
                    {
                    $.ajax({
                        url: 'VerifyGeneratedQRCODE',
                        type: "POST",
                        traditional: true,
                        data: { "PassCode": $("#PassCode").val() },
                        dataType: "json",
                        success: function (response)
                        {
                            if (response == true)
                            {
                                toastr.success("Two Factor Authentication Sucessfully Enabled !");
                            }
                            else
                            {
                                toastr.error("Two Factor Authentication Not Enabled !");
                            }
                            
                        },
                        failure: function (response) {

                        },
                        error: function (response) {
                            toastr.error(response.responseText);
                        }
                    });

                    }

                });
           

        });


        function IsValidate()
        {
             
            if($("#PassCode").val()=="")
            {
                toastr.error("Please Enter 6 Digit PassCode!");
            }
            return true;
        }


</script>



<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Security Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Security Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Enable/Disable 2Factor Authentication</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

            
            @Html.AntiForgeryToken()

            @Html.HiddenFor(x => x.UserUniqueKey)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>2 Factor Authenticator</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <img src="@Model.QrCodeSetupImageUrl" />
                                    <label class="mdl-textfield__label"> QR Code</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <label class="mdl-textfield__input">  @Model.ManualEntryKey</label>
                                  
                                  
                                    <label class="mdl-textfield__label"> Manual Setup Code</label>
                                </div>
                            </div>


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.PassCode, new { @class = "mdl-textfield__input"})
                                    <label class="mdl-textfield__label"> 6 digit passcode</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20 text-center">

                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnVerfiy">Verify</button>
                                
                                 </div>

                        </div>
                    </div>
                </div>
            </div>
        }

    </div>
</div>




thead tr th:nth-child(2), tbody tr td:nth-child(2) {
    text-align: left;
}

thead tr th, tbody tr td {
    text-align: center;
}

label.custom-control-label {
    cursor: pointer;
}

.card-body-col-12 {
    padding: 0px 0px 20px 30px;
}

.table-parent-icon {
    float: right;
    padding: 0px 5px;
    background: #007bffb8;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    outline: none;
}

.table-child-icon {
    float: right;
    padding: 0px 5px;
    background: #ccff00b8;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    outline: none;
}

.highlighted {
    background-color: Yellow;
    padding: 2px 3px; 
    border-radius: 7px; 
    font-weight: bold;
}

.modal-dialog {
    max-width: 80% !important;
}

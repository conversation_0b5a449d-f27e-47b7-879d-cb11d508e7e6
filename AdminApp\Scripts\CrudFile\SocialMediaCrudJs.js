﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();

    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
    });

    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#IsURL").click(function () {
        var videoType = $("#IsURL").val();
        // alert(videoType);
        if (videoType == "1") {
            $("#dvname").slideDown();
            $("#dvURL").slideDown();
            $("#dvFile").slideUp();
        }
        else if (videoType == "0") {
            $("#dvname").slideDown();
            $("#dvFile").slideDown();
            $("#dvURL").slideUp();
        }
        else {
            $("#dvname").slideUp();
            $("#dvFile").slideUp();
            $("#dvURL").slideUp();
        }
    });

});

function Edit(Id) {
    toastr.success("Edited Sucessfully");

}

function Delete(Id) {
    toastr.success("Deleted Sucessfully");
}

function ActivateDeactivate(Id) {
    toastr.success("Activated Sucessfully");
}

function resetForm() {
    $("#dvname").slideUp();
    $("#dvFile").slideUp();
    $("#dvURL").slideUp();

    $("#VideoTitle").val('');
    $("#VideoName").val('');
    $("#MetaData").val('');
    $("#MetaTag").val('');
    $("#MetaDescription").val('');
    $("#VideoDescription").val('');
    $("#ThumbImage").val('');
    $("#BannerImage").val('');
    $("#IsURL").val('');
    $("#VideoURLPath").val('');
    $("#videofile").val('');
}

function IsValidate() {
    if ($("#VideoTitle").val() == "") {
        toastr.clear();
        toastr.error("Please enter video title !");
        return false;
    }
    if ($("#VideoName").val() == "") {
        toastr.clear();
        toastr.error("Please enter video name !");
        return false;
    }
    if ($("#MetaData").val() == "") {
        toastr.clear();
        toastr.error("Please enter video meta data !");
        return false;

    }
    if ($("#MetaTag").val() == "") {
        toastr.clear();
        toastr.error("Please enter video meta tag !");
        return false;
    }
    if ($("#MetaDescription").val() == "") {
        toastr.clear();
        toastr.error("Please enter video meta description !");
        return false;
    }
    if ($("#VideoDescription").val() == "") {
        toastr.clear();
        toastr.error("Please enter about video !");
        return false;
    }
    //if ($("#ThumbImage").val() == "")
    //{
    //    toastr.clear();
    //    toastr.error("Please add video thumbnail image !");
    //    return false;
    //}
    //if ($("#BannerImage").val() == "")
    //{
    //    toastr.clear();
    //    toastr.error("Please add video banner image !");
    //    return false;
    //}
    if ($("#IsURL").val() == "") {
        toastr.clear();
        toastr.error("Please select video type !");
        return false;
    }
    //if ($("#VideoURLPath").val() == "")
    //{
    //    toastr.clear();
    //    toastr.error("Please enter video title !");
    //    return false;
    //}
    //if ($("#videofile").val() == "")
    //{
    //    toastr.clear();
    //    toastr.error("Please add video url or file!");
    //    return false;
    //}

    return true;
}
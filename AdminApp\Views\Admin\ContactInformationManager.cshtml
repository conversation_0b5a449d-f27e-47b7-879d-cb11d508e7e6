﻿
@{
    ViewBag.Title = "ContactInformationManager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@model DBLinker.Lib.RLT_ContactInformation


<link href="~/Content/css/jquery.dataTables.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/select/1.2.2/css/select.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.2/js/dataTables.select.min.js"></script>
<link href="~/Content/toastr.min.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<section class="content">
    .
    <div class="row">
        <div class="col-md-12">
            <!--breadcrumbs start -->
            <ul class="breadcrumb">
                <li><a href="#"><i class="fa fa-user"></i> Admin</a></li>
                <li><a href="#"><i class="fa fa-address-card"></i> Contact Information</a></li>

            </ul>
            <!--breadcrumbs end -->
        </div>
    </div>
    @using (Html.BeginForm())
    {

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.isActive)
        @Html.HiddenFor(x => x.ID)

        <div class="row" id="dvAddUpdate">
            <div class="col-md-12">
                <div class="col-lg-12">
                    <section class="panel">
                        <header class="panel-heading">
                            Add Update Contact Information
                        </header>
                        <div class="panel-body">

                            <div class="form-group">
                                <div class="col-sm-2">
                                    Address
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.Address, new { @class = "form-control", @placeholder = "enter company address !" })

                                </div>
                                <div class="col-sm-2">
                                    Sales  Number
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.SalesTelNo, new { @class = "form-control", @placeholder = "enter sales mobile/tel. Number !" })
                                </div>
                            </div>

                            <br /><br />
                            <div class="form-group">
                              
                                <div class="col-sm-2">
                                    Business Number
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.BusinessTelNo, new { @class = "form-control", @placeholder = "enter business mobile/tel. Number !" })
                                </div>

                                <div class="col-sm-2">
                                    Fax No
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.FaxNo, new { @class = "form-control", @placeholder = "enter fax number !" })

                                </div>
                            </div>

                            <br /><br />
                            <div class="form-group">
                                <div class="col-sm-2">
                                    Sales Email
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.SalesEmailNo, new { @class = "form-control", @placeholder = "enter sales email id !" })

                                </div>
                                <div class="col-sm-2">
                                   Business Email
                                </div>
                                <div class="col-lg-4">  
                                    @Html.TextBoxFor(x => x.BusinessEmailNo, new { @class = "form-control", @placeholder = "enter business email id !" })
                                </div>
                            </div>

                            <br /><br />
                            <div class="form-group">
                                <div class="col-sm-2">
                                    Working Hours
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.WorkingHours, new { @class = "form-control", @placeholder = "enter working hours !" })

                                </div>
                                <div class="col-sm-2">
                                    Google Map URL
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.GoogleMapLink, new { @class = "form-control", @placeholder = "enter google map url !" })
                                </div>
                            </div>

                        </div>
                        <footer class="panel-footer">
                            <center>
                                <button type="button" class="btn btn-info" id="btnSave" style="display:none">Save</button>
                                <button type="button" class="btn btn-info" id="btnUpdate" style="display:none">Update</button>
                                <button type="reset" class="btn btn-danger" id="btnReset">cancel</button>
                            </center>
                        </footer>
                    </section>
                </div>

            </div>
        </div>

    

    <section class="panel tasks-widget">
        <header class="panel-heading">
            Contact Information Manager
            <button type="button" id="btnAdd" class="btn btn-info" style="float:right;margin-top: -5px;">Add Contact Information</button>
        </header>
        <div class="panel-body">

            <div class="task-content">

                <table id="example" class="display" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Address</th>
                            <th>Sales No</th>
                            <th>Sales Email</th>
                            <th>Business No</th>
                            <th>Business Email</th>
                            <th>Fax No</th>
                            <th>Working Hours</th>
                            <th>Map URL</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th></th>
                            <th>Address</th>
                            <th>Sales No</th>
                            <th>Sales Email</th>
                            <th>Business No</th>
                            <th>Business Email</th>
                            <th>Fax No</th>
                            <th>Working Hours</th>
                            <th>Status</th>
                            <th>Map URL</th>
                            <th></th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @if (ViewBag.conInfoGridList != null)
                        {
                            foreach (var item in ViewBag.conInfoGridList)
                            {
                                <tr>
                                    <input type="hidden" id="<EMAIL>" value="@item.Address" />
                                    <input type="hidden" id="<EMAIL>" value="@item.SalesTelNo" />
                                    <input type="hidden" id="<EMAIL>" value="@item.SalesEmailNo" />
                                    <input type="hidden" id="<EMAIL>" value="@item.BusinessTelNo" />
                                    <input type="hidden" id="<EMAIL>" value="@item.BusinessEmailNo" />
                                    <input type="hidden" id="<EMAIL>" value="@item.FaxNo" />
                                    <input type="hidden" id="<EMAIL>" value="@item.WorkingHours" />
                                    <input type="hidden" id="<EMAIL>" value="@item.GoogleMapLink" />
                                    <input type="hidden" id="<EMAIL>" value="@item.isActive" />
                                    <td></td>
                                    <td>@item.Address</td>
                                    <td>@item.SalesTelNo</td>
                                    <td>@item.SalesEmailNo</td>
                                    <td>@item.BusinessTelNo</td>
                                    <td>@item.BusinessEmailNo</td>
                                    <td>@item.FaxNo</td>
                                    <td>@item.WorkingHours</td>
                                    <td>@item.isActive</td>
                                    <td title="@item.GoogleMapLink" style="white-space: nowrap; overflow: hidden;text-overflow: ellipsis; max-width: 200px;">@item.GoogleMapLink</td>
                                    <td>
                                        <button class="btn btn-default btn-xs" onclick="ActivateDeactivate(@item.ID)" type="button"><i class="fa fa-check"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Edit(@item.ID)" type="button" id="btnEdit"><i class="fa fa-pencil"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Delete(@item.ID)" type="submit" ><i class="fa fa-times"></i></button>
                                    </td>
                                </tr>
                            }

                        }
                    </tbody>
                </table>
            </div>


        </div>
    </section>
    }
</section>

<script src="~/Scripts/CrudFile/ContactInfoCrudJs.js"></script>




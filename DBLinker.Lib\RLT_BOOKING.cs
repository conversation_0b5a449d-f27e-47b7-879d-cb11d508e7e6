//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_BOOKING
    {
        public long PKID { get; set; }
        public string Booking_Id { get; set; }
        public Nullable<int> City_From_Id { get; set; }
        public Nullable<int> City_To_Id { get; set; }
        public Nullable<int> Trip_Type_Id { get; set; }
        public Nullable<int> Car_Category_Id { get; set; }
        public string Duration { get; set; }
        public Nullable<decimal> Distance { get; set; }
        public Nullable<decimal> Basic_Fare { get; set; }
        public Nullable<decimal> Driver_Charge { get; set; }
        public Nullable<decimal> Toll_Charge { get; set; }
        public Nullable<decimal> GST { get; set; }
        public Nullable<decimal> Fare { get; set; }
        public Nullable<decimal> GST_Fare { get; set; }
        public string Coupon_Code { get; set; }
        public Nullable<decimal> Coupon_Discount { get; set; }
        public Nullable<System.DateTime> Booking_Date { get; set; }
        public string PickUp_Address { get; set; }
        public string DropOff_Address { get; set; }
        public Nullable<System.DateTime> PickUp_Date { get; set; }
        public string PickUp_Time { get; set; }
        public string Name { get; set; }
        public string Mobile_No1 { get; set; }
        public string Mobile_No2 { get; set; }
        public string Mail_Id { get; set; }
        public Nullable<int> Mode_Of_Payment_Id { get; set; }
        public Nullable<int> Vendor_Id { get; set; }
        public Nullable<int> Car_Id { get; set; }
        public Nullable<int> Booking_Status_Id { get; set; }
        public string Booking_Remark { get; set; }
        public string Invoice_No { get; set; }
        public Nullable<System.DateTime> Invoice_Date { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Updated_By { get; set; }
        public string razorpay_payment_id { get; set; }
        public string razorpay_order_id { get; set; }
        public string razorpay_signature { get; set; }
        public string razorpay_status { get; set; }
        public string payment_link { get; set; }
        public string PickUpAddressLatitude { get; set; }
        public string PickUpAddressLongitude { get; set; }
        public string BookingEditRemark { get; set; }
        public Nullable<int> driver_id { get; set; }
        public string completepickupaddress { get; set; }
        public string completedropoffpaddress { get; set; }
        public Nullable<bool> IsFullOnlinePayment { get; set; }
        public Nullable<decimal> CashAmountToPayDriver { get; set; }
        public Nullable<decimal> TollCharge { get; set; }
        public Nullable<int> PaymentOption { get; set; }
        public Nullable<System.DateTime> DropOffDate { get; set; }
        public string DropOffTime { get; set; }
        public Nullable<System.DateTime> PaymentDate { get; set; }
        public string BookedBy { get; set; }
        public Nullable<bool> IsAdminBooked { get; set; }
        public string PaymentType { get; set; }
        public Nullable<decimal> PartialPaymentAmount { get; set; }
        public Nullable<decimal> RemainingAmountForDriver { get; set; }
        public string Booking_Created_By { get; set; }
    }
}

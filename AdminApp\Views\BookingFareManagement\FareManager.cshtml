﻿@model DBLinker.Lib.RLT_BOOKING_FARE
@using AdminApp.Mapper;
@{
    ViewBag.Title = "Fare Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Fare Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Fare Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Booking Fare</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Booking Fare</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.City_From, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label">City From</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.City_To, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label">City To</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.TripTypeList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Trip_Type_Id, new SelectList(ViewBag.TripTypeList, "PKID", "Trip_Type"), "-- Select Trip Type --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label">Trip Type</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CarCategory != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Category_Id, new SelectList(ViewBag.CarCategory, "PKID", "Car_Category"), "-- Select Car Category --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label">CAR Category</label>
                                </div>
                            </div>

                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Basic_Fare, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label">Basic Fare</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Charge, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label">Driver Charge</label>
                                </div>
                            </div>

                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Toll_Charge, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label">Toll Charge</label>
                                </div>
                            </div>

                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.Total_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                    <label class="mdl-textfield__label">Total Fare</label>
                                </div>
                            </div>



                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.GST, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "3" })
                                    <label class="mdl-textfield__label"> GST(%)</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.GST_Amount, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                    <label class="mdl-textfield__label"> GST Amount</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    @Html.TextBoxFor(x => x.Final_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                    <label class="mdl-textfield__label"> Final Fare</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    @Html.TextBoxFor(x => x.Remark, new { @class = "mdl-textfield__input", Style = "color:black;", @onblur = "isTextSpecialValid(this);", @maxlength = "300" })
                                    <label class="mdl-textfield__label"> Remark</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))

                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Fare Manager List</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                @(PermittedAction.Is_Add ? Html.Raw("<div class='btn-group'><a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a></div>") : Html.Raw(""))
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th>From</th>
                                        <th>To</th>
                                        <th>Trip Type</th>
                                        <th>Category</th>
                                        <th>Basic Fare</th>
                                        <th>Driver Charge</th>
                                        <th>Toll</th>
                                        <th>Total Fare</th>
                                        <th> GST Amount</th>
                                        <th>Final Fare</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (ViewBag.FareList != null)
                                    {
                                        foreach (var item in ViewBag.FareList)
                                        {

                                            <tr class="odd gradeX">
                                                <td>@item.City_From</td>
                                                <td>@item.City_To</td>
                                                <td>@item.Trip_Type</td>
                                                <td>@item.Car_Category</td>
                                                <td>@item.Basic_Fare</td>
                                                <td>@item.Driver_Charge</td>
                                                <td>@item.Toll_Charge</td>
                                                <td>@item.Total_Fare</td>
                                                <td>@item.GST_Amount</td>
                                                <td>@item.Final_Fare</td>
                                                <td> 
                                                    @Html.Raw(item.Is_Active == null
                                                 ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                                 ? "<span class='label label-sm label-success'> Active </span>"
                                                 : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID + ")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit("+item.PKID + ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_View ? Html.Raw("<a href='../BookingFareManagement/FareManagerView?id="+ QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "FareManager") + "' title='View Car Driver Info'><i class='material-icons'>remove_red_eye</i> View</a>") : Html.Raw(""))
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/CrudFile/FareManagerCrud.js"></script>


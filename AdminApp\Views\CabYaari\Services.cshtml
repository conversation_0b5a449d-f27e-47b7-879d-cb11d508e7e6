﻿@model  IEnumerable<DBLinker.Lib.RLT_Services>
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />
@{
    ViewBag.Title = "Services";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title"><PERSON><PERSON> <PERSON><PERSON></div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="index.html">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active"><PERSON><PERSON> <PERSON><PERSON></li>
                </ol>
            </div>
        </div>
        <!-- start widget -->
        <div class="row" id="dvAddUpdate">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Add Edit Services</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                            data-mdl-for="panel-button">
                            <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                            <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                            <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                        </ul>
                    </div>

                    @using (Html.BeginForm("Services", "CabYaari", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormCabYariServices" }))
                    {
                        @Html.AntiForgeryToken()
                        @Html.Hidden("PKID")

                        <div class="card-body">
                            <div class="row">

                                <div class="col-lg-9 p-t-20 row">
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ServiceName" name="ServiceName" type="text" value="" data-msgName="" maxlength="49">
                                            <label class="mdl-textfield__label required"><span>Service Name</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ServiceIconClass" name="ServiceIconClass" type="text" value="" data-msgName="" maxlength="49">
                                            <label class="mdl-textfield__label required"><span>Service Class</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-12 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <textarea rows="4" class="mdl-textfield__input" id="BriefIntroAboutService" name="BriefIntroAboutService" type="text" value="" data-msgName="" maxlength="199"></textarea>
                                            <label class="mdl-textfield__label required"><span>Short Brief Info About Services</span></label>
                                        </div>
                                    </div>

                                </div>

                                <div class="col-lg-3 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <label for="ServiceImagePath" class="file-uploader-lable">
                                            <div id="photo-div" class="file-uploader">
                                                <img src="#" class="file-uploder-img" id="img-viwer" />
                                                <p class="file-uploader-text"><strong>Click to<br />Choose Image</strong></p>
                                                <input type="file" id="ServiceImagePath" name="ServiceImagePath" accept="image/x-png,image/gif,image/jpeg" style="display:none">
                                            </div>
                                        </label>
                                        <label class="mdl-textfield__label required"><span>Service Image</span></label>
                                    </div>
                                    <div class="m-4">
                                        <span style=" color: #acaaaa;"> Set Service to active</span>
                                        <div class="row">
                                            <div>
                                                <input id="IsActiveTrue" name="IsActive" type="radio" value="true">
                                                <label for="IsActive"><span><span></span></span> Yes</label>
                                            </div>
                                            <div>
                                                <input id="IsActiveFalse" name="IsActive" type="radio" value="false">
                                                <label for="IsActive"><span><span></span></span>No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row p-t-20">
                                <div class="col-lg-12 p-t-20 ">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <textarea rows="4" class="mdl-textfield__input" id="AboutService" name="AboutService" type="text" value="" data-msgName="" maxlength="1000"></textarea>
                                        <label class="mdl-textfield__label">Detailed About for Service</label>
                                    </div>
                                </div>

                            </div>



                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>


        @*//div list pannel for vendor`s *@
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>Cab Yaari Services</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                @(PermittedAction.Is_Add ? Html.Raw("<div class='btn-group'><a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a></div>") : Html.Raw(""))
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <td>Photo</td>
                                        <th>Service<br />Name</th>
                                        <th>Service<br />Class</th>
                                        <th>Brief Info<br />About Service</th>
                                        <th>About<br />Service</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null)
                                    {
                                        foreach (var item in Model)
                                        {
                                            <tr class="odd gradeX">
                                                <td>
                                                    @Html.Raw(item.ServiceImagePath != null && item.ServiceImagePath != "" ? "<img src='../Docs/CabYaariServices/" + item.ServiceImagePath + "' height='60' width='60' />" : "<img src='../Content/img/no-user-image.gif'  height='60' width='60' />")
                                                </td>
                                                <td class="setString">@item.ServiceName</td>
                                                <td class="setString">@item.ServiceIconClass</td>
                                                <td class="setString">@item.BriefIntroAboutService</td>
                                                <td class="setString">@item.AboutService</td>
                                                <td>
                                                    @Html.Raw(item.IsActive == null
                                              ? "<span class='label label-sm label-warning'> Not Set </span>" : item.IsActive == true
                                              ? "<span class='label label-sm label-success'> Active </span>"
                                              : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID+")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>

                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit(" + item.PKID+ ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw(""))
                                                            </li>
                                                            @*<li>
                                                                    <a href="../VendorManagement/VendorManagerView?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.ID), "Vendor")" title="View Most favorite routes Info">
                                                                        <i class="material-icons">remove_red_eye</i>
                                                                        View
                                                                    </a>
                                                                </li>*@
                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @*//End div list pannel for vendor`s *@
    </div>
</div>


<script src="~/Scripts/CrudFile/CabYaariServices.js"></script>
﻿@model DBLinker.Lib.Model.M_Role_Menu_Mapping
<link href="~/Content/assets/css/RoleMenuMapping.css" rel="stylesheet" />
@if (Model.eRoleMenuMappingList != null)
{


    <div class="card-body ">

        <div class="col-12 card-body-col-12">
            <label>Serach To Desire Row: &nbsp;&nbsp;&nbsp;</label>
            <input onkeyup="searchInputTable(this)" class="col-6" type="text" placeholder="Search..">
            <label id="serchCount"></label>
            <br />
            <br />
            <div class="custom-control custom-checkbox custom-control-inline col-2 ">
                <input name="Is-View-Checkbok" id="Is-View-Checkbok" type="checkbox" onchange="checkUncheckView(this)" class="custom-control-input"> <label class="custom-control-label" for="Is-View-Checkbok">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Select All View</label>
            </div>
            <div class="custom-control custom-checkbox custom-control-inline col-2">
                <input name="Is-Add-Checkbok" id="Is-Add-Checkbok" type="checkbox" onchange="checkUncheckAdd(this)" class="custom-control-input">
                <label class="custom-control-label" for="Is-Add-Checkbok">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Select All Add</label>
            </div>
            <div class="custom-control custom-checkbox custom-control-inline col-2">
                <input name="Is-Edit-Checkbok" id="Is-Edit-Checkbok" type="checkbox" onchange="checkUncheckEdit(this)" class="custom-control-input">
                <label class="custom-control-label" for="Is-Edit-Checkbok">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Select All Edit</label>
            </div>
            <div class="custom-control custom-checkbox custom-control-inline col-2">
                <input name="Is-Delete-Checkbok" id="Is-Delete-Checkbok" type="checkbox" onchange="checkUncheckDelete(this)" class="custom-control-input">
                <label class="custom-control-label" id="Is-View-Checkbok" for="Is-Delete-Checkbok">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Select All Delete</label>
            </div>
        </div>

        <div class="table-scrollable">
            <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="27%">Menu Name</th>
                        <th width="5%">View</th>
                        <th width="5%">Add</th>
                        <th width="5%">Edit</th>
                        <th width="5%">Delete</th>
                        <th width="3%">Order<br />Number</th>
                        <th width="10%">Action<br />Name</th>
                        <th width="10%">Controller<br />Name</th>
                        <th width="15%">Date<br />Created</th>
                    </tr>
                </thead>
                <tbody id="ManuListTable">
                    @if (Model.eRoleMenuMappingList != null)
                    {
                        if (Model.eRoleMenuMappingList.Count != 0)
                        {
                            for (int i = 0; i < Model.eRoleMenuMappingList.Count; i++)
                            {
                                <tr class="odd gradeX">
                                  
                                    <td>@(i+1)</td>
                                    <td>
                                        @Html.HiddenFor(a => Model.eRoleMenuMappingList[i].Menu_Id)
                                        <a href="@Model.eRoleMenuMappingList[i].MenuURL" target="_blank" title="@Model.eRoleMenuMappingList[i].MenuURL">

                                            <i class="@Model.eRoleMenuMappingList[i].MenuIcon"></i>
                                            @Model.eRoleMenuMappingList[i].MenuName
                                        </a>

                                        @(unchecked(Model.eRoleMenuMappingList[i].OrderNumber == (int)Model.eRoleMenuMappingList[i].OrderNumber)
                                            ? Html.Raw("<label class='table-parent-icon'  data-toggle='modal' data-target='#js-model'   data-order=" + Model.eRoleMenuMappingList[i].OrderNumber+" title='Show Child`s Info'>+</label>")
                                            :Html.Raw("<label class='table-child-icon' data-toggle='modal' data-target='#js-model' data-order=" + (int)Model.eRoleMenuMappingList[i].OrderNumber + " title='Show Parent Info'>+</label>"))


                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(a => Model.eRoleMenuMappingList[i].Is_View, new { @class = "isViewCheckbox" })

                                    </td>

                                    <td>
                                        @Html.CheckBoxFor(a => Model.eRoleMenuMappingList[i].Is_Add, new { @class = "isAddCheckbox" })
                                    </td>

                                    <td>
                                        @Html.CheckBoxFor(a => Model.eRoleMenuMappingList[i].Is_Edit, new { @class = "isEditCheckbox" })

                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(a => Model.eRoleMenuMappingList[i].Is_Delete, new { @class = "isDeleteCheckbox" })

                                    </td>
                                    <td>@Model.eRoleMenuMappingList[i].OrderNumber</td>
                                    <td>
                                        @(Model.eRoleMenuMappingList[i].ActionName == null
                                            ? "n/a" : Model.eRoleMenuMappingList[i].ActionName )
                                    </td>
                                    <td>
                                        @(Model.eRoleMenuMappingList[i].ControllerName == null
                                            ? "n/a" : @Model.eRoleMenuMappingList[i].ControllerName)
                                    </td>
                                    <td>
                                        @(Model.eRoleMenuMappingList[i].CreatedDate != null
                                                ? Html.Raw(Convert.ToDateTime(Model.eRoleMenuMappingList[i].CreatedDate).ToString("ddd dd MMM yyyy")) : Html.Raw("n/a"))
                                    </td>
                                     
                                        @Html.HiddenFor(a => Model.eRoleMenuMappingList[i].ActionName)
                                     
                                </tr>
                            }
                        }

                    }

                </tbody>
            </table>
        </div>

    </div>


}




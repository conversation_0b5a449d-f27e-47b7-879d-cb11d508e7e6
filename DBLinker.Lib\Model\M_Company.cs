﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Company
    {       
        
        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        [Remote("Check_Car_Company", "Common", ErrorMessage = "Company Name already exists!")]
        public string Company_Name { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }

    }
}

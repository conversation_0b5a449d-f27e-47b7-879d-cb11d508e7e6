﻿


#circleBar {
    margin-top: 10px;
    text-align: center;
    font-family: '<PERSON>aa<PERSON><PERSON><PERSON>', cursive;
}

#circleBar .round {
    min-height: 255px;
    margin-top: 30px;
    position: relative;
    margin-bottom: 20px;
}

    #circleBar .round strong {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -50px;
        transform: translate(-50%);
        font-size: 40px;
        color: #212121;
        font-weight: 100;
    }

#circleBar span {
    display: block;
    color: #999;
    font-size: 16px;
    margin-top: 10px;
}

    #circleBar span i {
        color: #ff5c5c;
        font-size: 22px;
        margin-right: 6px;
    }

section button:hover {
    background-color: #ff5c5c;
}

﻿@model DBLinker.Lib.RLT_DRIVER_APPROVE_STATUS
@{
    ViewBag.Title = "DriveApproveStatus";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/css/jquery.dataTables.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/select/1.2.2/css/select.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.2/js/dataTables.select.min.js"></script>
<link href="~/Content/toastr.min.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<section class="content">
    .
    <div class="row">
        <div class="col-md-12">
            <!--breadcrumbs start -->
            <ul class="breadcrumb">
                <li><a href="#"><i class="fa fa-user"></i> Admin</a></li>
                <li><a href="#"><i class="fa fa-flag"></i> Driver Approve Manager</a></li>

            </ul>
            <!--breadcrumbs end -->
        </div>
    </div>
    @using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.Is_Active)
        @Html.HiddenFor(x => x.PKID)
        <div class="row" id="dvAddUpdate">
            <div class="col-md-12">
                <div class="col-lg-12">
                    <section class="panel">
                        <header class="panel-heading">
                            Add Update Drive Approve Status
                        </header>
                        <div class="panel-body">

                            <div class="form-group">
                                <div class="col-sm-2">
                                    Drive Approve Status Name
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.Status, new { @class = "form-control", @placeholder = "enter Drive Approve Status name !" })

                                </div>
                              

                            </div>

                        </div>
                        <footer class="panel-footer">
                            <center>
                                <button type="button" class="btn btn-info" id="btnSave">Save</button>
                                <button type="button" class="btn btn-info" id="btnUpdate">Update</button>
                                <button type="reset" class="btn btn-danger" id="btnReset">cancel</button>
                            </center>
                        </footer>
                    </section>
                </div>

            </div>
        </div>

    }

    <section class="panel tasks-widget">
        <header class="panel-heading">
            Drive Approve Status Manager
            <button type="button" id="btnAdd" class="btn btn-info" style="float:right;margin-top: -5px;">Add Drive Approve Status</button>
        </header>
        <div class="panel-body">

            <div class="task-content">

                <table id="example" class="display" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Drive Status Name</th>
                
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th></th>
                            <th>Drive Status  Name</th>
                            
                            <th>Status</th>
                            <th></th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @if (ViewBag.DriverAprStatus != null)
                        {
                            foreach (var item in ViewBag.DriverAprStatus)
                            {

                                <tr>
                                    <input type="hidden" id="<EMAIL>" value="@item.Status" />
                                    <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />

                                    <td></td>
                                    <td>@item.Status</td>

                                  

                                    <td>@item.Is_Active</td>

                                    <td>
                                        <button class="btn btn-default btn-xs" onclick="ActivateDeactivate(@item.PKID)" type="button"><i class="fa fa-check"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Edit(@item.PKID)" type="button"><i class="fa fa-pencil"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Delete(@item.PKID)" type="button"><i class="fa fa-times"></i></button>
                                    </td>
                                </tr>
                            }

                        }

                    </tbody>
                </table>
            </div>


        </div>
    </section>

</section>
<script src="~/Scripts/CrudFile/DriveApproveStatusCrudJs.js"></script>



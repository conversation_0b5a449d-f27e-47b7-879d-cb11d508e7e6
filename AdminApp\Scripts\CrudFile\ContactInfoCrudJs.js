﻿$(document).ready(function ()
{
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function ()
    {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function ()
    {
        if (IsValidate())
        {

            $.ajax({
                url: 'ContactInformationManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response)
                {
                    if (response > 0) {
                        toastr.success("Contact Information Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Contact Information Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {


        if (IsValidate())
        {

            $.ajax({
                url: 'ContactInformationManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response)
                {
                    if (response > 0) {
                        toastr.success("Contact Information Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Contact Information Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response)
                {
                    alert(response.responseText);
                }
            });

        }
    });


   



    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
   
   
    $("#Address").val($("#name_" + Id).val());
    $("#SalesTelNo").val($("#salesno_" + Id).val());
    $("#BusinessTelNo").val($("#busno_" + Id).val());
    $("#FaxNo").val($("#faxno_" + Id).val());
    $("#SalesEmailNo").val($("#salesemail_" + Id).val());
    $("#BusinessEmailNo").val($("#busemail_" + Id).val());
    $("#WorkingHours").val($("#workhours_" + Id).val());
    $("#GoogleMapLink").val($("#mapurl_" + Id).val());
    $("#isActive").val($("#status_" + Id).val());

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id)
{

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Address").val($("#name_" + Id).val());
    $("#SalesTelNo").val($("#salesno_" + Id).val());
    $("#BusinessTelNo").val($("#busno_" + Id).val());
    $("#FaxNo").val($("#faxno_" + Id).val());
    $("#SalesEmailNo").val($("#salesemail_" + Id).val());
    $("#BusinessEmailNo").val($("#busemail_" + Id).val());
    $("#WorkingHours").val($("#workhours_" + Id).val());
    $("#GoogleMapLink").val($("#mapurl_" + Id).val());
   

    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#isActive").val(true);
    }
    else {
        $("#isActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#ID").val() != "") {
        $.ajax({
            url: 'ContactInformationManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Contact Information Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Contact Information Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}

function ActivateDeactivate(Id)
{
    $("#Address").val($("#name_" + Id).val());
    $("#SalesTelNo").val($("#salesno_" + Id).val());
    $("#BusinessTelNo").val($("#busno_" + Id).val());
    $("#FaxNo").val($("#faxno_" + Id).val());
    $("#SalesEmailNo").val($("#salesemail_" + Id).val());
    $("#BusinessEmailNo").val($("#busemail_" + Id).val());
    $("#WorkingHours").val($("#workhours_" + Id).val());
    $("#GoogleMapLink").val($("#mapurl_" + Id).val());

    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#isActive").val(true);
    }
    else {
        $("#isActive").val(false);
    }

    $("#hdnOperation").val('U');

    if ($("#ID").val() != "") {
        $.ajax({
            url: 'ContactInformationManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Contact Information Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Contact Information Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }


}

function resetForm() {


    $("#Address").val('');
    $("#SalesTelNo").val('');
    $("#BusinessTelNo").val('');
    $("#FaxNo").val('');
    $("#SalesEmailNo").val('');
    $("#BusinessEmailNo").val('');
    $("#WorkingHours").val('');
    $("#GoogleMapLink").val('');
   

}

function IsValidate()
{
    
    if ($("#Address").val() == "") {
        toastr.clear();
        toastr.error("Please enter company address !");
        return false;
    }
    if ($("#SalesTelNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter sales number !");
        return false;
    }
    if ($("#BusinessTelNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter business number !");
        return false;

    }
    if ($("#SalesEmailNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter sales email id !");
        return false;
    }
    if ($("#BusinessEmailNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter business email id !");
        return false;
    }
    if ($("#WorkingHours").val() == "") {
        toastr.clear();
        toastr.error("Please enter working hours!");
        return false;

    }
   
  

    return true;
}
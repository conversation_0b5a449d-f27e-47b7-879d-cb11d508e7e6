﻿using AdminApp.Filters;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Repository;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;

namespace AdminApp.Controllers
{
    public class CommonController : Controller
    {

         RLTDBContext entity;       
        public CommonController()
        {
            entity = new RLTDBContext();
           

        }

        public ActionResult Check_Vendor_MemberID(string Vendor_Member_ID)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_Vendor_Details where a.Vendor_Member_Id == Vendor_Member_ID.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Car_Number(string Car_Number)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_CAR_DETAILS where a.Car_Number == Car_Number.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_City_Name(string City_Name,int PKID)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_CITY where a.City_Name == City_Name.Trim() && a.PKID!=PKID select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Car_Company(string Company_Name)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_CAR_COMPANY where a.Company_Name == Company_Name.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Car_Model_Name(string Car_Model_Name)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_CAR_MODEL where a.Car_Model_Name == Car_Model_Name.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }


        public ActionResult Check_Booking_Status(string BOOKING_STATUS)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_BOOKING_STATUS where a.BOOKING_STATUS == BOOKING_STATUS.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }


        public ActionResult Check_Trip_Type(string Trip_Type)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_TRIP_TYPES where a.Trip_Type == Trip_Type.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Discount_Coupon_Code(string Discount_Coupon)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_DISCOUNT_COUPON where a.Discount_Coupon == Discount_Coupon.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }
        public ActionResult Check_Vendor_Company_Name(string Vendor_Company_Name)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_Vendor_Details where a.Vendor_Company_Name == Vendor_Company_Name.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Account_Number(string Account_Number)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_Vendor_Bank_Details where a.Account_Number == Account_Number.Trim() select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }

        public ActionResult Check_Doc_Name(string Doc_Name, string Doc_For)
        {
            bool ifExist = false;
            try
            {
                var v = from a in entity.RLT_DOCUMNETS_NAME where a.Doc_Name == Doc_Name.Trim() && a.Doc_For== Doc_For select a;
                if (v.Count() > 0)
                    ifExist = true;
                else
                    ifExist = false;

                return Json(!ifExist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(false, JsonRequestBehavior.AllowGet);

            }

        }
    }
}
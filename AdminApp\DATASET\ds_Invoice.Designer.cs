﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace AdminApp.DATASET {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("ds_Invoice")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class ds_Invoice : global::System.Data.DataSet {
        
        private dt_InvoiceDataTable tabledt_Invoice;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public ds_Invoice() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected ds_Invoice(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["dt_Invoice"] != null)) {
                    base.Tables.Add(new dt_InvoiceDataTable(ds.Tables["dt_Invoice"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public dt_InvoiceDataTable dt_Invoice {
            get {
                return this.tabledt_Invoice;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public override global::System.Data.DataSet Clone() {
            ds_Invoice cln = ((ds_Invoice)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["dt_Invoice"] != null)) {
                    base.Tables.Add(new dt_InvoiceDataTable(ds.Tables["dt_Invoice"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars(bool initTable) {
            this.tabledt_Invoice = ((dt_InvoiceDataTable)(base.Tables["dt_Invoice"]));
            if ((initTable == true)) {
                if ((this.tabledt_Invoice != null)) {
                    this.tabledt_Invoice.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void InitClass() {
            this.DataSetName = "ds_Invoice";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/ds_Invoice.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tabledt_Invoice = new dt_InvoiceDataTable();
            base.Tables.Add(this.tabledt_Invoice);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private bool ShouldSerializedt_Invoice() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            ds_Invoice ds = new ds_Invoice();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public delegate void dt_InvoiceRowChangeEventHandler(object sender, dt_InvoiceRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class dt_InvoiceDataTable : global::System.Data.TypedTableBase<dt_InvoiceRow> {
            
            private global::System.Data.DataColumn columnInvoice_No;
            
            private global::System.Data.DataColumn columnInvoice_Date;
            
            private global::System.Data.DataColumn columnBasic_Fare;
            
            private global::System.Data.DataColumn columnDriver_Charge;
            
            private global::System.Data.DataColumn columnToll_Charge;
            
            private global::System.Data.DataColumn columnGST;
            
            private global::System.Data.DataColumn columnFare;
            
            private global::System.Data.DataColumn columnGST_Fare;
            
            private global::System.Data.DataColumn columnName;
            
            private global::System.Data.DataColumn columnMobile_No1;
            
            private global::System.Data.DataColumn columnPickUp_Address;
            
            private global::System.Data.DataColumn columnDropOff_Address;
            
            private global::System.Data.DataColumn columnCar_Number;
            
            private global::System.Data.DataColumn columnCar_Model_Name;
            
            private global::System.Data.DataColumn columnDriver_Name;
            
            private global::System.Data.DataColumn columnDriver_Photo;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceDataTable() {
                this.TableName = "dt_Invoice";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal dt_InvoiceDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected dt_InvoiceDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Invoice_NoColumn {
                get {
                    return this.columnInvoice_No;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Invoice_DateColumn {
                get {
                    return this.columnInvoice_Date;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Basic_FareColumn {
                get {
                    return this.columnBasic_Fare;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Driver_ChargeColumn {
                get {
                    return this.columnDriver_Charge;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Toll_ChargeColumn {
                get {
                    return this.columnToll_Charge;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn GSTColumn {
                get {
                    return this.columnGST;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn FareColumn {
                get {
                    return this.columnFare;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn GST_FareColumn {
                get {
                    return this.columnGST_Fare;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn NameColumn {
                get {
                    return this.columnName;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Mobile_No1Column {
                get {
                    return this.columnMobile_No1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn PickUp_AddressColumn {
                get {
                    return this.columnPickUp_Address;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn DropOff_AddressColumn {
                get {
                    return this.columnDropOff_Address;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Car_NumberColumn {
                get {
                    return this.columnCar_Number;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Car_Model_NameColumn {
                get {
                    return this.columnCar_Model_Name;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Driver_NameColumn {
                get {
                    return this.columnDriver_Name;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Driver_PhotoColumn {
                get {
                    return this.columnDriver_Photo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceRow this[int index] {
                get {
                    return ((dt_InvoiceRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_InvoiceRowChangeEventHandler dt_InvoiceRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_InvoiceRowChangeEventHandler dt_InvoiceRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_InvoiceRowChangeEventHandler dt_InvoiceRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_InvoiceRowChangeEventHandler dt_InvoiceRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void Adddt_InvoiceRow(dt_InvoiceRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceRow Adddt_InvoiceRow(
                        string Invoice_No, 
                        string Invoice_Date, 
                        string Basic_Fare, 
                        string Driver_Charge, 
                        string Toll_Charge, 
                        string GST, 
                        string Fare, 
                        string GST_Fare, 
                        string Name, 
                        string Mobile_No1, 
                        string PickUp_Address, 
                        string DropOff_Address, 
                        string Car_Number, 
                        string Car_Model_Name, 
                        string Driver_Name, 
                        string Driver_Photo) {
                dt_InvoiceRow rowdt_InvoiceRow = ((dt_InvoiceRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        Invoice_No,
                        Invoice_Date,
                        Basic_Fare,
                        Driver_Charge,
                        Toll_Charge,
                        GST,
                        Fare,
                        GST_Fare,
                        Name,
                        Mobile_No1,
                        PickUp_Address,
                        DropOff_Address,
                        Car_Number,
                        Car_Model_Name,
                        Driver_Name,
                        Driver_Photo};
                rowdt_InvoiceRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowdt_InvoiceRow);
                return rowdt_InvoiceRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public override global::System.Data.DataTable Clone() {
                dt_InvoiceDataTable cln = ((dt_InvoiceDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new dt_InvoiceDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal void InitVars() {
                this.columnInvoice_No = base.Columns["Invoice_No"];
                this.columnInvoice_Date = base.Columns["Invoice_Date"];
                this.columnBasic_Fare = base.Columns["Basic_Fare"];
                this.columnDriver_Charge = base.Columns["Driver_Charge"];
                this.columnToll_Charge = base.Columns["Toll_Charge"];
                this.columnGST = base.Columns["GST"];
                this.columnFare = base.Columns["Fare"];
                this.columnGST_Fare = base.Columns["GST_Fare"];
                this.columnName = base.Columns["Name"];
                this.columnMobile_No1 = base.Columns["Mobile_No1"];
                this.columnPickUp_Address = base.Columns["PickUp_Address"];
                this.columnDropOff_Address = base.Columns["DropOff_Address"];
                this.columnCar_Number = base.Columns["Car_Number"];
                this.columnCar_Model_Name = base.Columns["Car_Model_Name"];
                this.columnDriver_Name = base.Columns["Driver_Name"];
                this.columnDriver_Photo = base.Columns["Driver_Photo"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            private void InitClass() {
                this.columnInvoice_No = new global::System.Data.DataColumn("Invoice_No", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnInvoice_No);
                this.columnInvoice_Date = new global::System.Data.DataColumn("Invoice_Date", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnInvoice_Date);
                this.columnBasic_Fare = new global::System.Data.DataColumn("Basic_Fare", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBasic_Fare);
                this.columnDriver_Charge = new global::System.Data.DataColumn("Driver_Charge", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDriver_Charge);
                this.columnToll_Charge = new global::System.Data.DataColumn("Toll_Charge", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnToll_Charge);
                this.columnGST = new global::System.Data.DataColumn("GST", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGST);
                this.columnFare = new global::System.Data.DataColumn("Fare", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFare);
                this.columnGST_Fare = new global::System.Data.DataColumn("GST_Fare", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGST_Fare);
                this.columnName = new global::System.Data.DataColumn("Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnName);
                this.columnMobile_No1 = new global::System.Data.DataColumn("Mobile_No1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMobile_No1);
                this.columnPickUp_Address = new global::System.Data.DataColumn("PickUp_Address", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPickUp_Address);
                this.columnDropOff_Address = new global::System.Data.DataColumn("DropOff_Address", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDropOff_Address);
                this.columnCar_Number = new global::System.Data.DataColumn("Car_Number", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCar_Number);
                this.columnCar_Model_Name = new global::System.Data.DataColumn("Car_Model_Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCar_Model_Name);
                this.columnDriver_Name = new global::System.Data.DataColumn("Driver_Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDriver_Name);
                this.columnDriver_Photo = new global::System.Data.DataColumn("Driver_Photo", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDriver_Photo);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceRow Newdt_InvoiceRow() {
                return ((dt_InvoiceRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new dt_InvoiceRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Type GetRowType() {
                return typeof(dt_InvoiceRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.dt_InvoiceRowChanged != null)) {
                    this.dt_InvoiceRowChanged(this, new dt_InvoiceRowChangeEvent(((dt_InvoiceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.dt_InvoiceRowChanging != null)) {
                    this.dt_InvoiceRowChanging(this, new dt_InvoiceRowChangeEvent(((dt_InvoiceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.dt_InvoiceRowDeleted != null)) {
                    this.dt_InvoiceRowDeleted(this, new dt_InvoiceRowChangeEvent(((dt_InvoiceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.dt_InvoiceRowDeleting != null)) {
                    this.dt_InvoiceRowDeleting(this, new dt_InvoiceRowChangeEvent(((dt_InvoiceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void Removedt_InvoiceRow(dt_InvoiceRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                ds_Invoice ds = new ds_Invoice();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "dt_InvoiceDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class dt_InvoiceRow : global::System.Data.DataRow {
            
            private dt_InvoiceDataTable tabledt_Invoice;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal dt_InvoiceRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tabledt_Invoice = ((dt_InvoiceDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Invoice_No {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Invoice_NoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Invoice_No\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Invoice_NoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Invoice_Date {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Invoice_DateColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Invoice_Date\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Invoice_DateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Basic_Fare {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Basic_FareColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Basic_Fare\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Basic_FareColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Driver_Charge {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Driver_ChargeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Driver_Charge\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Driver_ChargeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Toll_Charge {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Toll_ChargeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Toll_Charge\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Toll_ChargeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string GST {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.GSTColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'GST\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.GSTColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Fare {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.FareColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Fare\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.FareColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string GST_Fare {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.GST_FareColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'GST_Fare\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.GST_FareColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Name\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Mobile_No1 {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Mobile_No1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Mobile_No1\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Mobile_No1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string PickUp_Address {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.PickUp_AddressColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PickUp_Address\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.PickUp_AddressColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string DropOff_Address {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.DropOff_AddressColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'DropOff_Address\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.DropOff_AddressColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Car_Number {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Car_NumberColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Car_Number\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Car_NumberColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Car_Model_Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Car_Model_NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Car_Model_Name\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Car_Model_NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Driver_Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Driver_NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Driver_Name\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Driver_NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Driver_Photo {
                get {
                    try {
                        return ((string)(this[this.tabledt_Invoice.Driver_PhotoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Driver_Photo\' in table \'dt_Invoice\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Invoice.Driver_PhotoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsInvoice_NoNull() {
                return this.IsNull(this.tabledt_Invoice.Invoice_NoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetInvoice_NoNull() {
                this[this.tabledt_Invoice.Invoice_NoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsInvoice_DateNull() {
                return this.IsNull(this.tabledt_Invoice.Invoice_DateColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetInvoice_DateNull() {
                this[this.tabledt_Invoice.Invoice_DateColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsBasic_FareNull() {
                return this.IsNull(this.tabledt_Invoice.Basic_FareColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetBasic_FareNull() {
                this[this.tabledt_Invoice.Basic_FareColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDriver_ChargeNull() {
                return this.IsNull(this.tabledt_Invoice.Driver_ChargeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDriver_ChargeNull() {
                this[this.tabledt_Invoice.Driver_ChargeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsToll_ChargeNull() {
                return this.IsNull(this.tabledt_Invoice.Toll_ChargeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetToll_ChargeNull() {
                this[this.tabledt_Invoice.Toll_ChargeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsGSTNull() {
                return this.IsNull(this.tabledt_Invoice.GSTColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetGSTNull() {
                this[this.tabledt_Invoice.GSTColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsFareNull() {
                return this.IsNull(this.tabledt_Invoice.FareColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetFareNull() {
                this[this.tabledt_Invoice.FareColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsGST_FareNull() {
                return this.IsNull(this.tabledt_Invoice.GST_FareColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetGST_FareNull() {
                this[this.tabledt_Invoice.GST_FareColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNameNull() {
                return this.IsNull(this.tabledt_Invoice.NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNameNull() {
                this[this.tabledt_Invoice.NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsMobile_No1Null() {
                return this.IsNull(this.tabledt_Invoice.Mobile_No1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetMobile_No1Null() {
                this[this.tabledt_Invoice.Mobile_No1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPickUp_AddressNull() {
                return this.IsNull(this.tabledt_Invoice.PickUp_AddressColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPickUp_AddressNull() {
                this[this.tabledt_Invoice.PickUp_AddressColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDropOff_AddressNull() {
                return this.IsNull(this.tabledt_Invoice.DropOff_AddressColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDropOff_AddressNull() {
                this[this.tabledt_Invoice.DropOff_AddressColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCar_NumberNull() {
                return this.IsNull(this.tabledt_Invoice.Car_NumberColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCar_NumberNull() {
                this[this.tabledt_Invoice.Car_NumberColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCar_Model_NameNull() {
                return this.IsNull(this.tabledt_Invoice.Car_Model_NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCar_Model_NameNull() {
                this[this.tabledt_Invoice.Car_Model_NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDriver_NameNull() {
                return this.IsNull(this.tabledt_Invoice.Driver_NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDriver_NameNull() {
                this[this.tabledt_Invoice.Driver_NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDriver_PhotoNull() {
                return this.IsNull(this.tabledt_Invoice.Driver_PhotoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDriver_PhotoNull() {
                this[this.tabledt_Invoice.Driver_PhotoColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public class dt_InvoiceRowChangeEvent : global::System.EventArgs {
            
            private dt_InvoiceRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceRowChangeEvent(dt_InvoiceRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_InvoiceRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591
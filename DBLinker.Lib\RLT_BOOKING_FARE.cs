//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_BOOKING_FARE
    {
        public int PKID { get; set; }
        public Nullable<int> City_From { get; set; }
        public Nullable<int> City_To { get; set; }
        public Nullable<int> Trip_Type_Id { get; set; }
        public Nullable<int> Car_Category_Id { get; set; }
        public Nullable<decimal> Basic_Fare { get; set; }
        public Nullable<decimal> Driver_Charge { get; set; }
        public Nullable<decimal> Toll_Charge { get; set; }
        public Nullable<decimal> Total_Fare { get; set; }
        public Nullable<decimal> GST { get; set; }
        public Nullable<decimal> GST_Amount { get; set; }
        public Nullable<decimal> Final_Fare { get; set; }
        public string Remark { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Updated_By { get; set; }
    }
}

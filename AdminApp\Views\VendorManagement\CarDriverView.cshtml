﻿@model DBLinker.Lib.Model.M_CarDriver_Details
@using AdminApp.Mapper;

@{
    ViewBag.Title = "Car Driver Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $("#CarDriverManager").parent().parent().find("a").next().slideToggle(500);
        $("#CarDriverManager").parent().parent().toggleClass('toggled');
        $("#CarDriverManager").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">View Car Driver Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/CarDriverManager">Car Driver Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Car Driver Details</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Car Driver Profile : @Model.Driver_Name</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>

                    </div>
                    <div class="card-body row">
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Special_Remarking, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Drive Profile Remarks</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Vendor Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Owner_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Car Owner</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Car_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Car</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Driver Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @if (!string.IsNullOrEmpty(Model.Driver_Photo))
                                {
                                    <img src="~/Docs/DriverPhoto/@Model.Driver_Photo" id="Driver_Photo" height="80" width="120" />
                                }
                                else
                                {
                                    <img src="~/Content/img/no-user-image.gif" id="Driver_Photo" height="80" width="120" />
                                }
                                <label class="mdl-textfield__label "><span>Photo</span></label>

                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_DOB, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Driver DOB</span> </label>
                            </div>
                        </div>


                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Drive_Phone_1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Phone 1</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Drive_Phone_2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Phone 2</label>
                            </div>
                        </div>


                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_DL, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label "><span>Driver Commercial DL</span></label>
                            </div>
                        </div>

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Aadhaar, new { @class = "mdl-textfield__input txtbx" })
                                <label class="mdl-textfield__label "><span>Driver Aadhaar</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Religion, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Driver Religion</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Father_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Father Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Mother_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Mother Name</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Driver_Gender, "Male", new { @id = "Driver_Gender1" })
                                        <label for="radio1"><span><span></span></span> Male</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Driver_Gender, "Female", new { @id = "Driver_Gender2" })
                                        <label for="radio2"><span><span></span></span>Female</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label"><span>Driver Gender</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Driver_Marrital_Status, "Married", new { @id = "Driver_Marrital_Status1" })
                                        <label for="radio1"><span><span></span></span> Married</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Driver_Marrital_Status, "Unmarried", new { @id = "Driver_Marrital_Status2" })
                                        <label for="radio2"><span><span></span></span>Unmarried</label>
                                    </div>

                                    <div style="clear:both;"></div>
                                </div>
                                <label class="mdl-textfield__label"><span>Marrital Status</span></label>
                            </div>
                        </div>

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Driver_Drinking_Status, "Drinker", new { @id = "Driver_Drinking_Status1" })
                                        <label for="radio1"><span><span></span></span> Drinker</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Driver_Drinking_Status, "Non-Drinker", new { @id = "Driver_Drinking_Status2" })
                                        <label for="radio2"><span><span></span></span>Non-Drinker</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label"><span>Drinking Status</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Driver_Smoking_Status, "Smoker", new { @id = "Driver_Smoking_Status1" })
                                        <label for="radio1"><span><span></span></span> Smoker</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Driver_Smoking_Status, "Non-Smoker", new { @id = "Driver_Smoking_Status2" })
                                        <label for="radio2"><span><span></span></span>Non-Smoker</label>
                                    </div>

                                    <div style="clear:both;"></div>
                                </div>
                                <label class="mdl-textfield__label"><span>Smoking Status</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Driver_Eating_Type, "Vegetarian", new { @id = "Driver_Eating_Type1" })
                                        <label for="radio1"><span><span></span></span> Vegetarian</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Driver_Eating_Type, "Non Vegetarian", new { @id = "Driver_Eating_Type2" })
                                        <label for="radio2"><span><span></span></span> Non Vegetarian</label>
                                    </div>
                                    <div style="clear:both;"></div>
                                </div>
                                <label class="mdl-textfield__label"><span>Driver Eating Type</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Current Address </span>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Current_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                <label class="mdl-textfield__label "><span>Address 1</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Current_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                <label class="mdl-textfield__label "><span>Address 2</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-2 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Current_Address_State_Name, new { @class = "mdl-textfield__input", @maxlength = "300" })

                                <label class="mdl-textfield__label "><span>State</span></label>
                            </div>
                        </div>
                        <div class="col-lg-2 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Driver_Current_Address_City_Name, new { @class = "mdl-textfield__input", @maxlength = "300" })

                                <label class="mdl-textfield__label "><span>City</span></label>
                            </div>
                        </div>




                        <div class="col-lg-12 p-t-20">
                            <span style="font-size:15px;font-weight:bold;">Permanent Address </span>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Permanent_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                <label class="mdl-textfield__label "><span>Address 1</span></label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Permanent_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                <label class="mdl-textfield__label "><span>Address 2</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-2 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Permanent_Address_State_Name, new { @class = "mdl-textfield__input", @maxlength = "300" })

                                <label class="mdl-textfield__label "><span>State</span></label>
                            </div>
                        </div>
                        <div class="col-lg-2 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Driver_Permanent_Address_City_Name, new { @class = "mdl-textfield__input", @maxlength = "300" })

                                <label class="mdl-textfield__label "><span>City</span></label>
                            </div>
                        </div>



                    </div>


                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-box">
                                <div class="card-head">
                                    <header> Documents</header>
                                </div>
                                <div class="card-body ">
                                    <div class="table-scrollable">
                                        <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                            <thead>
                                                <tr>
                                                    <th width="40%">Document  Name</th>
                                                    <th width="20%">View</th>
                                                    <th width="20%">End Date</th>
                                                    <th width="20%">Verified</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @if (Model.M_CarDriver_DocsList != null)
                                            {
                                                if (Model.M_CarDriver_DocsList.Count != 0)
                                                {
                                                    for (int i = 0; i < Model.M_CarDriver_DocsList.Count; i++)
                                                    {
                                                        <tr class="odd gradeX">
                                                            <td>
                                                                @Html.DisplayFor(a => Model.M_CarDriver_DocsList[i].CarDriver_Doc_Name)
                                                            </td>

                                                            <td>
                                                                @if (!string.IsNullOrEmpty(Model.M_CarDriver_DocsList[i].CarDriver_Doc_Path))
                                                            {
                                                                <a onClick='return popupSubWindow(this)' href=/Docs/DriverDocs/@Model.M_CarDriver_DocsList[i].CarDriver_Doc_Path><img src='~/Content/img/attachment.png' height='25' width='25' title="Click here to view the document."></a>
                                                        }
                                                            </td>
                                                            <td>
                                                                @Html.DisplayFor(a => Model.M_CarDriver_DocsList[i].CarDriver_Doc_EndDate)
                                                            </td>
                                                            <td>
                                                                <div class="col-md-10">
                                                                    @if (Model.M_CarDriver_DocsList[i].Is_Verified == true)
                                                                {
                                                                    <label> Yes</label>
                                                            }
                                                            else
                                                            {
                                                                <label> No</label>
                                                        }
                                                                </div>

                                                            </td>
                                                        </tr>
                                                }
                                                /**/
                                            }

                                        }

                                            </tbody>
                                        </table>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/CarDriverManager">Back</a>
            </div>
        </div>

    </div>
</div>


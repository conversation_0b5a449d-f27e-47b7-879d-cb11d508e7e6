﻿@model DBLinker.Lib.Model.M_Discount_Coupon
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />
@{
    ViewBag.Title = "Discount Coupon Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Discount Coupon Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Discount Coupon Details</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("DiscountCouponManager", "BookingFareManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormDiscountCouponManager" }))
        {
            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Discount Coupon Details</header>

                        </div>
                        <div class="card-body row">
                            <div class="col-lg-9 p-t-20 row">
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Coupon_Name, new { @class = "mdl-textfield__input" })
                                        <label class="mdl-textfield__label required"><span>Coupon Name</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Discount_Coupon, new { @class = "mdl-textfield__input" })
                                        @Html.ValidationMessageFor(model => model.Discount_Coupon, "", new { @class = "text-danger" })
                                        <label class="mdl-textfield__label required"><span>Coupon Code</span></label>
                                    </div>
                                </div>

                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Discount, new { @class = "mdl-textfield__input Decimaltxtbx" })
                                        <label class="mdl-textfield__label required"><span>Discount (%)</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Max_Discount, new { @class = "mdl-textfield__input txtbx" })
                                        <label class="mdl-textfield__label required"><span>Max Discount</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-4 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Fare_When_Applied, new { @class = "mdl-textfield__input txtbx" })
                                        <label class="mdl-textfield__label required"><span>Min Fare when Applied</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-4 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Coupon_Last_Date, new { @class = "mdl-textfield__input", type = "date" })
                                        <label class="mdl-textfield__label required"><span>Expiry Date</span> </label>
                                    </div>
                                </div>
                                <div class="col-lg-4 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.CouponMaxTimeAllowance, new { @class = "mdl-textfield__input txtbx" })
                                        <label class="mdl-textfield__label required"><span>Max Time Allowance</span> </label>
                                    </div>
                                </div>

                            </div>
                            <div class="col-lg-3 p-t-20 row">
                                <div class="col-lg-12 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <label for="discountCopounImage" class="file-uploader-lable">
                                            <div id="photo-div" class="file-uploader">
                                                <img src="#" class="file-uploder-img" id="img-viwer" />
                                                <p class="file-uploader-text"><strong>Click to<br />Choose Image</strong></p>
                                                <input type="file" id="discountCopounImage" name="discountCopounImage" accept="image/x-png,image/gif,image/jpeg" style="display:none">
                                            </div>
                                        </label>
                                        <label class="mdl-textfield__label required"><span>Discount Banner</span></label>
                                    </div>
                                </div>
                                <div class="col-lg-12 p-t-20">
                                    <span style=" color: #acaaaa;"> Set Discount Coupon to active</span>
                                    <div class="row">
                                        <div>
                                            <input id="IsActiveTrue" name="Is_Active" type="radio" value="true">
                                            <label for="IsActive"><span><span></span></span> Yes</label>
                                        </div>
                                        <div>
                                            <input id="IsActiveFalse" name="Is_Active" type="radio" value="false">
                                            <label for="IsActive"><span><span></span></span>No</label>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextAreaFor(x => x.Coupon_Remark, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label">Short Description About Discount Coupon</label>
                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Discount Coupon</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='#' id='btnAdd' class='btn btn-info'> Add New <i class='fa fa-plus'></i></a>") : Html.Raw(""))
                                </div>
                            </div>

                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>photo</th>
                                        <th>Coupon Name</th>
                                        <th>Coupon Code</th>
                                        <th>Discount (%)</th>
                                        <th>Max Discount</th>
                                        <th>Min Fare</th>
                                        <th>Expiry Date</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>


                                    @if (ViewBag.CouponList != null)
                                    {
                                        foreach (var item in ViewBag.CouponList as List<DBLinker.Lib.RLT_DISCOUNT_COUPON>)
                                        {
                                            <tr class="odd gradeX">
                                                <td>  
                                                    @Html.Raw(item.DiscountImage != null ? "<img src='../Docs/DiscountCouponManager/" + item.DiscountImage + "' height='60' width='60' nerror='this.src=" + "../Docs/No_image_available.png" + "' />" : "<img src='../Docs/No_image_available.png'  height='60' width='60' />")
                                                </td>
                                                <td>@item.Coupon_Name</td>
                                                <td>@item.Discount_Coupon</td>
                                                <td>@item.Discount %</td>
                                                <td>₹ @item.Max_Discount</td>
                                                <td>₹ @item.Fare_When_Applied</td>
                                                <td>
                                                    @(item.Coupon_Last_Date != null ? item.Coupon_Last_Date?.ToString("dd MMMM yyyy") : "n/a")
                                                </td>
                                                <td>
                                                    @Html.Raw(item.Is_Active == null
                                                 ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                                 ? "<span class='label label-sm label-success'> Active </span>"
                                                 : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td align="center">
                                                    @(PermittedAction.Is_Edit ? Html.Raw("<a class='btn btn-tbl-edit btn-xs "+(item.Is_Active == null ? "btn-warning" : item.Is_Active == true ? "btn-success" : "btn-danger") +"' onclick='ActivateDeactivate(" + item.PKID + ")' title='"+ (item.Is_Active != false ? "Disable" : "Enable") + "'><i class='fa fa-"+(item.Is_Active == true ? "check" : "ban") +"'></i></a>") : Html.Raw(""))
                                                    @(PermittedAction.Is_Edit ? Html.Raw("<a class='btn btn-tbl-delete btn-xs btn-elegant' onclick='Edit(" + item.PKID + ")' title='Edit'><i class='fa fa-pencil'></i></a>") : Html.Raw(""))
                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Scripts/CrudFile/DiscountCouponCrudJs.js"></script>

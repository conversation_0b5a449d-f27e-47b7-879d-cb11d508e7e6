﻿

$(document).ready(function () {

    $("#UserManager").parent().parent().find("a").next().slideToggle(500);
    $("#UserManager").parent().parent().toggleClass('toggled');
    $("#UserManager").find("a").addClass("selectedMenu");
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();

    $('#UserName').bind('keyup', function (e) {
        $('#UserName').val($('#UserName').val().replace(/[^a-zA-Z0-9_]/g, ""));
    });

    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $('.selectDropdown').select2();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

    });


    $("#btnSave").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormUserManager")[0]);
            $.ajax({
                url: 'AddUserManager',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response == true) {
                        toastr.success("User Added Sucessfully!", "User Added");
                        window.setTimeout(function () { location.reload() }, 1000)
                    }
                    else if (response == false) {
                        toastr.error("Sorry User not added sucessfully, please try it again !", "Error");
                    }
                    else {
                        toastr.error(response, "Error");
                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormUserManager")[0]);
            $.ajax({
                url: 'EditUserManager',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response != false) {
                        if (response > 0) {
                            toastr.success("( " + response + " ) Changes successfully updated in user profile !", "User Update");
                            window.setTimeout(function () { location.reload() }, 1000);
                        }
                        else {
                            toastr.error(response + "!", "User Update", "User");
                        }
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error"); 
                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#VendorId").change(function () {
        BindCarOwner(0);
    });


    $("#RoleId").change(function () {
        RoleDiv();
    });

    $("#UserPhoto").change(function () {
        var input = document.getElementById("UserPhoto");
        if (PhotoChecker(input)) {
            $("#img-viwer").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
            FileUploader();
        }
    });

});

function FileUploader() {
    var getImg = $("#img-viwer").attr("src")
    if (getImg == "#") {
        $("#img-viwer").hide();
        $("p.file-uploader-text").show();
    } else {
        $("#img-viwer").show();
        $("p.file-uploader-text").hide();
    }
}
function PhotoChecker(input) {
    if (input.files.length > 0) {
        for (var i = 0; i < input.files.length; i++) {
            var file = input.files[i];
            var fileType = file["type"];
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            if ($.inArray(fileType, validImageTypes) < 0) {
                toastr.clear();
                toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
                return false;
            } else return true;
        }
    } else return "no-file";
};



function Edit(Id) {
    $.ajax({
        url: 'EditUserManager',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                resetForm();
                // fill data  
                $("#PKID").val(data.PKID);
                $("#UserName").val(data.UserName);
                $("#UserPWD").val(data.UserPWD);
                $("#UserFirstName").val(data.UserFirstName);
                $("#UserMiddleName").val(data.UserMiddleName);
                $("#UserLastName").val(data.UserLastName);
                $("#UserEmailID").val(data.UserEmailID);
                $("#UserMobileNo").val(data.UserMobileNo);
                if (data.IsActive != null && data.IsActive == true) $("#IsActiveTrue").prop("checked", true);
                else $("#IsActiveFalse").prop("checked", true);
                if (data.UserPhoto != null && data.UserPhoto != "") {
                    $("#img-viwer").attr('src', '../Docs/UserPhoto/' + data.UserPhoto)
                    $("#img-viwer").show();
                    $("p.file-uploader-text").hide();
                }
                else {
                    $("#img-viwer").hide();
                    $("p.file-uploader-text").show();
                }
                // set default values  
                $("#btnSave").slideUp();
                $("#btnAdd").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('.selectDropdown').select2();
                $("#RoleId").val(data.RoleId);
                $("#RoleId").trigger("change");
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}
function ActivateDeactivate(Id) {
    $.ajax({
        url: 'ActiveDeactiveUserManager',
        type: "POST",
        data: { id: Id },
        dataType: "json",
        success: function (response) {
            if (response == true) {
                toastr.success("User Informations Successfully Updated!", "User Status Update");
                window.setTimeout(function () { location.reload() }, 1000)
            }
            else {
                toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

            }
        },
        failure: function (response) {

        },
        error: function (response) {
            alert(response.responseText);
        }
    });
}

function resetForm() {
    $("#RoleId").val('');
    $("#UserPhoto").val('');
    $("#UserName").val('');
    $("#UserPWD").val('');
    $("#UserFirstName").val('');
    $("#UserMiddleName").val('');
    $("#UserLastName").val('');
    $("#UserEmailID").val('');
    $("#UserMobileNo").val('');
    $("#IsActiveTrue").prop("checked", false);
    $("#IsActiveFalse").prop("checked", false);
}

function IsValidate() {
    if ($("#RoleId").val() == "") {
        toastr.clear();
        toastr.error("Please select Role!", "Required!");
        return false;
    }

    if ($("#UserName").val() == "") {
        toastr.clear();
        toastr.error("Please enter User Name!", "Required!");
        return false;
    }

    if ($("#UserPWD").val() == "") {
        toastr.clear();
        toastr.error("Please enterPassword!", "Required!");
        return false;
    }

    if ($("#UserFirstName").val() == "") {
        toastr.clear();
        toastr.error("Please enter user first name !", "Required!");
        return false;
    }

    if ($("#UserEmailID").val() == "") {
        toastr.clear();
        toastr.error("Please enter Email !", "Required!");
        return false;
    }

    if ($("#UserMobileNo").val() == "") {
        toastr.clear();
        toastr.error("Please enter mobile no !", "Required!");
        return false;
    }

    if ($("input[type=radio][name=IsActive]:checked").val() == undefined) {
        toastr.clear();
        toastr.error("Please Set User to active or Inactive!");
        return false;
    }

    return true;
}




$("#UserPhoto").change(function (event) {

    if (PhotoCheck($("#UserPhoto").val()) == false) {
        var tmppath = URL.createObjectURL(event.target.files[0]);
        $("#UserPhoto_View").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
    }
    else {
        $("#UserPhoto").val("");
        $("#UserPhoto_View").fadeIn("fast").attr('src', "/Content/img/no-user-image.gif");
        toastr.clear();
        toastr.error("Please upload only JPG,PNG Image !");
        return false;
    }
});

function BindCarOwner(selectId) {
    $("#CarOwnerId").empty();
    $.ajax({
        type: 'POST',
        url: '../VendorManagement/GetOwner', // Calling json method  
        dataType: 'json',
        data: { id: $("#VendorId").val() },
        success: function (CarOwner) {
            $("#CarOwnerId").append('<option value=>-- Select Car Owner --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#CarOwnerId").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0) {
                $("#CarOwnerId").val(selectId);
            }

        }
    });
    return false;
}

function RoleDiv() {
    if ($("#RoleId").val() == 3) { //Vendor
        $("#DivUserMapping").show();
        $("#DivCarOwner1").show();
        $("#DivCarOwner2").hide();
        $("#DivEmployee").hide();
        $("#DivEmployee2").hide();
    }
    else if ($("#RoleId").val() == 4) { //Car Owner
        $("#DivUserMapping").show();
        $("#DivCarOwner1").show();
        $("#DivCarOwner2").show();
        $("#DivEmployee").hide();
        $("#DivEmployee2").hide();
    }
    else if ($("#RoleId").val() == 6) { //Employee
        $("#DivUserMapping").show();
        $("#DivCarOwner1").hide();
        $("#DivCarOwner2").hide();
        $("#DivEmployee").show();
        $("#DivEmployee2").show();

    }
    else {
        $("#DivUserMapping").hide();
    }
}



﻿@model DBLinker.Lib.Model.M_Car_Details

@{
    ViewBag.Title = "Car Docs";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Add Car Documents</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/CarManager">Car Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Car Documents</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddCarDocs", "VendorManagement", FormMethod.Post, new { enctype = "multipart/form-data" }))
    {
        @Html.AntiForgeryToken()

        @Html.HiddenFor(x => x.PKID)

        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>@Model.Car_Number Documents</header>
                    </div>
                    <div class="card-body ">
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                <thead>
                                    <tr>

                                        <th width="25%">Document  Name</th>
                                        <th width="30%">Document</th>
                                        <th width="8%">View</th>
                                        <th width="10%">Document Expiry Date</th>
                                        <th width="20%">Verified</th>
                                    </tr>
                                </thead>
                                <tbody>


                                    @if (Model.M_Car_DocsList != null)
                                {
                                    if (Model.M_Car_DocsList.Count != 0)
                                    {
                                        for (int i = 0; i < Model.M_Car_DocsList.Count; i++)
                                        {
                                            <tr class="odd gradeX">
                                                <td>
                                                    @Html.HiddenFor(a => Model.M_Car_DocsList[i].Car_Doc_Id)
                                                    @Html.HiddenFor(a => Model.M_Car_DocsList[i].Car_Doc_Path)
                                                    @Html.DisplayFor(a => Model.M_Car_DocsList[i].Car_Doc_Name)
                                                </td>
                                                <td>
                                                    @Html.TextBoxFor(a => Model.M_Car_DocsList[i].Car_Doc_UP, new { @class = "mdl-textfield__input", @type = "file", @style = "height: 60px !important;border: 2px dashed rgba(0, 0, 0, 0.3);*/: ; " })

                                                </td>

                                                <td>
                                                    @if (!string.IsNullOrEmpty(Model.M_Car_DocsList[i].Car_Doc_Path))
                                                {
                                                    <a onClick='return popupSubWindow(this)' href=/Docs/CarDocs/@Model.M_Car_DocsList[i].Car_Doc_Path><img src='~/Content/img/attachment.png' height='25' width='25' title="Click here to view the document."></a>
                                            }
                                                </td>
                                                <td>
                                                    @Html.TextBoxFor(a => Model.M_Car_DocsList[i].Car_Doc_EndDate, new { type = "date" ,@class= "dateFields" })
                                                </td>

                                                <td>
                                                    <div style="float:left;width:40%">
                                                        @Html.RadioButtonFor(a => Model.M_Car_DocsList[i].Is_Verified, true)
                                                        <label for="radio1"><span><span></span></span> Yes</label>
                                                    </div>
                                                    <div style="float:left;width:50%">
                                                        @Html.RadioButtonFor(a => Model.M_Car_DocsList[i].Is_Verified, false)
                                                        <label for="radio2"><span><span></span></span>No</label>
                                                    </div>


                                                </td>
                                            </tr>
                                    }
                                }

                            }

                                </tbody>
                            </table>
                        </div>

                      </div>
                </div>
            </div>
        </div>
        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                @(PermittedAction.Is_Add && PermittedAction.Is_Edit ? Html.Raw("<button type='submit' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/CarManager">Back</a>
            </div>
        </div>
}

    </div>

</div>

<script src="~/Scripts/CrudFile/CarDocsCrud.js"></script>
﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
   public class M_Vendor_Docs
    {

        public int PKID { get; set; }
        
        public int Vendor_Doc_Id { get; set; }
        public string Vendor_Doc_Name { get; set; }
        public string Vendor_Doc_Path { get; set; }
        public HttpPostedFileBase Vendor_Doc_UP { get; set; }

        public int Vendor_Ref_PKID { get; set; }
        public int Last_Modified_By { get; set; }
        public bool Is_Verified { get; set; }

        public string Comments { get; set; }

        public Nullable<System.DateTime> Uplodated_Date { get; set; }
        public Nullable<System.DateTime> Modified_Date { get; set; }
    }
}

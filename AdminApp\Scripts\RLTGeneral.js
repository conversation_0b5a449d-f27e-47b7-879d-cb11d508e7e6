﻿//========Bind Dashboard Data=============//


//$(document).ready(function () { 
//    $('.dateCalander').datepicker({
//        dateFormat: "dd/mm/yy",
//        showStatus: true,
//        showWeeks: true,
//        currentText: 'Now',
//        autoSize: true,
//        gotoCurrent: true,
//        showAnim: 'blind',
//        highlightWeek: true,
//        showOn: 'button',
//        buttonImageOnly: true,
//        changeMonth: true,
//        changeYear: true,
//        yearRange: $("#ShowYear").val(),
//        maxDate: new Date($("#currentDate").val()),
//        buttonImage: '../Content/HSMS/images/cal.gif'
//    })
//    $(".dateCalander").datepicker().datepicker("setDate", new Date());
//});

//========fOR ACCEPT NUMBER ONLY===================//
$(document).ready(function () {



    $(".txtbx").keydown(function (e) {
        // Allow: backspace, delete, tab, escape, enter and .
        if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
            // Allow: Ctrl+A, Command+A
            (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: home, end, left, right, down, up
            (e.keyCode >= 35 && e.keyCode <= 40)) {
            // let it happen, don't do anything
            return;
        }
        // Ensure that it is a number and stop the keypress
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
            e.preventDefault();
        }
    });


    // Catch all events related to changes
    $('.Inttxtbx').on('change keyup', function () {
        // Remove invalid characters
        var sanitized = $(this).val().replace(/[^-0-9]/g, '');
        // Remove non-leading minus signs
        sanitized = sanitized.replace(/(.)-+/g, '$1');
        // Remove the first point if there is more than one
        sanitized = sanitized.replace(/\.(?=.*\.)/g, '');
        // Update value
        $(this).val(sanitized);
    });

    // Catch all events related to changes
    $('.Decimaltxtbx').on('change keyup', function () {
        // Remove invalid characters
        var sanitized = $(this).val().replace(/[^-.0-9]/g, '');
        // Remove non-leading minus signs
        sanitized = sanitized.replace(/(.)-+/g, '$1');
        // Remove the first point if there is more than one
        sanitized = sanitized.replace(/\.(?=.*\.)/g, '');
        // Update value
        $(this).val(sanitized);
    });



});






var isEmail = function (what) {
    $($(what)).removeClass('errorValidField');
    if ($(what).val() != "") {
        var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        if (!regex.test($.trim($(what).val()))) {
            $($(what)).addClass('errorValidField');
            return false;
        }
    }
    return true;
};
var isTextValid = function (what) {
    $($(what)).removeClass('errorValidField');
    if ($(what).val() != "") {
        var regex = /^[a-zA-Z ]*$/;
        if (!regex.test($.trim($(what).val()))) {
            $($(what)).addClass('errorValidField');
            return false;
        }
    }
    return true;
};
var isTextSpecialValid = function (what) {
    $($(what)).removeClass('errorValidField');
    if ($(what).val() != "") {
        var regex = /^[^>#*]+$/;
        if (!regex.test($.trim($(what).val()))) {
            $($(what)).addClass('errorValidField');
            return false;
        }
    }
    return true;
};
var isTextExist = function (what, str) {
    if ($.trim($(what).val()) == "") {
        $(what).addClass("errorField");
        var val = $("#idAlert").html();
        $("#idAlert").html(val + "<br/>" + "Please enter " + str);
        return false;
    }
    return true;
};


var isOptionSelected = function (what, str) {
    if ($.trim($(what).val()) == "") {
        $(what).addClass("errorField");
        var val = $("#idAlert").html();
        $("#idAlert").html(val + "<br/> Please Select " + str);
        return false;
    }
    return true;
};


function PhotoCheck(photoValue) {
    if ((photoValue.match(/\.(jpg?)$/i)) || (photoValue.match(/\.(jpeg?)$/i)) || (photoValue.match(/\.(png?)$/i)) || (photoValue.match(/\.(gif?)$/i))) {
        return false;
    }
    else {
        return true;
    }
}

//function PhotoChecker() {
//    var input = $("#Driver_Photo");

//    var file = input[0].files[0];
//    var fileType = file["type"];
//    var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
//    if ($.inArray(fileType, validImageTypes) < 0) {
//        toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
//        return false;
//    }
//    else {
//        return true;
//    }
//};



var openModal = function (what, header, path) {
    Custombox.open({
        target: what,
        effect: 'fadein',
        overlay: true,
        overlayColor: '#000',
        overlayOpacity: 0.8,
        speed: 200,
        cache: false
    });

    $(what).find("div.headerContent").text(header);
    $(what).find(".modalContent").load(path);
};

var openModalDiv = function (what, header) {
    Custombox.open({
        target: what,
        effect: 'fadein',
        overlay: true,
        overlayColor: '#000',
        overlayOpacity: 0.8,
        speed: 200,
        cache: false
    });

    $(what).find("div.headerContent").text(header);
};

var closePopup = function (what) {
    Custombox.close();
};

var showLoader = function () {
    $(".overlay").show();
};

var hideLoader = function () {
    $(".overlay").hide();
};


function imageFormat(cellvalue, options, rowObject) {
    if (!(cellvalue == null || cellvalue == "")) {
        return "<center><a href='" + cellvalue + "' onClick='return popupSubWindow(this)'><img src='../Content/HSMS/Images/attachment.png' height='16' width='16'></a></center>";
    } else {
        return "";
    }
}

function getLabelValue(e) {
    return e.innerHTML;
}


//========Open Docs in Popup===================//

function popupSubWindow(mylink) {
    if (!window.focus) return true; var href;
    if (typeof (mylink) == 'string') href = mylink;
    else href = mylink.href; window.open(href, 'RLT', 'left=300,top=80,width=850,height=500,toolbar=1,scrollbars=yes'); return false;
}

$(document).ready(function () {
    $(".searchableLinks").hide();
    $('#searchBarField').on('keyup  paste', function () {
        var input = $(this);
        $(".searchableLinks").empty();
        var linkCount = 0;
        var links = "";
        var parentName = "";
        var getUrls = [];
        var tree = [];
        $('#remove-scroll').find('a').each(function () {
            getUrls.push($(this));
            tree.url = $(this).attr('herf');
        });
        if (input.val().replace(/\s/g, '').length == 0) {
            $(this).val('');
        }
        else {
            $(this).val($(this).val().replace(/\s+/g, " "));
            input.val($(this).val().replace(/\s+/g, " "));
        }
        var searchTerm = input.val();
        if (searchTerm.length >= 1) {
            var result = getUrls.filter(item => item.text().toLowerCase().indexOf(searchTerm) > -1);
            if (result.length > 0) {
                for (var i = 0; i < result.length; i++) {
                    if (result[i].attr("href") != "#") {
                        var icon = result[i].children('i').attr('class');
                        if (icon === undefined || result[i].text().replace(/\s/g, '') != "input") {
                            var parent = result[i].parent('li').parent('ul').parent('li');
                            icon = parent.find('i').attr('class');
                            if (parent.find('a[href="#"]').text() !== undefined) {
                                if (parent.find('a[href="#"]').text().length > 0)
                                    parentName = "<span class='small-text'> (" + parent.find('a[href="#"]').text() + ")</span>";
                                else {
                                    icon = result[i].children('i').attr('class');
                                    parentName = "<span class='small-text'> (" + result[i].text().trim() + ")</span>"
                                }
                            }
                            links += "<a href='" + result[i].attr("href") + "' class='nav-link'>"
                                + "<i class='" + icon + "'></i> &nbsp;"
                                + "<span class='title'>" + result[i].text().trim() + "</span>"
                                + parentName
                                + "</a>"
                            linkCount = linkCount + 1;
                        }
                    }
                }
            }
        }
        if (linkCount == 0 && searchTerm != "") {
            links += "<p class='search-result-found'> 0 Results Found.</p>"
        }
        if (links != "") {
            $(".searchableLinks").empty();
            if (linkCount != 0) {
                $(".searchableLinks").append("<p class='search-result-found'>" + linkCount + " Results Found.</p>")
            }
            $(".searchableLinks").append(links);
            $(".searchableLinks").show();
        }
        else {
            $(".searchableLinks").hide();
        }
    });

});


$(document).ajaxSuccess(function (event, request, settings) {
    if (request.responseJSON !== undefined) {
        if (request.responseJSON.Unauthorized == 401) {
            window.location = '../error/Not-Authorized';
        }
    }
});

 
    
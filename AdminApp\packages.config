﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AjaxControlToolkit4.1.50731" version="4.1.50731.0" targetFramework="net472" />
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="bootstrap" version="5.3.3" targetFramework="net461" />
  <package id="EntityFramework" version="6.5.1" targetFramework="net461" />
  <package id="jQuery" version="3.5.1" targetFramework="net461" />
  <package id="jQuery.Validation" version="1.19.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="4.0.0" targetFramework="net461" />
  <package id="Microsoft.ReportingServices.ReportViewerControl.WebForms" version="150.1652.0" targetFramework="net461" />
  <package id="Microsoft.SqlServer.Types" version="14.0.314.76" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net461" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net461" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net461" />
  <package id="Razorpay" version="3.1.4" targetFramework="net461" requireReinstallation="true" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net461" />
  <package id="Unity" version="5.11.10" targetFramework="net461" requireReinstallation="true" />
  <package id="Unity.Mvc5" version="1.4.0" targetFramework="net461" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>
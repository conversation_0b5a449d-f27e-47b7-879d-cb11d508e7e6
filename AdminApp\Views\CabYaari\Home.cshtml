﻿@model  IEnumerable<DBLinker.Lib.RLTMostFavoriteRoute>
@{
    ViewBag.Title = "Home";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />
<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title"><PERSON><PERSON></div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="index.html">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Cab Yaari</li>
                </ol>
            </div>
        </div>
        <!-- start widget -->

        <div class="row" id="dvAddUpdate">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Add Most Favourite Routes</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                            data-mdl-for="panel-button">
                            <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                            <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                            <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                        </ul>
                    </div>
                    @using (Html.BeginForm("Home", "CabYaari", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormCabYariHome" }))
                    {
                        @Html.AntiForgeryToken()
                        @Html.Hidden("ID")
                        <input type="hidden" name="fairCalculateBtnClickedOnDdlChanged" value="false" id="fairCalculateBtnClickedOnDdlChanged" />
                        <div class="card-body">
                            <div class="row">

                                <div class="col-lg-9 p-t-20 row">
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CityList != null)
                                            {
                                                @Html.DropDownList("PickUpCityID", new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select From City --", new { @class = "mdl-textfield__input selectDropdown", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>City From</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CityList != null)
                                            { 
                                                @Html.DropDownList("DropOffCityID", new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select To City --", new { @class = "mdl-textfield__input selectDropdown", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>City To</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CarCategory != null)
                                            { 
                                                @Html.DropDownList("TripType", new SelectList(ViewBag.TripTypeList, "PKID", "Trip_Type"), "-- Select Trip Type --", new { @class = "mdl-textfield__input selectDropdown", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>Trip Type</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CarCategory != null)
                                            { 
                                                @Html.DropDownList("CarCategoryID", new SelectList(ViewBag.CarCategory, "PKID", "Car_Category"), "-- Select Car Category --", new { @class = "mdl-textfield__input selectDropdown", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>CAR Category</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-12 text-center p-t-20">
                                        <button type="button" class="col-lg-6 mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect btn-pink" id="btnCalculateFair">Calculate Fair</button>

                                    </div>
                                </div>

                                <div class="col-lg-3 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <label for="City_Image" class="file-uploader-lable">
                                            <div id="photo-div" class="file-uploader">
                                                <img src="#" class="file-uploder-img" id="city-img-viwer" />
                                                <p class="file-uploader-text"><strong>Click to<br />Choose Image</strong></p>
                                                <input type="file" id="City_Image" name="City_Image" accept="image/x-png,image/gif,image/jpeg" style="display:none">
                                            </div>
                                        </label>
                                        <label class="mdl-textfield__label required"><span>City Image</span></label>
                                    </div>


                                    <div class="row">
                                        <span style=" color: #acaaaa;"> Set Most Favourite Routs to active</span>
                                        <div>
                                            <input id="IsActiveTrue" name="IsActive" type="radio" value="true">
                                            <label for="IsActive"><span><span></span></span> Yes</label>
                                        </div>
                                        <div>
                                            <input id="IsActiveFalse" name="IsActive" type="radio" value="false">
                                            <label for="IsActive"><span><span></span></span>No</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row p-t-20 CalculatedFairFields">
                                <div class="col-lg-3 p-t-20 ">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <input style="font-weight:bold;" class="mdl-textfield__input" id="TravelDuration" name="TravelDuration" readonly="readonly" type="text" value="" data-msgName="Travel Duration Field">
                                        <label class="mdl-textfield__label"> Duration</label>
                                    </div>
                                </div>
                                <div class="col-lg-3 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <input style="font-weight:bold;" class="mdl-textfield__input" id="Distance" name="Distance" readonly="readonly" type="text" value="" data-msgName="Travel Distance Field">
                                        <label class="mdl-textfield__label">Distance</label>
                                    </div>
                                </div>
                                <div class="col-lg-3 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <input style="font-weight:bold;" class="mdl-textfield__input" id="Fare" name="Fare" readonly="readonly" type="text" value="" data-msgName="Travel Fare Field">
                                        <label class="mdl-textfield__label">Fare</label>
                                    </div>
                                </div>
                                <div class="col-lg-3 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <input style="font-weight:bold;" class="mdl-textfield__input" id="GSTFare" name="GSTFare" readonly="readonly" type="text" value="" data-msgName="Travel GST Field">
                                        <label class="mdl-textfield__label">Gst Amount ( 5% )</label>
                                    </div>
                                </div>
                            </div>



                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                                <button type='reset' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default' id='btnReset'>cancel</button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>


        @*//div list pannel for vendor`s *@
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Most Favourite Routes</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                @(PermittedAction.Is_Add ? Html.Raw("<div class='btn-group'><a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a></div>") : Html.Raw(""))
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <td>Photo</td>
                                        <th>City From</th>
                                        <th>City To</th>
                                        <th>Distance<br />Duration</th>
                                        <th>Car Category<br />Trip Type</th>
                                        <th>Fare<br />Gst Fare</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null)
                                    {
                                        foreach (var item in Model)
                                        {
                                            <tr class="odd gradeX">
                                                <td>
                                                    @Html.Raw(item.CityImage != null ? "<img src='../Docs/MostFavouriteCityImg/" + item.CityImage + "' height='60' width='60' />" : "<img src='~/ Content/img/no-user-image.gif'  height='60' width='60' />")
                                                </td>
                                                <td class="getCity" data-id="@item.PickUpCityID"></td>
                                                <td class="getCity" data-id="@item.DropOffCityID"></td>
                                                <td>@item.Distance KM<br />@item.TravelDuration</td>
                                                <td>
                                                    <span class="getCarCategory" data-id="@item.CarCategoryID"></span><br />
                                                    <span class="getTripType" data-id="@item.TripType"></span>
                                                </td>
                                                <td>
                                                    <strong>&#x20B9; </strong> @string.Format("{0:n2}", item.Fare)<br />
                                                    <strong>&#x20B9; </strong> @string.Format("{0:n2}", item.GSTFare)
                                                </td>
                                                <td>
                                                    @Html.Raw(item.IsActive == null
                                                ? "<span class='label label-sm label-warning'> Not Set </span>" : item.IsActive == true
                                                ? "<span class='label label-sm label-success'> Active </span>"
                                                : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate("+item.ID+")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>

                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit("+item.ID+ ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw(""))
                                                            </li>
                                                            @*<li>
                                                                    <a href="../VendorManagement/VendorManagerView?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.ID), "Vendor")" title="View Most favorite routes Info">
                                                                        <i class="material-icons">remove_red_eye</i>
                                                                        View
                                                                    </a>
                                                                </li>*@
                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @*//End div list pannel for vendor`s *@
    </div>
</div>
<script src="~/Scripts/CrudFile/CabYariHome.js"></script>
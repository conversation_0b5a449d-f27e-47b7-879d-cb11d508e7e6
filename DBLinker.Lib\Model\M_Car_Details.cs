﻿using System;
using System.Collections.Generic;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Car_Details
    {
        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        public Nullable<int> Vendor_PKID { get; set; }
        public Nullable<int> Car_Owner_PKID { get; set; }
        [Remote("Check_Car_Number", "Common", ErrorMessage = "Car Number already exists!")]
        public string Car_Number { get; set; }
        public string Car_Purchase_Year { get; set; }
        public Nullable<int> Car_Company_PKID { get; set; }
        public Nullable<int> Car_Model_PKID { get; set; }
        public string Car_Registered_Document { get; set; }
        public string Car_Registered_Document_EditFile { get; set; }
        public Nullable<int> Car_Registered_City_PKID { get; set; }
        public string Car_Fuel_Type_Status { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Update_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }

        public string Car_Owner_Name { get; set; }
        public string Vendor_Name { get; set; }
        
        public string Company_Name { get; set; }
        public string Car_Model_Name { get; set; }
        public string City_Name { get; set; }
        public string Car_Manufacturing_Year { get; set; }
        public List<M_Car_Docs> M_Car_DocsList { get; set; }

    }
}

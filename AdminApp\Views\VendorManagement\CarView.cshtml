﻿@model DBLinker.Lib.Model.M_Car_Details
@using AdminApp.Mapper;

@{
    ViewBag.Title = "Car Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $("#CarManager").parent().parent().find("a").next().slideToggle(500);
        $("#CarManager").parent().parent().toggleClass('toggled');
        $("#CarManager").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">View Car Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/CarManager">Car Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Car Details</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Car Details</header>

                    </div>
                    <div class="card-body row">


                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Vendor Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Car_Owner_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Car Owner</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Number, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Car Number</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Purchase_Year, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Car Purchase Year</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Purchase_Year, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>Car Comapny</span> </label>
                            </div>
                        </div>


                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Model_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> <span>Car Model</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.City_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Car Registered City</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Manufacturing_Year, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Car Manufacturing Year</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "CNG", new { @id = "Car_Fuel_Type_Status1" })
                                        <label for="radio1"><span><span></span></span> CNG</label>
                                    </div>
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "Petrol", new { @id = "Car_Fuel_Type_Status2" })
                                        <label for="radio2"><span><span></span></span>Petrol</label>
                                    </div>
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "Diesel", new { @id = "Car_Fuel_Type_Status3" })
                                        <label for="radio2"><span><span></span></span>Diesel</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                    <label class="mdl-textfield__label required"><span>Car Fule Type</span></label>
                                </div>
                            </div>
                        </div>




                    </div>


                    <div class="row">
                        <div class="col-md-12">
                            <div class="card card-box">
                                <div class="card-head">
                                    <header> Documents</header>
                                </div>
                                <div class="card-body ">
                                    <div class="table-scrollable">
                                        <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                            <thead>
                                                <tr>

                                                    <th width="40%">Document  Name</th>
                                                    <th width="20%">View</th>
                                                    <th width="20%">End Date</th>
                                                    <th width="20%">Verified</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @if (Model.M_Car_DocsList != null)
                                            {
                                                if (Model.M_Car_DocsList.Count != 0)
                                                {
                                                    for (int i = 0; i < Model.M_Car_DocsList.Count; i++)
                                                    {
                                                        <tr class="odd gradeX">
                                                            <td>
                                                                @Html.DisplayFor(a => Model.M_Car_DocsList[i].Car_Doc_Name)
                                                            </td>

                                                            <td>
                                                                @if (!string.IsNullOrEmpty(Model.M_Car_DocsList[i].Car_Doc_Path))
                                                            {
                                                                <a onClick='return popupSubWindow(this)' href=/Docs/CarDocs/@Model.M_Car_DocsList[i].Car_Doc_Path><img src='~/Content/img/attachment.png' height='25' width='25' title="Click here to view the document."></a>
                                                        }
                                                            </td>
                                                            <td>
                                                                @Html.DisplayFor(a => Model.M_Car_DocsList[i].Car_Doc_EndDate)
                                                            </td>
                                                            <td>
                                                                <div class="col-md-10">
                                                                    @if (Model.M_Car_DocsList[i].Is_Verified == true)
                                                                {
                                                                    <label> Yes</label>
                                                            }
                                                            else
                                                            {
                                                                <label> No</label>
                                                        }
                                                                </div>

                                                            </td>
                                                        </tr>
                                                }
                                                /**/
                                            }

                                        }

                                            </tbody>
                                        </table>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/CarManager">Back</a>
            </div>
        </div>

    </div>
</div>


﻿@model DBLinker.Lib.Model.M_Template

@{
    ViewBag.Title = "Template Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="//cdn.tinymce.com/4/tinymce.min.js"></script>
<script>

    tinymce.init({
        selector: "#Message_Body",
        height: 300,
        plugins: [
            "advlist autolink autosave link image lists charmap print preview hr anchor pagebreak spellchecker",
            "searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking",
            "table contextmenu directionality emoticons template textcolor paste textcolor colorpicker textpattern"
        ],

        toolbar1: "bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | styleselect formatselect fontselect fontsizeselect",
        toolbar2: "cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | undo redo | link unlink anchor image media code | insertdatetime preview | forecolor backcolor",
        toolbar3: "table | hr removeformat | subscript superscript | charmap emoticons | print fullscreen | ltr rtl | spellchecker | visualchars visualblocks nonbreaking template pagebreak restoredraft",

        menubar: false,
        toolbar_items_size: 'small',

        templates: [{
            title: 'Test template 1',
            content: 'Test 1'
        }, {
            title: 'Test template 2',
            content: 'Test 2'
        }],
        content_css: [
            '/Content/style.css'
        ]
    });

</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Template Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Template Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add New Template</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddTemplateManager", "MailSMS", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormTemplateManager" }))
    {


        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.IsActive)
        @Html.HiddenFor(x => x.PKID)
        <div class="row" id="dvAddUpdate">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Add New Template</header>

                    </div>
                    <div class="card-body row">
                        <div class="col-lg-12 p-t-20">
                            <label style="color:red;font-weight:bold;"> Note:  </label>&nbsp;&nbsp;
                            <label style="color:black;"> Please use these keywords in subject and Message Body </label> &nbsp;&nbsp;
                            <label style="color:black;font-weight:bold;"> 1. $Vendor$ &nbsp; 2. $CarOwner$ &nbsp; 3. $Driver$ &nbsp; 4. $Employee$ </label>


                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Mail_SMS_Type, "Mail", new { @id = "Mail_SMS_Type1" })
                                        <label for="radio1"><span><span></span></span> Mail</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Mail_SMS_Type, "SMS", new { @id = "Mail_SMS_Type2" })
                                        <label for="radio2"><span><span></span></span>SMS</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label required"><span>Template For</span> </label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Template_Name, new { @class = "mdl-textfield__input", @maxlength = "100" })
                                <label class="mdl-textfield__label required"><span>Template Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Subject, new { @class = "mdl-textfield__input", @maxlength = "500" })
                                <label class="mdl-textfield__label required"><span>Message Subject</span></label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.Message_Body, new { @class = "mdl-textfield__input" })

                                <label class="mdl-textfield__label required"><span>Message Body</span> </label>
                            </div>
                        </div>

                        <div class="col-lg-12 p-t-20 text-center">


                            <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                            <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
}
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Template</header>

                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">

                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th width="15%">Template For</th>
                                        <th width="25%">Template Name</th>
                                        <th width="35%">Subject</th>
                                        <th width="10%">Status</th>
                                        <th width="10%" style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.TemplateList != null)
                                {
                                    foreach (var item in ViewBag.TemplateList)
                                    {

                                        <tr class="odd gradeX">

                                            <input type="hidden" id="<EMAIL>" value="@item.Mail_SMS_Type" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Template_Name" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Subject" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Message_Body" />
                                            <input type="hidden" id="<EMAIL>" value="@item.IsActive" />


                                            <td>@item.Mail_SMS_Type</td>
                                            <td>@item.Template_Name</td>
                                            <td>@item.Subject</td>
                                            <td>
                                                @{
                                                if (item.IsActive != null && item.IsActive != false)
                                                {
                                                    <span class="label label-sm label-success"> Active </span>
                                            }
                                            else
                                            {
                                                <span class="label label-sm label-danger"> InActive </span>
                                        }
                                                }
                                            </td>

                                            <td align="center">
                                                <a class="btn btn-tbl-edit btn-xs" onclick="ActivateDeactivate(@item.PKID)" title="Enable">
                                                    <i class="fa fa-check"></i>
                                                </a>
                                                <a class="btn btn-tbl-delete btn-xs" onclick="Edit(@item.PKID)" title="Edit">
                                                    <i class="fa fa-pencil"></i>
                                                </a>

                                            </td>
                                        </tr>
                                }

                            }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="~/Scripts/CrudFile/TemplateCrud.js"></script>





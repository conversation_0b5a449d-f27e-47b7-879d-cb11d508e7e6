﻿@model DBLinker.Lib.Model.M_Document_Name

@{
    ViewBag.Title = "Document Name";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Document Name Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Document Name Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Document Name</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Document Name</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.DropDownListFor(x => x.Doc_For, new List<SelectListItem>{
new SelectListItem{ Text="Vendor", Value = "Vendor" },
new SelectListItem{ Text="Car Driver", Value = "Car Driver" },
new SelectListItem{ Text="Car", Value = "Car" },
}, "-- Select Document For --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    <label class="mdl-textfield__label">Document For</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Doc_Name, new { @class = "mdl-textfield__input" })
                                    @Html.ValidationMessageFor(x => x.Doc_Name, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label"> Document Name</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Is_Doc_Req, true, new { @id = "Is_Doc_Req1" })
                                            <label for="radio1"><span><span></span></span> Yes</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Is_Doc_Req, false, new { @id = "Is_Doc_Req2" })
                                            <label for="radio2"><span><span></span></span>No</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Document Required? </span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Is_Doc_Expiry_Date, true, new { @id = "Is_Doc_Expiry_Date1" })
                                            <label for="radio1"><span><span></span></span> Yes</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Is_Doc_Expiry_Date, false, new { @id = "Is_Doc_Expiry_Date2" })
                                            <label for="radio2"><span><span></span></span>No</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Document Have Expiry Date?</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20 text-center">


                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Document Name</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>Document  For</th>
                                        <th>Document  Name</th>
                                        <th>Is Required ?</th>
                                        <th>Is Expiry Date ?</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.vendorNameList != null)
                                    {
                                        foreach (var item in ViewBag.vendorNameList)
                                        {

                                            <tr class="odd gradeX">
                                                <input type="hidden" id="<EMAIL>" value="@item.Doc_For" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Doc_Name" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Doc_Req" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Doc_Expiry_Date" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />

                                                <td>@item.Doc_For</td>
                                                <td>@item.Doc_Name</td>
                                                <td>
                                                    @{
                                                        if (item.Is_Doc_Req != null && item.Is_Doc_Req != false)
                                                        {
                                                            <span> Yes </span>
                                                        }
                                                        else
                                                        {
                                                            <span> No </span>
                                                        }
                                                    }

                                                </td>
                                                <td>
                                                    @{
                                                        if (item.Is_Doc_Expiry_Date != null && item.Is_Doc_Expiry_Date != false)
                                                        {
                                                            <span> Yes </span>
                                                        }
                                                        else
                                                        {
                                                            <span> No </span>
                                                        }
                                                    }

                                                </td>
                                                <td>
                                                    @{
                                                        if (item.Is_Active != null && item.Is_Active != false)
                                                        {
                                                            <span class="label label-sm label-success"> Active </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="label label-sm label-danger"> In-Active </span>
                                                        }
                                                    }
                                                </td>



                                                <td align="center">
                                                    <a class="btn btn-tbl-edit btn-xs" onclick="ActivateDeactivate(@item.PKID)" title="Enable">
                                                        <i class="fa fa-check"></i>
                                                    </a>
                                                    <a class="btn btn-tbl-delete btn-xs" onclick="Edit(@item.PKID)" title="Edit">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>

                                                    @*<a class="btn btn-tbl-delete btn-xs" onclick="Delete(@item.PKID)" title="Delete">
                                                            <i class="fa fa-trash-o "></i>
                                                        </a>*@


                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Scripts/CrudFile/DocNameCrud.js"></script>


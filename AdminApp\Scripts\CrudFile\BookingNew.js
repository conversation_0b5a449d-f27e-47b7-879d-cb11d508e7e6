﻿$(document).ready(function () {

    $("#NewBooking").parent().parent().find("a").next().slideToggle(500);
    $("#NewBooking").parent().parent().toggleClass('toggled');
    $("#NewBooking").find("a").addClass("selectedMenu");


    $("#btnSearchCab").click(function () {

        if ($("#City_From_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select City From!");
            return false;
        }
        else if ($("#City_To_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select City To!");
            return false;
        }
        else if ($("#Trip_Type_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Trip Type!");
            return false;
        }
        else if ($("#Car_Category_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Car Category!");
            return false;
        }
        else if ($("#PickUp_Address").val() == "" || $("#PickUpAddressLatitude").val() == "") {
            toastr.clear();
            toastr.error("Please select pickup address!");
            return false;
        }
        else if ($("#DropOff_Address").val() == "" || $("#PickUpAddressLongitude").val() == "") {
            toastr.clear();
            toastr.error("Please select drop off address!");
            return false;
        }
        else {
            showLoader();
            calculateFare();
            setTimeout(function () {
                hideLoader();
            }, 1000);
        }


    });

    $("#btnApplyDiscount").click(function () {
        if ($("#Coupon_Code").val() == "") {
            toastr.clear();
            toastr.error("Please enter Coupon code!");
            return false;
        }
        else if ($("#Fare").val() == "") {
            toastr.clear();
            toastr.error("Please calculate fare first!");
            return false;
        }
        else if ($("#Coupon_Code_Applied").html() != "") {
            toastr.clear();
            toastr.error("coupon already applied!");
            return false;
        }
        else {
            showLoader();
            calculateDiscount();
            setTimeout(function () {
                hideLoader();
            }, 1000);
        }


    });

    $("#btnRemoveDiscount").click(function () {
        $("#Coupon_Code_Applied").html("");
        $("#btnRemoveDiscount").hide();
        $("#btnApplyDiscount").show();

        $('#Basic_Fare').val($('#hd_OldBasic_Fare').val());
        $('#GST_Fare').val($('#hd_OldGST_Fare').val());
        $('#Fare').val($('#hd_OldFare').val());
        
    });

    $("#Mode_Of_Payment_Id").change(function () {
        var selectedText = $("#Mode_Of_Payment_Id option:selected").text().toLowerCase();

        // Show online payment button for online payment methods
        if (selectedText.includes("online") || selectedText.includes("credit") ||
            selectedText.includes("debit") || selectedText.includes("upi") ||
            selectedText.includes("net banking") || selectedText.includes("wallet")) {
            $("#btnSubmitWithPayment").show();
            $("#paymentMethodInfo").show();
            $("#partialPaymentSection").show();
        } else {
            $("#btnSubmitWithPayment").hide();
            $("#paymentMethodInfo").hide();
            $("#partialPaymentSection").hide();
            // Reset partial payment when hiding
            $("#chkPartialPayment").prop('checked', false);
            $("#partialAmountSection").hide();
            $("#PaymentOption").val(2); // Set to full payment (FullPay=2) when resetting
        }
    });

    // Handle partial payment checkbox
    $("#chkPartialPayment").change(function () {
        if ($(this).is(':checked')) {
            $("#partialAmountSection").show();
            $("#PaymentOption").val(1); // Set to partial payment (PartialPay=1)

            // Auto-populate with suggested amount from API (25% of basic fare OR minimum ₹500)
            var suggestedAmount = $("#txtPartialAmount").attr('data-suggested');
            if (suggestedAmount && suggestedAmount > 0) {
                $("#txtPartialAmount").val(suggestedAmount);
                $("#PartialPaymentAmount").val(suggestedAmount);
            }
        } else {
            $("#partialAmountSection").hide();
            $("#PaymentOption").val(2); // Set to full payment (FullPay=2)
            $("#txtPartialAmount").val('');
            $("#PartialPaymentAmount").val(0);
        }
    });

    // Handle partial amount input
    $("#txtPartialAmount").on('input', function () {
        var partialAmount = parseFloat($(this).val()) || 0;
        var totalFare = parseFloat($("#Fare").val()) || 0;

        if (partialAmount > totalFare) {
            toastr.warning("Partial amount cannot be greater than total fare!");
            $(this).val(totalFare);
            partialAmount = totalFare;
        }

        $("#PartialPaymentAmount").val(partialAmount);
    });

    $("#btnSubmitWithPayment").click(function () {
        if ($("#Mode_Of_Payment_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Payment Mode!");
            return false;
        }

        // Validate partial payment if enabled
        if ($("#chkPartialPayment").is(':checked')) {
            var partialAmount = parseFloat($("#txtPartialAmount").val()) || 0;
            var totalFare = parseFloat($("#Fare").val()) || 0;

            if (partialAmount <= 0) {
                toastr.clear();
                toastr.error("Please enter a valid partial payment amount!");
                return false;
            }

            if (partialAmount >= totalFare) {
                toastr.clear();
                toastr.error("Partial amount must be less than total fare!");
                return false;
            }
        }

        // Proceed with booking creation
        showLoader();
        $("#Is_OnlinePayment").val(1);

        $.ajax({
            url: 'NewBooking',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response.success) {
                    var message = $("#chkPartialPayment").is(':checked') ?
                        "Booking created successfully! Partial payment link sent." :
                        "Booking created successfully! Payment link sent.";
                    toastr.success(message);
                    // Redirect to ViewBooking page with the encrypted booking ID (encrypted on server)
                    window.location.href = "/BookingManagement/ViewBooking?id=" + response.bookingId;
                }
                else {
                    toastr.error("Sorry, booking could not be created. Please try again!");
                }
            },
            failure: function (response) {

            }

        });

        setTimeout(function () {
            hideLoader();
        }, 1000);


    });

    // Clear pickup address when "From City" changes
    $('#City_From_Id').change(function() {
        $('#PickUp_Address').val('');
        $('#PickUpAddressLatitude').val('');
    });

    // Clear drop-off address when "To City" changes
    $('#City_To_Id').change(function() {
        $('#DropOff_Address').val('');
        $('#PickUpAddressLongitude').val('');
    });


    $('#PickUp_Address').autocomplete({
        source: function (request, response) {
            $('#PickUpAddressLatitude').val('');

            // Check if a city is selected
            var cityFromId = $("#City_From_Id").val();
            if (!cityFromId || cityFromId === '') {
                response([]);
                return;
            }

            var autocompleteUrl = '../GoogleMap/AutoSuggestion?placeName=' + $("#PickUp_Address").val() + '&CityId=' + cityFromId;
            $.ajax({
                url: autocompleteUrl,
                type: 'GET',
                cache: false,
                dataType: 'json',
                success: function (json) {
                    response($.map(json, function (data, id) {
                        return {
                            label:data.placeAddress,
                            value: data.latlong
                        };
                    }));
                },
                error: function (xmlHttpRequest, textStatus, errorThrown) {
                    console.log('some error occured', textStatus, errorThrown);
                }
            });
        },
        minLength: 1,
        select: function (event, ui) {
            $('#PickUp_Address').val(ui.item.label);
            $('#PickUpAddressLatitude').val(ui.item.value);
            return false;
        }
    });
   


    $('#DropOff_Address').autocomplete({

        source: function (request, response) {
            $('#PickUpAddressLongitude').val('');

            // Check if a city is selected
            var cityToId = $("#City_To_Id").val();
            if (!cityToId || cityToId === '') {
                response([]);
                return;
            }

            var autocompleteUrl = '../GoogleMap/AutoSuggestion?placeName=' + $("#DropOff_Address").val() + '&CityId=' + cityToId;
            $.ajax({
                url: autocompleteUrl,
                type: 'GET',
                cache: false,
                dataType: 'json',
                success: function (json) {
                    response($.map(json, function (data, id) {

                        return {
                            label:data.placeAddress,
                            value: data.latlong
                        };
                    }));
                },
                error: function (xmlHttpRequest, textStatus, errorThrown) {
                    console.log('some error occured', textStatus, errorThrown);
                }
            });
        },
        minLength: 1,
        select: function (event, ui) {

            $('#DropOff_Address').val(ui.item.label);
            $('#PickUpAddressLongitude').val(ui.item.value);
            return false;
        }
    });



   
});

function calculateFare() {
    $.ajax({
        type: 'POST',
        url: '../GoogleMap/GetDistanceTimeDetailsBasedOnPickUpDropOffLocation?pickUpAddressLatLong=' + $("#PickUpAddressLatitude").val()
            + '&dropOffAddressLatLong=' + $("#PickUpAddressLongitude").val() + '&Trip_Type_Id=' + $("#Trip_Type_Id").val()
            + '&Car_Category_Id=' + $("#Car_Category_Id").val(),
        dataType: 'json',
        success: function (FareChart) {
            if (FareChart != "") {
                $("#Distance").val(FareChart.Distance);
                $("#Basic_Fare").val(FareChart.Basic_Fare);
                $("#GST_Fare").val(FareChart.GST_Fare);

                $("#Duration").val(FareChart.Duration);
                $("#FixRateNote").html(FareChart.FixRateNote);
                $("#Fare").val(Math.round(FareChart.Fare));

                // Store the suggested partial payment amount from API
                if (FareChart.PartialPaymentAmount) {
                    $("#txtPartialAmount").attr('data-suggested', FareChart.PartialPaymentAmount);

                    // If partial payment is already enabled, update the amount
                    if ($("#chkPartialPayment").is(':checked')) {
                        $("#txtPartialAmount").val(FareChart.PartialPaymentAmount);
                        $("#PartialPaymentAmount").val(FareChart.PartialPaymentAmount);
                    }
                }

            }
            else {
                $("#Distance").val("");
                $("#Basic_Fare").val("");
                $("#GST_Fare").val("");
                $("#Fare").val("");
                $("#Duration").val("");

                toastr.clear();
                toastr.error("No booking fare record found!");
                return false;
            }
        }
    });
    return false;
}



//function calculateFare() {
//    $.ajax({
//        type: 'POST',
//        url: '../MapMyIndia/GetDistanceTimeDetails?City_From_Id=' + $("#City_From_Id").val()
//        + '&City_To_Id=' + $("#City_To_Id").val() + '&Trip_Type_Id=' + $("#Trip_Type_Id").val()
//        + '&Car_Category_Id=' + $("#Car_Category_Id").val(),
//        dataType: 'json',
//        success: function (FareChart) {
//            if (FareChart != "") {
//                $("#Distance").val(FareChart.Distance);
//                $("#Basic_Fare").val(FareChart.Basic_Fare);
//                $("#GST_Fare").val(FareChart.GST_Fare);
                
//                $("#Duration").val(FareChart.Duration);
//                $("#FixRateNote").html(FareChart.FixRateNote);
//                $("#Fare").val(Math.round(FareChart.Fare));
                
//            }
//            else {
//                $("#Distance").val("");
//                $("#Basic_Fare").val("");
//                $("#GST_Fare").val("");
//                $("#Fare").val("");
//                $("#Duration").val("");

//                toastr.clear();
//                toastr.error("No booking fare record found!");
//                return false;
//            }
//        }
//    });
//    return false;
//}


function calculateDiscount() {
    $.ajax({
        type: 'POST',
        url: '../BookingManagement/calculateDiscount?Coupon_Code=' + $("#Coupon_Code").val(),
        dataType: 'json',
        success: function (CouponData) {
            if (CouponData != "") {

                if (CouponData.Is_Active == true) {

                    $('#hd_OldBasic_Fare').val($('#Basic_Fare').val());
                    $('#hd_OldGST_Fare').val($('#GST_Fare').val());
                    $('#hd_OldFare').val($('#Fare').val());
                    var Basic_Fare = (isNaN(parseFloat($('#Basic_Fare').val()))) ? 0 : parseFloat($('#Basic_Fare').val());
                    if (Basic_Fare >= CouponData.Fare_When_Applied) {



                        var Discount = (Basic_Fare * CouponData.Discount) / 100;
                        if (Discount > CouponData.Max_Discount) {
                            Discount = CouponData.Max_Discount;
                        }
                        var finalBasicAmount = Basic_Fare - Discount;
                        $('#Basic_Fare').val(finalBasicAmount.toFixed(2));

                        var GST_Fare = (finalBasicAmount * 5) / 100;
                        $('#GST_Fare').val(GST_Fare.toFixed(2));
                        var Fare = finalBasicAmount + GST_Fare;
                        $('#Fare').val(Math.round(Fare));

                        $("#Coupon_Code_Applied").html(Discount.toFixed(2) + " rupee discount applied sucessfully");
                        $('#Coupon_Discount').val(Discount.toFixed(2));

                        $("#btnRemoveDiscount").show();
                        $("#btnApplyDiscount").hide();
                    }
                    else {

                        toastr.clear();
                        toastr.error("Basic fare Must be " + CouponData.Fare_When_Applied +" for this coupon");
                        return false;
                    }

                }

                else {

                    toastr.clear();
                    toastr.error("sorry this Coupon is expired!");
                    return false;
                }

            }
            else {

                toastr.clear();
                toastr.error("No Coupon found!");
                return false;
            }
        }
    });
    return false;
}





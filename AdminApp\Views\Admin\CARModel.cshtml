﻿@model DBLinker.Lib.Model.M_Car_Model
@{
    ViewBag.Title = "CARModel";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Car Model Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Car Model Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Car Model</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {


            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Car Model</header>

                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.TextBoxFor(x => x.Car_Model_Name, new { @class = "form-control", @placeholder = "enter car model name !" })
                                    @Html.ValidationMessageFor(model => model.Car_Model_Name, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label">Car Model Name</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <label class="mdl-textfield__label">Car Company</label>
                                    @if (ViewBag.carCompanyRepo != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Company_PKID, new SelectList(ViewBag.carCompanyRepo, "PKID", "Company_Name"), "-- Select Car Company --", new { @class = "form-control" })
                                    }

                                </div>
                            </div>


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    <label class="mdl-textfield__label">Car Category</label>
                                    @if (ViewBag.carCategortyRepo != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Category_PKID, new SelectList(ViewBag.carCategortyRepo, "PKID", "Car_Category_Abbr"), "-- Select Car Category --", new { @class = "form-control" })
                                    }

                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @if (ViewBag.CarSegment != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Segment_PKID, new SelectList(ViewBag.CarSegment, "PKID", "Car_Segment"), "-- Select Car Segment --", new { @class = "form-control" })
                                    }
                                    <label class="mdl-textfield__label">Car Segment</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @if (ViewBag.FuelTypeRepo != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Fuel_Type_Status, new SelectList(ViewBag.FuelTypeRepo, "PKID", "CAR_FUEL_TYPE"), "-- Select Car Fuel type --", new { @class = "form-control" })
                                    }
                                    <label class="mdl-textfield__label">Car Fuel Type</label>
                                </div>
                            </div>



                            <div class="col-lg-12 p-t-20 text-center">


                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Car Models</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th>Car Model Name</th>
                                        <th>Company Name</th>
                                        <th>Category Name</th>
                                        <th>Car Segment</th>
                                        <th>Fuel Type</th>

                                        <th>Status</th>

                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.CarModel != null)
                                    {
                                        foreach (var item in ViewBag.CarModel)
                                        {

                                            <tr class="odd gradeX">
                                                <input type="hidden" id="<EMAIL>" value="@item.Car_Model_Name" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Car_Company_PKID" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Car_Category_PKID" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Car_Segment_PKID" />


                                                <input type="hidden" id="<EMAIL>" value="@item.Car_Fuel_Type_Status" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />

                                                <td width="20%"> @item.Car_Model_Name</td>
                                                <td width="15%">@item.Company_Name</td>
                                                <td width="13%">@item.Car_Category_Name</td>
                                                <td width="13%">@item.Car_Segment_Name</td>

                                                <td width="13%">@item.CAR_FUEL_TYPE</td>



                                                <td width="8%">
                                                    @{
                                                        if (item.Is_Active != null && item.Is_Active != false)
                                                        {
                                                            <span class="label label-sm label-success"> Active </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="label label-sm label-danger"> In-Active </span>
                                                        }
                                                    }
                                                </td>


                                                <td align="center" width="20%">
                                                    <a class="btn btn-tbl-edit btn-xs" onclick="ActivateDeactivate(@item.PKID)" title="Enable">
                                                        <i class="fa fa-check"></i>
                                                    </a>
                                                    <a class="btn btn-tbl-delete btn-xs" onclick="Edit(@item.PKID)" title="Edit">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>



                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<script src="~/Scripts/CrudFile/CARModelCrudJs.js"></script>
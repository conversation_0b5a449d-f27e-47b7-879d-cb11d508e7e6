﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
   public class M_Car_Docs
    {

        public int PKID { get; set; }
        public int Car_PKID { get; set; }
        public int Car_Doc_Id { get; set; }
        public string Car_Doc_Name { get; set; }
        public string Car_Doc_Path { get; set; }
        public DateTime? Car_Doc_EndDate { get; set; }
        public HttpPostedFileBase Car_Doc_UP { get; set; }
        public bool Is_Verified { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Last_Modified_Date { get; set; }
        public int Last_Modified_By { get; set; }
    }
}

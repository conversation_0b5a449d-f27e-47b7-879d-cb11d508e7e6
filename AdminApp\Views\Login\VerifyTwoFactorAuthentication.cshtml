﻿

@{
    Layout = null;
}

<!DOCTYPE html>

<html lang="en">

<head>
    <title>Verify Two Factor Authentication</title>
    <script src="~/Scripts/jquery.min.js"></script>
    <link href="~/Content/toastr.css" rel="stylesheet" />
    <script src="~/Scripts/toastr.min.js"></script>
    <style>
        body {
            font: 13px/20px 'Lucida Grande', Tahoma, Verdana, sans-serif;
            color: #404040;
            background: #0ca3d2;
        }

        .container {
            margin: 80px auto;
            width: 640px;
        }

        .login {
            position: relative;
            margin: 0 auto;
            padding: 20px 20px 20px;
            width: 310px;
            background: white;
            border-radius: 3px;
            &:before;

        {
            content: '';
            position: absolute;
            top: -8px;
            right: -8px;
            bottom: -8px;
            left: -8px;
            z-index: -1;
            background: rgba(black, .08);
            border-radius: 4px;
        }

        h1 {
            margin: -20px -20px 21px;
            line-height: 40px;
            font-size: 15px;
            font-weight: bold;
            color: #555;
            text-align: center;
            text-shadow: 0 1px white;
            background: #f3f3f3;
            border-bottom: 1px solid #cfcfcf;
            border-radius: 3px 3px 0 0;
        }

        p {
            margin: 20px 0 0;
        }

            p:first-child {
                margin-top: 0;
            }

        input[type=text], input[type=password] {
            width: 278px;
        }

        p.remember_me {
            float: left;
            line-height: 31px;
            label;

        {
            font-size: 12px;
            color: #777;
            cursor: pointer;
        }

        input {
            position: relative;
            bottom: 1px;
            margin-right: 4px;
            vertical-align: middle;
        }

        }

        p.submit {
            text-align: right;
        }

        }

        .login-help {
            margin: 20px 0;
            font-size: 11px;
            color: white;
            text-align: center;
            text-shadow: 0 1px #2a85a1;
            a;

        {
            color: #cce7fa;
            text-decoration: none;
            &:hover;

        {
            text-decoration: underline;
        }

        }
        }

        :-moz-placeholder {
            color: #c9c9c9 !important;
            font-size: 13px;
        }

        ::-webkit-input-placeholder {
            color: #ccc;
            font-size: 13px;
        }

        input {
            font-family: 'Lucida Grande', Tahoma, Verdana, sans-serif;
            font-size: 14px;
        }

            input[type=text], input[type=password] {
                margin: 5px;
                padding: 0 10px;
                width: 200px;
                height: 34px;
                color: #404040;
                background: white;
                border: 1px solid;
                border-color: #c4c4c4 #d1d1d1 #d4d4d4;
                border-radius: 2px;
                outline: 5px solid #eff4f7;
                &:focus;

        {
            border-color: #7dc9e2;
            outline-color: #dceefc;
            outline-offset: 0;
        }

        }

        input[type=button] {
            padding: 0 18px;
            height: 29px;
            font-size: 12px;
            font-weight: bold;
            color: #527881;
            text-shadow: 0 1px #e3f1f1;
            background: #cde5ef;
            border: 1px solid;
            border-color: #b4ccce #b3c0c8 #9eb9c2;
            border-radius: 16px;
            outline: 0;
            &:active;

        {
            background: #cde5ef;
            border-color: #9eb9c2 #b3c0c8 #b4ccce;
        }

        }

        .lt-ie9 {
            input [type=text], input[type=password];

        {
            line-height: 34px;
        }

        }
    </style>

    <script>

        $(document).ready(function ()
        {


            $("#btnVerify").click(function ()
            {
                if (IsValidate()) {
                    $.ajax({
                        url: 'VerifyTwoFactorAuthentication',
                        type: "POST",
                        traditional: true,
                        data: { "PassCode": $("#FAAuth").val() },
                        dataType: "json",
                        success: function (response) {
                            if (response == true)
                            {
                                toastr.success("Two Factor Authentication Sucessfully Verified !");
                                document.location.href = "http://localhost:55967/Admin/Dashboard";
                            }
                            else {
                                toastr.error("Two Factor Authentication Not Verified !");
                            }

                        },
                        failure: function (response) {

                        },
                        error: function (response) {
                            toastr.error(response.responseText);
                        }
                    });

                }

            });


           
         
        });



        function IsValidate() {
            if ($("#FAAuth").val() == "") {
                toastr.clear();
                toastr.error("Please enter 2FA Verfication Code  !");
                return false;
            }
           
            return true;
        }

    </script>

</head>
<body>
    <section class="container">
        <div class="login">
            <h1>Verfiy with 2FA</h1>

            @using (Html.BeginForm())
            {
                @Html.AntiForgeryToken()

                <p>
                   
                    <input type="text" value="" id="FAAuth" placeholder="enter 6 digit 2FA"/>



                </p>
                

                <p>

                    <input type="button" id="btnVerify" name="commit" value="2FA Verfication">
                    @Html.ActionLink("LogOut", "LogOut", "Admin", new { @status = "true", @class = "button" }, null)
                </p>

   

            }
        </div>
    </section>


</body>
</html>

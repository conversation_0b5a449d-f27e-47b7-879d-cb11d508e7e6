//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_BOOKINGS
    {
        public long PKID { get; set; }
        public string Booking_Id { get; set; }
        public string City_From_Id { get; set; }
        public string City_To_Id { get; set; }
        public string PickUp_Address { get; set; }
        public string DropOff_Address { get; set; }
        public string PickUpLatLong { get; set; }
        public string DropOffLatLong { get; set; }
        public string TravelerName { get; set; }
        public string TravelerEmail { get; set; }
        public string TravelerPhone { get; set; }
        public string BookingSessionID { get; set; }
        public string PaymentOption { get; set; }
        public string PaymentMode { get; set; }
        public Nullable<bool> IsWhatsAppNumber { get; set; }
        public string CashToPayDriver { get; set; }
        public string DriverNightCharge { get; set; }
        public string Trip_Type_Id { get; set; }
        public string Car_Category_Id { get; set; }
        public string Duration { get; set; }
        public string Distance { get; set; }
        public string Basic_Fare { get; set; }
        public string Driver_Charge { get; set; }
        public string Toll_Charge { get; set; }
        public string GST { get; set; }
        public string Fare { get; set; }
        public string GST_Fare { get; set; }
        public string Coupon_Code { get; set; }
        public string Coupon_Discount { get; set; }
        public Nullable<System.DateTime> Booking_Date { get; set; }
        public Nullable<System.DateTime> PickUp_Date { get; set; }
        public string PickUp_Time { get; set; }
        public string Vendor_Id { get; set; }
        public string Car_Id { get; set; }
        public string Booking_Status_Id { get; set; }
        public string Booking_Remark { get; set; }
        public string Invoice_No { get; set; }
        public Nullable<System.DateTime> Invoice_Date { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public string Created_By { get; set; }
        public string Updated_By { get; set; }
        public string razorpay_payment_id { get; set; }
        public string razorpay_order_id { get; set; }
        public string razorpay_signature { get; set; }
        public string razorpay_status { get; set; }
    }
}

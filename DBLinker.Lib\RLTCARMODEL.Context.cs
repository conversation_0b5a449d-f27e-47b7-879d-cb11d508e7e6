﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class RLTDBContext : DbContext
    {
        public RLTDBContext()
            : base("name=RLTDBContext")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<C__EFMigrationsHistory> C__EFMigrationsHistory { get; set; }
        public virtual DbSet<C__EFMigrationsHistory1> C__EFMigrationsHistory1 { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<RLT_AboutUs> RLT_AboutUs { get; set; }
        public virtual DbSet<RLT_ADMIN_ROLE> RLT_ADMIN_ROLE { get; set; }
        public virtual DbSet<RLT_ADMIN_USER> RLT_ADMIN_USER { get; set; }
        public virtual DbSet<RLT_Admin_User_Info> RLT_Admin_User_Info { get; set; }
        public virtual DbSet<RLT_BANK_NAMES> RLT_BANK_NAMES { get; set; }
        public virtual DbSet<RLT_BOOKING> RLT_BOOKING { get; set; }
        public virtual DbSet<RLT_BOOKING_FARE> RLT_BOOKING_FARE { get; set; }
        public virtual DbSet<RLT_BOOKING_GST> RLT_BOOKING_GST { get; set; }
        public virtual DbSet<RLT_BOOKING_MODE> RLT_BOOKING_MODE { get; set; }
        public virtual DbSet<RLT_BOOKING_PAYMENT_DETAILS> RLT_BOOKING_PAYMENT_DETAILS { get; set; }
        public virtual DbSet<RLT_BOOKING_RULES> RLT_BOOKING_RULES { get; set; }
        public virtual DbSet<RLT_BOOKING_STATUS> RLT_BOOKING_STATUS { get; set; }
        public virtual DbSet<RLT_BOOKINGS> RLT_BOOKINGS { get; set; }
        public virtual DbSet<RLT_CAR_CATEGORY> RLT_CAR_CATEGORY { get; set; }
        public virtual DbSet<RLT_CAR_CHARGES_FECILITIES_DETAILS> RLT_CAR_CHARGES_FECILITIES_DETAILS { get; set; }
        public virtual DbSet<RLT_CAR_COMPANY> RLT_CAR_COMPANY { get; set; }
        public virtual DbSet<RLT_CAR_DETAILS> RLT_CAR_DETAILS { get; set; }
        public virtual DbSet<RLT_CAR_DOCS> RLT_CAR_DOCS { get; set; }
        public virtual DbSet<RLT_CAR_DRIVER_DETAILS> RLT_CAR_DRIVER_DETAILS { get; set; }
        public virtual DbSet<RLT_CAR_DRIVER_DOCS> RLT_CAR_DRIVER_DOCS { get; set; }
        public virtual DbSet<RLT_CAR_FUEL_TYPES> RLT_CAR_FUEL_TYPES { get; set; }
        public virtual DbSet<RLT_CAR_MODEL> RLT_CAR_MODEL { get; set; }
        public virtual DbSet<RLT_CAR_OWNER_BANK_DETAILS> RLT_CAR_OWNER_BANK_DETAILS { get; set; }
        public virtual DbSet<RLT_CAR_OWNER_DETAILS> RLT_CAR_OWNER_DETAILS { get; set; }
        public virtual DbSet<RLT_CAR_SEGMENT> RLT_CAR_SEGMENT { get; set; }
        public virtual DbSet<RLT_CITY> RLT_CITY { get; set; }
        public virtual DbSet<RLT_CLASS_MASTER> RLT_CLASS_MASTER { get; set; }
        public virtual DbSet<RLT_Company_Details> RLT_Company_Details { get; set; }
        public virtual DbSet<RLT_CONTACT_ENQUIRIES> RLT_CONTACT_ENQUIRIES { get; set; }
        public virtual DbSet<RLT_ContactInformation> RLT_ContactInformation { get; set; }
        public virtual DbSet<RLT_COUNTRY> RLT_COUNTRY { get; set; }
        public virtual DbSet<RLT_Department_Name> RLT_Department_Name { get; set; }
        public virtual DbSet<RLT_DISCOUNT_COUPON> RLT_DISCOUNT_COUPON { get; set; }
        public virtual DbSet<RLT_DOCUMNETS_NAME> RLT_DOCUMNETS_NAME { get; set; }
        public virtual DbSet<RLT_DRIVER_APPROVE_STATUS> RLT_DRIVER_APPROVE_STATUS { get; set; }
        public virtual DbSet<RLT_DRIVER_ENQUIRIES> RLT_DRIVER_ENQUIRIES { get; set; }
        public virtual DbSet<RLT_ENTITY_GENERALS> RLT_ENTITY_GENERALS { get; set; }
        public virtual DbSet<RLT_FAQ_Details> RLT_FAQ_Details { get; set; }
        public virtual DbSet<RLT_LOCATION_CODE> RLT_LOCATION_CODE { get; set; }
        public virtual DbSet<RLT_LOG> RLT_LOG { get; set; }
        public virtual DbSet<RLT_MENU_MASTER> RLT_MENU_MASTER { get; set; }
        public virtual DbSet<RLT_NextId> RLT_NextId { get; set; }
        public virtual DbSet<RLT_PAYMENT_METHOD> RLT_PAYMENT_METHOD { get; set; }
        public virtual DbSet<RLT_PrivacyPolicy> RLT_PrivacyPolicy { get; set; }
        public virtual DbSet<RLT_ROLE_MENU_MAPPING> RLT_ROLE_MENU_MAPPING { get; set; }
        public virtual DbSet<RLT_ROUTE_PLAN> RLT_ROUTE_PLAN { get; set; }
        public virtual DbSet<RLT_ROUTES_DETAILS> RLT_ROUTES_DETAILS { get; set; }
        public virtual DbSet<RLT_Services> RLT_Services { get; set; }
        public virtual DbSet<RLT_STATE> RLT_STATE { get; set; }
        public virtual DbSet<RLT_SUBSCRIBERS> RLT_SUBSCRIBERS { get; set; }
        public virtual DbSet<RLT_Template_Mail_Message> RLT_Template_Mail_Message { get; set; }
        public virtual DbSet<RLT_Template_Master> RLT_Template_Master { get; set; }
        public virtual DbSet<RLT_TermsConditions> RLT_TermsConditions { get; set; }
        public virtual DbSet<RLT_TRIP_TYPES> RLT_TRIP_TYPES { get; set; }
        public virtual DbSet<RLT_USER_LOGIN_DETAILS> RLT_USER_LOGIN_DETAILS { get; set; }
        public virtual DbSet<RLT_UserFeedBack> RLT_UserFeedBack { get; set; }
        public virtual DbSet<RLT_Vendor_Bank_Details> RLT_Vendor_Bank_Details { get; set; }
        public virtual DbSet<RLT_VENDOR_CAR_DETAILS> RLT_VENDOR_CAR_DETAILS { get; set; }
        public virtual DbSet<RLT_Vendor_Details> RLT_Vendor_Details { get; set; }
        public virtual DbSet<RLT_Vendor_Docs> RLT_Vendor_Docs { get; set; }
        public virtual DbSet<RLTBookingAuditTrail> RLTBookingAuditTrails { get; set; }
        public virtual DbSet<RLTConfigurationAuditTrail> RLTConfigurationAuditTrails { get; set; }
        public virtual DbSet<RLTMostFavoriteRoute> RLTMostFavoriteRoutes { get; set; }
        public virtual DbSet<RLTResource> RLTResources { get; set; }
        public virtual DbSet<RLTResourceStau> RLTResourceStaus { get; set; }
        public virtual DbSet<RoleClaim> RoleClaims { get; set; }
        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<TempBooking> TempBookings { get; set; }
        public virtual DbSet<UserClaim> UserClaims { get; set; }
        public virtual DbSet<UserDiscountAvailed> UserDiscountAvaileds { get; set; }
        public virtual DbSet<UserLogin> UserLogins { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<UserToken> UserTokens { get; set; }
        public virtual DbSet<Vendor_Car_Driver_Docs> Vendor_Car_Driver_Docs { get; set; }
        public virtual DbSet<RefreshToken> RefreshTokens { get; set; }
        public virtual DbSet<Role1> Roles1 { get; set; }
        public virtual DbSet<RoleClaims1> RoleClaims1 { get; set; }
        public virtual DbSet<User1> Users1 { get; set; }
        public virtual DbSet<UserClaims1> UserClaims1 { get; set; }
        public virtual DbSet<UserLogins1> UserLogins1 { get; set; }
        public virtual DbSet<UserTokens1> UserTokens1 { get; set; }
    
        public virtual ObjectResult<Proc_GetUserDtl_Result> Proc_GetUserDtl(string userId)
        {
            var userIdParameter = userId != null ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Proc_GetUserDtl_Result>("Proc_GetUserDtl", userIdParameter);
        }
    
        public virtual ObjectResult<SP_GET_REPORT_DATA_Result> SP_GET_REPORT_DATA(Nullable<int> flag, Nullable<int> cityFrom, Nullable<int> cityTo, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> bookingStatusId, string bookingId, string customerName, string customerMobile)
        {
            var flagParameter = flag.HasValue ?
                new ObjectParameter("Flag", flag) :
                new ObjectParameter("Flag", typeof(int));
    
            var cityFromParameter = cityFrom.HasValue ?
                new ObjectParameter("CityFrom", cityFrom) :
                new ObjectParameter("CityFrom", typeof(int));
    
            var cityToParameter = cityTo.HasValue ?
                new ObjectParameter("CityTo", cityTo) :
                new ObjectParameter("CityTo", typeof(int));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("FromDate", fromDate) :
                new ObjectParameter("FromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            var bookingStatusIdParameter = bookingStatusId.HasValue ?
                new ObjectParameter("BookingStatusId", bookingStatusId) :
                new ObjectParameter("BookingStatusId", typeof(int));
    
            var bookingIdParameter = bookingId != null ?
                new ObjectParameter("BookingId", bookingId) :
                new ObjectParameter("BookingId", typeof(string));
    
            var customerNameParameter = customerName != null ?
                new ObjectParameter("CustomerName", customerName) :
                new ObjectParameter("CustomerName", typeof(string));
    
            var customerMobileParameter = customerMobile != null ?
                new ObjectParameter("CustomerMobile", customerMobile) :
                new ObjectParameter("CustomerMobile", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SP_GET_REPORT_DATA_Result>("SP_GET_REPORT_DATA", flagParameter, cityFromParameter, cityToParameter, fromDateParameter, toDateParameter, bookingStatusIdParameter, bookingIdParameter, customerNameParameter, customerMobileParameter);
        }
    
        public virtual int usp_Booking_Create(string booking_Id, string pickUpCity, string dropOffCity, string tripType, string carCategory, string duration, Nullable<decimal> distance, Nullable<decimal> basicFare, Nullable<decimal> driverCharge, Nullable<decimal> gST, Nullable<decimal> fare, Nullable<decimal> gSTFare, string couponCode, Nullable<decimal> couponDiscount, string pickUpAddress, string dropOffAddress, Nullable<System.DateTime> pickUpDate, string pickUpTime, string travelerName, string phoneNumber, string mailId, Nullable<int> paymentMode, string bookingCreatedBy, string razorpayPaymentID, string razorpayOrderID, string razorpaySignature, string razorpayStatus, string pickUpAddressLongLat, string pickUpAddressLongitude, Nullable<decimal> cashAmountToPayDriver, Nullable<int> paymentOption, Nullable<decimal> tollCharge, ObjectParameter result)
        {
            var booking_IdParameter = booking_Id != null ?
                new ObjectParameter("Booking_Id", booking_Id) :
                new ObjectParameter("Booking_Id", typeof(string));
    
            var pickUpCityParameter = pickUpCity != null ?
                new ObjectParameter("PickUpCity", pickUpCity) :
                new ObjectParameter("PickUpCity", typeof(string));
    
            var dropOffCityParameter = dropOffCity != null ?
                new ObjectParameter("DropOffCity", dropOffCity) :
                new ObjectParameter("DropOffCity", typeof(string));
    
            var tripTypeParameter = tripType != null ?
                new ObjectParameter("TripType", tripType) :
                new ObjectParameter("TripType", typeof(string));
    
            var carCategoryParameter = carCategory != null ?
                new ObjectParameter("CarCategory", carCategory) :
                new ObjectParameter("CarCategory", typeof(string));
    
            var durationParameter = duration != null ?
                new ObjectParameter("Duration", duration) :
                new ObjectParameter("Duration", typeof(string));
    
            var distanceParameter = distance.HasValue ?
                new ObjectParameter("Distance", distance) :
                new ObjectParameter("Distance", typeof(decimal));
    
            var basicFareParameter = basicFare.HasValue ?
                new ObjectParameter("BasicFare", basicFare) :
                new ObjectParameter("BasicFare", typeof(decimal));
    
            var driverChargeParameter = driverCharge.HasValue ?
                new ObjectParameter("DriverCharge", driverCharge) :
                new ObjectParameter("DriverCharge", typeof(decimal));
    
            var gSTParameter = gST.HasValue ?
                new ObjectParameter("GST", gST) :
                new ObjectParameter("GST", typeof(decimal));
    
            var fareParameter = fare.HasValue ?
                new ObjectParameter("Fare", fare) :
                new ObjectParameter("Fare", typeof(decimal));
    
            var gSTFareParameter = gSTFare.HasValue ?
                new ObjectParameter("GSTFare", gSTFare) :
                new ObjectParameter("GSTFare", typeof(decimal));
    
            var couponCodeParameter = couponCode != null ?
                new ObjectParameter("CouponCode", couponCode) :
                new ObjectParameter("CouponCode", typeof(string));
    
            var couponDiscountParameter = couponDiscount.HasValue ?
                new ObjectParameter("CouponDiscount", couponDiscount) :
                new ObjectParameter("CouponDiscount", typeof(decimal));
    
            var pickUpAddressParameter = pickUpAddress != null ?
                new ObjectParameter("PickUpAddress", pickUpAddress) :
                new ObjectParameter("PickUpAddress", typeof(string));
    
            var dropOffAddressParameter = dropOffAddress != null ?
                new ObjectParameter("DropOffAddress", dropOffAddress) :
                new ObjectParameter("DropOffAddress", typeof(string));
    
            var pickUpDateParameter = pickUpDate.HasValue ?
                new ObjectParameter("PickUpDate", pickUpDate) :
                new ObjectParameter("PickUpDate", typeof(System.DateTime));
    
            var pickUpTimeParameter = pickUpTime != null ?
                new ObjectParameter("PickUpTime", pickUpTime) :
                new ObjectParameter("PickUpTime", typeof(string));
    
            var travelerNameParameter = travelerName != null ?
                new ObjectParameter("TravelerName", travelerName) :
                new ObjectParameter("TravelerName", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var mailIdParameter = mailId != null ?
                new ObjectParameter("MailId", mailId) :
                new ObjectParameter("MailId", typeof(string));
    
            var paymentModeParameter = paymentMode.HasValue ?
                new ObjectParameter("PaymentMode", paymentMode) :
                new ObjectParameter("PaymentMode", typeof(int));
    
            var bookingCreatedByParameter = bookingCreatedBy != null ?
                new ObjectParameter("BookingCreatedBy", bookingCreatedBy) :
                new ObjectParameter("BookingCreatedBy", typeof(string));
    
            var razorpayPaymentIDParameter = razorpayPaymentID != null ?
                new ObjectParameter("RazorpayPaymentID", razorpayPaymentID) :
                new ObjectParameter("RazorpayPaymentID", typeof(string));
    
            var razorpayOrderIDParameter = razorpayOrderID != null ?
                new ObjectParameter("RazorpayOrderID", razorpayOrderID) :
                new ObjectParameter("RazorpayOrderID", typeof(string));
    
            var razorpaySignatureParameter = razorpaySignature != null ?
                new ObjectParameter("RazorpaySignature", razorpaySignature) :
                new ObjectParameter("RazorpaySignature", typeof(string));
    
            var razorpayStatusParameter = razorpayStatus != null ?
                new ObjectParameter("RazorpayStatus", razorpayStatus) :
                new ObjectParameter("RazorpayStatus", typeof(string));
    
            var pickUpAddressLongLatParameter = pickUpAddressLongLat != null ?
                new ObjectParameter("PickUpAddressLongLat", pickUpAddressLongLat) :
                new ObjectParameter("PickUpAddressLongLat", typeof(string));
    
            var pickUpAddressLongitudeParameter = pickUpAddressLongitude != null ?
                new ObjectParameter("PickUpAddressLongitude", pickUpAddressLongitude) :
                new ObjectParameter("PickUpAddressLongitude", typeof(string));
    
            var cashAmountToPayDriverParameter = cashAmountToPayDriver.HasValue ?
                new ObjectParameter("CashAmountToPayDriver", cashAmountToPayDriver) :
                new ObjectParameter("CashAmountToPayDriver", typeof(decimal));
    
            var paymentOptionParameter = paymentOption.HasValue ?
                new ObjectParameter("PaymentOption", paymentOption) :
                new ObjectParameter("PaymentOption", typeof(int));
    
            var tollChargeParameter = tollCharge.HasValue ?
                new ObjectParameter("TollCharge", tollCharge) :
                new ObjectParameter("TollCharge", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Booking_Create", booking_IdParameter, pickUpCityParameter, dropOffCityParameter, tripTypeParameter, carCategoryParameter, durationParameter, distanceParameter, basicFareParameter, driverChargeParameter, gSTParameter, fareParameter, gSTFareParameter, couponCodeParameter, couponDiscountParameter, pickUpAddressParameter, dropOffAddressParameter, pickUpDateParameter, pickUpTimeParameter, travelerNameParameter, phoneNumberParameter, mailIdParameter, paymentModeParameter, bookingCreatedByParameter, razorpayPaymentIDParameter, razorpayOrderIDParameter, razorpaySignatureParameter, razorpayStatusParameter, pickUpAddressLongLatParameter, pickUpAddressLongitudeParameter, cashAmountToPayDriverParameter, paymentOptionParameter, tollChargeParameter, result);
        }
    
        public virtual int usp_Booking_Execute(string booking_Id, string city_From_Id, string city_To_Id, string pickUp_Address, string dropOff_Address, string pickUpLatLong, string dropOffLatLong, string travelerName, string travelerEmail, string travelerPhone, string bookingSessionID, string paymentOption, string paymentMode, Nullable<bool> isWhatsAppNumber, string cashToPayDriver, string driverNightCharge, string trip_Type_Id, string car_Category_Id, string distance, string basic_Fare, string toll_Charge, string gST, string fare, string gST_Fare, string coupon_Code, string coupon_Discount, string pickUp_Date, string pickUp_Time)
        {
            var booking_IdParameter = booking_Id != null ?
                new ObjectParameter("Booking_Id", booking_Id) :
                new ObjectParameter("Booking_Id", typeof(string));
    
            var city_From_IdParameter = city_From_Id != null ?
                new ObjectParameter("City_From_Id", city_From_Id) :
                new ObjectParameter("City_From_Id", typeof(string));
    
            var city_To_IdParameter = city_To_Id != null ?
                new ObjectParameter("City_To_Id", city_To_Id) :
                new ObjectParameter("City_To_Id", typeof(string));
    
            var pickUp_AddressParameter = pickUp_Address != null ?
                new ObjectParameter("PickUp_Address", pickUp_Address) :
                new ObjectParameter("PickUp_Address", typeof(string));
    
            var dropOff_AddressParameter = dropOff_Address != null ?
                new ObjectParameter("DropOff_Address", dropOff_Address) :
                new ObjectParameter("DropOff_Address", typeof(string));
    
            var pickUpLatLongParameter = pickUpLatLong != null ?
                new ObjectParameter("PickUpLatLong", pickUpLatLong) :
                new ObjectParameter("PickUpLatLong", typeof(string));
    
            var dropOffLatLongParameter = dropOffLatLong != null ?
                new ObjectParameter("DropOffLatLong", dropOffLatLong) :
                new ObjectParameter("DropOffLatLong", typeof(string));
    
            var travelerNameParameter = travelerName != null ?
                new ObjectParameter("TravelerName", travelerName) :
                new ObjectParameter("TravelerName", typeof(string));
    
            var travelerEmailParameter = travelerEmail != null ?
                new ObjectParameter("TravelerEmail", travelerEmail) :
                new ObjectParameter("TravelerEmail", typeof(string));
    
            var travelerPhoneParameter = travelerPhone != null ?
                new ObjectParameter("TravelerPhone", travelerPhone) :
                new ObjectParameter("TravelerPhone", typeof(string));
    
            var bookingSessionIDParameter = bookingSessionID != null ?
                new ObjectParameter("BookingSessionID", bookingSessionID) :
                new ObjectParameter("BookingSessionID", typeof(string));
    
            var paymentOptionParameter = paymentOption != null ?
                new ObjectParameter("PaymentOption", paymentOption) :
                new ObjectParameter("PaymentOption", typeof(string));
    
            var paymentModeParameter = paymentMode != null ?
                new ObjectParameter("PaymentMode", paymentMode) :
                new ObjectParameter("PaymentMode", typeof(string));
    
            var isWhatsAppNumberParameter = isWhatsAppNumber.HasValue ?
                new ObjectParameter("IsWhatsAppNumber", isWhatsAppNumber) :
                new ObjectParameter("IsWhatsAppNumber", typeof(bool));
    
            var cashToPayDriverParameter = cashToPayDriver != null ?
                new ObjectParameter("CashToPayDriver", cashToPayDriver) :
                new ObjectParameter("CashToPayDriver", typeof(string));
    
            var driverNightChargeParameter = driverNightCharge != null ?
                new ObjectParameter("DriverNightCharge", driverNightCharge) :
                new ObjectParameter("DriverNightCharge", typeof(string));
    
            var trip_Type_IdParameter = trip_Type_Id != null ?
                new ObjectParameter("Trip_Type_Id", trip_Type_Id) :
                new ObjectParameter("Trip_Type_Id", typeof(string));
    
            var car_Category_IdParameter = car_Category_Id != null ?
                new ObjectParameter("Car_Category_Id", car_Category_Id) :
                new ObjectParameter("Car_Category_Id", typeof(string));
    
            var distanceParameter = distance != null ?
                new ObjectParameter("Distance", distance) :
                new ObjectParameter("Distance", typeof(string));
    
            var basic_FareParameter = basic_Fare != null ?
                new ObjectParameter("Basic_Fare", basic_Fare) :
                new ObjectParameter("Basic_Fare", typeof(string));
    
            var toll_ChargeParameter = toll_Charge != null ?
                new ObjectParameter("Toll_Charge", toll_Charge) :
                new ObjectParameter("Toll_Charge", typeof(string));
    
            var gSTParameter = gST != null ?
                new ObjectParameter("GST", gST) :
                new ObjectParameter("GST", typeof(string));
    
            var fareParameter = fare != null ?
                new ObjectParameter("Fare", fare) :
                new ObjectParameter("Fare", typeof(string));
    
            var gST_FareParameter = gST_Fare != null ?
                new ObjectParameter("GST_Fare", gST_Fare) :
                new ObjectParameter("GST_Fare", typeof(string));
    
            var coupon_CodeParameter = coupon_Code != null ?
                new ObjectParameter("Coupon_Code", coupon_Code) :
                new ObjectParameter("Coupon_Code", typeof(string));
    
            var coupon_DiscountParameter = coupon_Discount != null ?
                new ObjectParameter("Coupon_Discount", coupon_Discount) :
                new ObjectParameter("Coupon_Discount", typeof(string));
    
            var pickUp_DateParameter = pickUp_Date != null ?
                new ObjectParameter("PickUp_Date", pickUp_Date) :
                new ObjectParameter("PickUp_Date", typeof(string));
    
            var pickUp_TimeParameter = pickUp_Time != null ?
                new ObjectParameter("PickUp_Time", pickUp_Time) :
                new ObjectParameter("PickUp_Time", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Booking_Execute", booking_IdParameter, city_From_IdParameter, city_To_IdParameter, pickUp_AddressParameter, dropOff_AddressParameter, pickUpLatLongParameter, dropOffLatLongParameter, travelerNameParameter, travelerEmailParameter, travelerPhoneParameter, bookingSessionIDParameter, paymentOptionParameter, paymentModeParameter, isWhatsAppNumberParameter, cashToPayDriverParameter, driverNightChargeParameter, trip_Type_IdParameter, car_Category_IdParameter, distanceParameter, basic_FareParameter, toll_ChargeParameter, gSTParameter, fareParameter, gST_FareParameter, coupon_CodeParameter, coupon_DiscountParameter, pickUp_DateParameter, pickUp_TimeParameter);
        }
    
        public virtual int usp_Contact_Enquiry_Execute(string name, string phoneNumber, string emailID, Nullable<bool> isWhatsAppNumbe, string message, ObjectParameter isSucessful)
        {
            var nameParameter = name != null ?
                new ObjectParameter("Name", name) :
                new ObjectParameter("Name", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var emailIDParameter = emailID != null ?
                new ObjectParameter("EmailID", emailID) :
                new ObjectParameter("EmailID", typeof(string));
    
            var isWhatsAppNumbeParameter = isWhatsAppNumbe.HasValue ?
                new ObjectParameter("IsWhatsAppNumbe", isWhatsAppNumbe) :
                new ObjectParameter("IsWhatsAppNumbe", typeof(bool));
    
            var messageParameter = message != null ?
                new ObjectParameter("Message", message) :
                new ObjectParameter("Message", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Contact_Enquiry_Execute", nameParameter, phoneNumberParameter, emailIDParameter, isWhatsAppNumbeParameter, messageParameter, isSucessful);
        }
    
        public virtual int usp_Driver_Enquiry_Execute(string firstName, string lastName, string phoneNumber, string emailID, Nullable<bool> isWhatsAppNumbe, string address, ObjectParameter isSucessful)
        {
            var firstNameParameter = firstName != null ?
                new ObjectParameter("FirstName", firstName) :
                new ObjectParameter("FirstName", typeof(string));
    
            var lastNameParameter = lastName != null ?
                new ObjectParameter("LastName", lastName) :
                new ObjectParameter("LastName", typeof(string));
    
            var phoneNumberParameter = phoneNumber != null ?
                new ObjectParameter("PhoneNumber", phoneNumber) :
                new ObjectParameter("PhoneNumber", typeof(string));
    
            var emailIDParameter = emailID != null ?
                new ObjectParameter("EmailID", emailID) :
                new ObjectParameter("EmailID", typeof(string));
    
            var isWhatsAppNumbeParameter = isWhatsAppNumbe.HasValue ?
                new ObjectParameter("IsWhatsAppNumbe", isWhatsAppNumbe) :
                new ObjectParameter("IsWhatsAppNumbe", typeof(bool));
    
            var addressParameter = address != null ?
                new ObjectParameter("Address", address) :
                new ObjectParameter("Address", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_Driver_Enquiry_Execute", firstNameParameter, lastNameParameter, phoneNumberParameter, emailIDParameter, isWhatsAppNumbeParameter, addressParameter, isSucessful);
        }
    
        public virtual ObjectResult<string> usp_Get_About_Us()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_Get_About_Us");
        }
    
        public virtual ObjectResult<usp_Get_Car_Category_Details_Result> usp_Get_Car_Category_Details()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Car_Category_Details_Result>("usp_Get_Car_Category_Details");
        }
    
        public virtual ObjectResult<usp_Get_Cities_Result> usp_Get_Cities()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Cities_Result>("usp_Get_Cities");
        }
    
        public virtual ObjectResult<usp_Get_Comapny_Info_Result> usp_Get_Comapny_Info()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Comapny_Info_Result>("usp_Get_Comapny_Info");
        }
    
        public virtual ObjectResult<usp_Get_Discount_Coupon_Details_Result> usp_Get_Discount_Coupon_Details(string couponCode)
        {
            var couponCodeParameter = couponCode != null ?
                new ObjectParameter("CouponCode", couponCode) :
                new ObjectParameter("CouponCode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Discount_Coupon_Details_Result>("usp_Get_Discount_Coupon_Details", couponCodeParameter);
        }
    
        public virtual ObjectResult<usp_Get_Driver_Booking_info_Result> usp_Get_Driver_Booking_info(string vendor_Id, string statusid)
        {
            var vendor_IdParameter = vendor_Id != null ?
                new ObjectParameter("Vendor_Id", vendor_Id) :
                new ObjectParameter("Vendor_Id", typeof(string));
    
            var statusidParameter = statusid != null ?
                new ObjectParameter("statusid", statusid) :
                new ObjectParameter("statusid", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Driver_Booking_info_Result>("usp_Get_Driver_Booking_info", vendor_IdParameter, statusidParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> usp_Get_Driver_Login_and_info(string emailid, string mobileno, string password, string flag)
        {
            var emailidParameter = emailid != null ?
                new ObjectParameter("emailid", emailid) :
                new ObjectParameter("emailid", typeof(string));
    
            var mobilenoParameter = mobileno != null ?
                new ObjectParameter("mobileno", mobileno) :
                new ObjectParameter("mobileno", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("password", password) :
                new ObjectParameter("password", typeof(string));
    
            var flagParameter = flag != null ?
                new ObjectParameter("flag", flag) :
                new ObjectParameter("flag", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("usp_Get_Driver_Login_and_info", emailidParameter, mobilenoParameter, passwordParameter, flagParameter);
        }
    
        public virtual ObjectResult<string> usp_Get_FAQ()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_Get_FAQ");
        }
    
        public virtual ObjectResult<string> usp_Get_Privacy_Policy()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("usp_Get_Privacy_Policy");
        }
    
        public virtual ObjectResult<usp_Get_Services_Result> usp_Get_Services()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Services_Result>("usp_Get_Services");
        }
    
        public virtual ObjectResult<usp_Get_Template_Master_Result> usp_Get_Template_Master(string templateName, string templateType)
        {
            var templateNameParameter = templateName != null ?
                new ObjectParameter("TemplateName", templateName) :
                new ObjectParameter("TemplateName", typeof(string));
    
            var templateTypeParameter = templateType != null ?
                new ObjectParameter("TemplateType", templateType) :
                new ObjectParameter("TemplateType", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_Template_Master_Result>("usp_Get_Template_Master", templateNameParameter, templateTypeParameter);
        }
    
        public virtual ObjectResult<usp_Get_User_Details_Result> usp_Get_User_Details(string userID)
        {
            var userIDParameter = userID != null ?
                new ObjectParameter("UserID", userID) :
                new ObjectParameter("UserID", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_User_Details_Result>("usp_Get_User_Details", userIDParameter);
        }
    
        public virtual ObjectResult<usp_Get_UserLogin_Details_Result> usp_Get_UserLogin_Details(string userEmailId, string userMobileNumber, string userPassword, Nullable<bool> isOTPLogin, Nullable<int> iD)
        {
            var userEmailIdParameter = userEmailId != null ?
                new ObjectParameter("UserEmailId", userEmailId) :
                new ObjectParameter("UserEmailId", typeof(string));
    
            var userMobileNumberParameter = userMobileNumber != null ?
                new ObjectParameter("UserMobileNumber", userMobileNumber) :
                new ObjectParameter("UserMobileNumber", typeof(string));
    
            var userPasswordParameter = userPassword != null ?
                new ObjectParameter("UserPassword", userPassword) :
                new ObjectParameter("UserPassword", typeof(string));
    
            var isOTPLoginParameter = isOTPLogin.HasValue ?
                new ObjectParameter("IsOTPLogin", isOTPLogin) :
                new ObjectParameter("IsOTPLogin", typeof(bool));
    
            var iDParameter = iD.HasValue ?
                new ObjectParameter("ID", iD) :
                new ObjectParameter("ID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_Get_UserLogin_Details_Result>("usp_Get_UserLogin_Details", userEmailIdParameter, userMobileNumberParameter, userPasswordParameter, isOTPLoginParameter, iDParameter);
        }
    
        public virtual int usp_update_otp(string userType, string userName, Nullable<int> oTP, ObjectParameter isSucessful)
        {
            var userTypeParameter = userType != null ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var oTPParameter = oTP.HasValue ?
                new ObjectParameter("OTP", oTP) :
                new ObjectParameter("OTP", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_update_otp", userTypeParameter, userNameParameter, oTPParameter, isSucessful);
        }
    
        public virtual int usp_UserLogin_Execute(string userEmailId, string userMobileNumber, string userPassword, Nullable<bool> isOTPLogin, ObjectParameter isSucessful)
        {
            var userEmailIdParameter = userEmailId != null ?
                new ObjectParameter("UserEmailId", userEmailId) :
                new ObjectParameter("UserEmailId", typeof(string));
    
            var userMobileNumberParameter = userMobileNumber != null ?
                new ObjectParameter("UserMobileNumber", userMobileNumber) :
                new ObjectParameter("UserMobileNumber", typeof(string));
    
            var userPasswordParameter = userPassword != null ?
                new ObjectParameter("UserPassword", userPassword) :
                new ObjectParameter("UserPassword", typeof(string));
    
            var isOTPLoginParameter = isOTPLogin.HasValue ?
                new ObjectParameter("IsOTPLogin", isOTPLogin) :
                new ObjectParameter("IsOTPLogin", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_UserLogin_Execute", userEmailIdParameter, userMobileNumberParameter, userPasswordParameter, isOTPLoginParameter, isSucessful);
        }
    }
}

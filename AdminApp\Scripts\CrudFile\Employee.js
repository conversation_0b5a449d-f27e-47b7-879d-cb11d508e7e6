﻿$(document).ready(function () {

    $("#EmployeeList").parent().parent().find("a").next().slideToggle(500);
    $("#EmployeeList").parent().parent().toggleClass('toggled');
    $("#EmployeeList").find("a").addClass("selectedMenu");


    $('#divPersonal').show();
    $('#divJob').hide();
    $('#divEducation').hide();
    $('#divContact').hide();  

   
    //Edit City Load
    if ($("#hf_Current_Address_City").val() != "") {
        GetCityByStateCurrent($("#hf_Current_Address_City").val());
    }
    if ($("#hf_Permanent_Address_City").val() != "") {
        GetCityByStateParmanent($("#hf_Permanent_Address_City").val());
    }


    $("#btnSave").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormEmployee")[0]);
            $.ajax({
                url: 'Employee',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Employee Informations Successfully Saved!", "User Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            var formData = new FormData($("#idFormEmployee")[0]);
            $.ajax({
                url: 'Employee',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Employee Informations Successfully Saved!", "User Update");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        resetForm();
    });


  
   
    $('#LnkPersonal').click(function () {

        $('#divPersonal').slideDown(1000); 
        $('#divJob').slideUp(1000);   
        $('#divEducation').slideUp(1000);   
        $('#divContact').slideUp(1000);
    });
    $('#LnkJob').click(function () {
        $('#divPersonal').slideUp(1000);
        $('#divJob').slideDown(1000);
        $('#divEducation').slideUp(1000);
        $('#divContact').slideUp(1000); 
    });
    $('#LnkEducation').click(function () {
        $('#divPersonal').slideUp(1000);
        $('#divJob').slideUp(1000);
        $('#divEducation').slideDown(1000);
        $('#divContact').slideUp(1000); 
    });
    $('#LnkContact').click(function () {
        $('#divPersonal').slideUp(1000);
        $('#divJob').slideUp(1000);
        $('#divEducation').slideUp(1000);
        $('#divContact').slideDown(1000); 
    });
    $('#EmployeeTab li > a').click(function () {
        $('#EmployeeTab li').removeClass();
        $(this).parent().addClass('active');
    });


    $("#Current_Address_State").change(function () {
        GetCityByStateCurrent(0);
    });
    $("#Permanent_Address_State").change(function () {
        GetCityByStateParmanent(0);
    });
    $("#Current_Permanent_Same1").change(function () {
        $("#Permanent_Address1").val($("#Current_Address1").val());
        $("#Permanent_Address2").val($("#Current_Address2").val());
        $("#Permanent_Address_State").val($("#Current_Address_State").val());
        GetCityByStateParmanent($("#Current_Address_City").val());

        $("#Permanent_Address1").attr("readonly", "true");
        $("#Permanent_Address2").attr("readonly", "true");
        $("#Permanent_Address_State").attr("readonly", "true");
        $("#Permanent_Address_City").attr("readonly", "true");
    });
    $("#Current_Permanent_Same2").change(function () {
        $("#Permanent_Address1").val('');
        $("#Permanent_Address2").val('');
        $("#Permanent_Address_State").val('');
        $("#Permanent_Address_City").empty();
        $("#Permanent_Address_City").append('<option value=>-- Select City --</option>');
    });


   

});




function resetForm() {

    $("#Employee_Name").val('');
    $("#Photo").val('');
    $("#DOB").val('');
}

function IsValidate() {
   

    if ($("#Employee_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Employee Name!", "Required!");
        return false;
    }

    if ($("#DOB").val() == "") {
        toastr.clear();
        toastr.error("Please enter Employee DOB!", "Required!");
        return false;
    }
    if ($("#Phone_1").val() == "") {
        toastr.clear();
        toastr.error("Please enter Mobile No!", "Required!");
        return false;
    }
    
    if ($("#Email").val() == "") {
        toastr.clear();
        toastr.error("Please enter Email !", "Required!");
        return false;
    }
  
    if ($("#Gender1").prop("checked") == false && $("#Gender2").prop("checked") == false) {
        toastr.clear();
        toastr.error("Please Select Gender!", "Required!");
        return false;
    }
    if ($("#Marrital_Status1").prop("checked") == false && $("#Marrital_Status2").prop("checked") == false) {
        toastr.clear();
        toastr.error("Please Select Marrital Status!", "Required!");
        return false;
    }

   
    return true;
}



$("#Photo").change(function (event) {

    if (PhotoCheck($("#Photo").val()) == false) {
        var tmppath = URL.createObjectURL(event.target.files[0]);
        $("#Employee_Photo_View").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
    }
    else {
        $("#Photo").val("");
        $("#Employee_Photo_View").fadeIn("fast").attr('src', "/Content/img/no-user-image.gif");
        toastr.clear();
        toastr.error("Please upload only JPG,PNG Image !");
        return false;
    }
});


function GetCityByStateCurrent(selectId) {
    $("#Current_Address_City").empty();
    $.ajax({
        type: 'POST',
        url: '../VendorManagement/GetCityByState', // Calling json method  
        dataType: 'json',
        data: { id: $("#Current_Address_State").val() },
        success: function (CarOwner) {
            $("#Current_Address_City").append('<option value=>-- Select City --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Current_Address_City").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0)
                $("#Current_Address_City").val(selectId);

        }
    });
    return false;
}

function GetCityByStateParmanent(selectId) {
    $("#Permanent_Address_City").empty();
    $.ajax({
        type: 'POST',
        url: '../VendorManagement/GetCityByState', // Calling json method  
        dataType: 'json',
        data: { id: $("#Permanent_Address_State").val() },
        success: function (CarOwner) {
            $("#Permanent_Address_City").append('<option value=>-- Select City --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Permanent_Address_City").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0)
                $("#Permanent_Address_City").val(selectId);

        }
    });
    return false;
}




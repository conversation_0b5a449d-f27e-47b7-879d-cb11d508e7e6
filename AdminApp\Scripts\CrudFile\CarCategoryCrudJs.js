﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });

    $("#btnAdd").click(function () {
        $("#hdnOperation").val("A")
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnSave").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormCarCategory")[0]);
            $.ajax({
                url: 'CarCategory',
                type: "POST",
                traditional: true,
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");
                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            }); 
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormCarCategory")[0]);
            $.ajax({
                url: 'CarCategory',
                type: "POST",
                traditional: true,
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });

    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

});

function Edit(Id) {
    $.ajax({
        url: 'CarCategoryEdit',
        type: "GET",
        data: { pkid: Id },
        dataType: "json",
        success: function (data) {
            if (data != null) {
                // fill data 
                $("#PKID").val(data.PKID);
                $("#Car_Category_Name").val(data.Car_Category_Name);
                $("#Car_Category_Abbr").val(data.Car_Category_Abbr);
                $("#Per_KM_fare").val(data.Per_KM_fare);
                $("#Capacity").val(data.Capacity);
                $("#Features").val(data.Features);
                $("#Is_Active").val(data.Is_Active);
                $("#Car_Categroy_Image").val(data.Car_Categroy_Image);
                if (data.Car_Categroy_Image != null)
                    $("#Car_Categroy_Image_viwer").attr("src", "../Docs/CarCategory/" + data.Car_Categroy_Image)
                else 
                $("#Car_Categroy_Image_viwer").attr("src", "/Content/img/no-user-image.gif")
                //focus 
                $("#Car_Category_Name").focus();
                // set default values
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
} 

function ActivateDeactivate(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('D'); 
    if ($("#PKID").val() != "") { 
        $.ajax({
            url: 'CarCategory',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !"); 
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        }); 
    } 
}

function resetForm() {
    $("#Car_Category_Name").val('');
    $("#Car_Category_Abbr").val('');
    $("#Per_KM_fare").val('');
    $("#Capacity").val('');
    $("#Features").val('');
}

function IsValidate() { 
    if ($("#Car_Category_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Car Category name !");
        return false;
    }
    if ($("#Car_Category_Abbr").val() == "") {
        toastr.clear();
        toastr.error("Please enter Car Category abbr !");
        return false;
    }
    if ($("#Per_KM_fare").val() == "") {
        toastr.clear();
        toastr.error("Please enter Per KM Fare !");
        return false;
    }
    if ($("#Capacity").val() == "") {
        toastr.clear();
        toastr.error("Please enter Capacity !");
        return false;
    }
    if ($("#hdnOperation").val() == "A" || $("#hdnOperation").val() == "") {
        if ($("#CarCategoryImage").val() == "") {
            toastr.error("Please select car category image !", "Car Category");
            return false;
        }
    } 
    return true;
} 

$("#CarCategoryImage").change(function (event) {
    if (PhotoCheck($("#CarCategoryImage").val()) == false) {
        $("#Car_Categroy_Image_viwer").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
    }
    else {
        $("#Car_Categroy_Image_viwer").fadeIn("fast").attr('src', "/Content/img/no-user-image.gif");
        toastr.clear();
        toastr.error("Please upload only JPG,PNG Image !");
        return false;
    }
});
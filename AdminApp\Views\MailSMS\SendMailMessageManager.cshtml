﻿@model DBLinker.Lib.Model.M_Template_Mail_Message

@{
    ViewBag.Title = "Template Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="//cdn.tinymce.com/4/tinymce.min.js"></script>
<script>

    tinymce.init({
        selector: "#Message_Body",
        height: 300,
        plugins: [
            "advlist autolink autosave link image lists charmap print preview hr anchor pagebreak spellchecker",
            "searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking",
            "table contextmenu directionality emoticons template textcolor paste textcolor colorpicker textpattern"
        ],

        toolbar1: "bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | styleselect formatselect fontselect fontsizeselect",
        toolbar2: "cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | undo redo | link unlink anchor image media code | insertdatetime preview | forecolor backcolor",
        toolbar3: "table | hr removeformat | subscript superscript | charmap emoticons | print fullscreen | ltr rtl | spellchecker | visualchars visualblocks nonbreaking template pagebreak restoredraft",

        menubar: false,
        toolbar_items_size: 'small',

        templates: [{
            title: 'Test template 1',
            content: 'Test 1'
        }, {
            title: 'Test template 2',
            content: 'Test 2'
        }],
        content_css: [
            '/Content/style.css'
        ]
    });

</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Mail/Message Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Mail/Message Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Send Mail/Message</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddSendMailMessageManager", "MailSMS", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormSendMailMessageManager" }))
        {


            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />

            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Send Mail/Message</header>

                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Mail_SMS_Type, "Mail", new { @id = "Mail_SMS_Type1" })
                                            <label for="radio1"><span><span></span></span> Mail</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Mail_SMS_Type, "SMS", new { @id = "Mail_SMS_Type2" })
                                            <label for="radio2"><span><span></span></span>SMS</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Template For</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.DropDownListFor(x => x.SendToType, new List<SelectListItem>{
new SelectListItem{ Text="Vendor", Value = "1" },new SelectListItem{ Text="Car Owner", Value = "2" },
new SelectListItem{ Text="Driver", Value = "3" },new SelectListItem{ Text="Employee", Value = "4" }
}, "-- Send To  --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    <label class="mdl-textfield__label required"><span>Send To </span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20" id="DivSendToId" style="display:none;">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.SendToId, new SelectList(string.Empty, "Value", "Text"), "-- Select Name --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    @Html.HiddenFor(x => x.SendToId, new { @id = "hf_SendToId" })
                                    <label class="mdl-textfield__label"><span>Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20" id="DivTemplate" style="display:none;">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.TemplateId, new SelectList(string.Empty, "Value", "Text"), "-- Select Template --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    @Html.HiddenFor(x => x.TemplateId, new { @id = "hf_TemplateId" })
                                    <label class="mdl-textfield__label required"><span>Template Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20" id="DivMobile" style="display:none;">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.MobileNo, new { @class = "mdl-textfield__input", @maxlength = "100" })
                                    <label class="mdl-textfield__label required"><span>Mobile No</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20" id="DivMail" style="display:none;">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.MailId, new { @class = "mdl-textfield__input", @maxlength = "500" })
                                    <label class="mdl-textfield__label required"><span>Mail Id</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20" id="DivMailSubject" style="display:none;">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Subject, new { @class = "mdl-textfield__input", @maxlength = "500" })
                                    <label class="mdl-textfield__label required"><span>Message Subject</span></label>
                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.TextBoxFor(x => x.Message_Body, new { @class = "mdl-textfield__input" })

                                    <label class="mdl-textfield__label required"><span>Message Body</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20 text-center">


                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Template</header>

                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">

                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th width="15%">Type</th>
                                        <th width="25%">Send To</th>
                                        <th width="35%">Template</th>
                                        <th width="10%">Mobile No/Mail</th>
                                        <th width="10%">Subject</th>
                                        <th width="10%" style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.TemplateMailMessageList != null)
                                    {
                                        foreach (var item in ViewBag.TemplateMailMessageList)
                                        {

                                            <tr class="odd gradeX">
                                                <td>@item.Mail_SMS_Type</td>
                                                @if (item.SendToType = "1")
                                                {
                                                    <td>Vendor</td>
                                                }
                                                else if (item.SendToType = "2")
                                                {
                                                    <td>Car Owner</td>
                                                }
                                                else if (item.SendToType = "3")
                                                {
                                                    <td>Driver</td>
                                                }
                                                else
                                                {
                                                    <td>Employee</td>
                                                }

                                                <td>@item.Template_Name</td>

                                                @if (item.Mail_SMS_Type = "Mail")
                                                {
                                                    <td>@item.MailId</td>
                                                }
                                                else
                                                {
                                                    <td>@item.MobileNo</td>
                                                }


                                                <td>@item.Subject</td>

                                                <td align="center">
                                                    <a class="btn btn-tbl-delete btn-xs" onclick="Delete(@item.PKID)" title="Edit">
                                                        <i class="fa fa-trash-o"></i>
                                                    </a>

                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="~/Scripts/CrudFile/SendMailMessageCrud.js"></script>





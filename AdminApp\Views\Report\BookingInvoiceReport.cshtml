﻿
@{
    ViewBag.Title = "Booking Report";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}

<script type="text/javascript">
    $(document).ready(function () {
        $("#BookingInvoiceReport").parent().parent().find("a").next().slideToggle(500);
        $("#BookingInvoiceReport").parent().parent().toggleClass('toggled');
        $("#BookingInvoiceReport").find("a").addClass("selectedMenu");


        $("#btnSearch").click(function () {
            
            if ($('#BookingId').val() != "") {
                var URL = "../CommonrdlcReport.aspx?ReportID=2" + "&BookingId=" + $('#BookingId').val();
                window.open(URL);
            }
            else {
                
                    toastr.clear();
                    toastr.error("Please enter Booking Id !");
            }
        });
        $("#btnReset").click(function () {

            $("#BookingId").val('');
        });

    });
    
     
    
</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Invoice Report</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Booking Invoice Report</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Booking Invoice Report</header>
                        
                    </div>
                    <div class="card-body row">
                      
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("BookingId", "", new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Booking Id</span> </label>
                            </div>
                        </div>
                      

                        <div class="col-lg-6 p-t-20 text-center">
                            @(PermittedAction.Is_View ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSearch'>Search</button>") : Html.Raw(""))
                           
                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>
</div>


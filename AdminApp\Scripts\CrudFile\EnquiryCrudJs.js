﻿
$(document).ready(function ()
{
    $("#dvAddUpdate").slideUp();
    $("#btnUpdate").slideUp();
  
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnUpdate").click(function () 
            {
                if (IsValidate())
                {

                    $.ajax({
                        url: 'EnquiryManager',
                        type: "POST",
                        traditional: true,
                        data: $("form").serialize(),
                        dataType: "json",
                        success: function (response) 
                        {
                            if (response > 0) {
                                toastr.success("Data Successfully Updated!");
                                document.location.reload();
                            }
                            else {
                                toastr.success("Sorry Data Not Updated,Please Try it Again !");

                            }
                        },
                        failure: function (response) {

                        },
                        error: function (response) {
                            alert(response.responseText);
                        }
                    });



                }
            });



            $("#btnReset").click(function () {

                $("#dvAddUpdate").slideUp();
                resetForm();
            });



    



});


function Edit(Id) 

{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#FirstName").val($("#fname_" + Id).val());
    $("#LastName").text($("#lname_" + Id).val());

    $("#EmailID").val($("#email_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());


    $("#IsActive").val(true);

    $("#Adult").val($("#adult_" + Id).val());
    $('#Child').val($("#child_" + Id).val());

    $("#equirymessage").val($("#message_" + Id).val());
    $("#comment").val($("#comment_" + Id).val());

    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}



function Delete(Id) {

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#FirstName").val($("#fname_" + Id).val());
    $("#LastName").text($("#lname_" + Id).val());

    $("#EmailID").val($("#email_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());


    $("#IsActive").val(true);

    $("#Adult").val($("#adult_" + Id).val());
    $('#Child').val($("#child_" + Id).val());

    $("#equirymessage").val($("#message_" + Id).val());
    $("#comment").val($("#comment_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }


    $("#hdnStatus").val(false);


    if (Id != "") {

        $.ajax({
            url: 'EnquiryManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }
}




function ActivateDeactivate(Id) {

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#FirstName").val($("#fname_" + Id).val());
    $("#LastName").text($("#lname_" + Id).val());

    $("#EmailID").val($("#email_" + Id).val());
    $("#PhoneNo").val($("#phone_" + Id).val());


    $("#IsActive").val(true);

    $("#Adult").val($("#adult_" + Id).val());
    $('#Child').val($("#child_" + Id).val());

    $("#equirymessage").val($("#message_" + Id).val());
    $("#comment").val($("#comment_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnStatus").val(false);


    if (Id!="") {

        $.ajax({
            url: 'EnquiryManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });



    }


}

function resetForm()
{


    $("#equirymessage").val('');


}

function IsValidate() 
{

    if ($("#comment").val() == "")
    {
        toastr.clear();
        toastr.error("Please enter your comment !");
        return false;
    }

    return true;
}
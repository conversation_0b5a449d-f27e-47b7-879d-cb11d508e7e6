﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace AdminApp.Mapper
{
    public class QueryStringEncoding
    {
        public static string EncryptString(string Message, string Passphrase)
        {
            byte[] Results;
            System.Text.UTF8Encoding UTF8 = new System.Text.UTF8Encoding();

            // Step 1. We hash the passphrase using MD5 
            // We use the MD5 hash generator as the result is a 128 bit byte array 
            // which is a valid length for the TripleDES encoder we use below

            MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
            byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(Passphrase));

            // Step 2. Create a new TripleDESCryptoServiceProvider object 
            TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();

            // Step 3. Setup the encoder 
            TDESAlgorithm.Key = TDESKey;
            TDESAlgorithm.Mode = CipherMode.ECB;
            TDESAlgorithm.Padding = PaddingMode.PKCS7;

            // Step 4. Convert the input string to a byte[] 
            byte[] DataToEncrypt = UTF8.GetBytes(Message);

            // Step 5. Attempt to encrypt the string 
            try
            {
                ICryptoTransform Encryptor = TDESAlgorithm.CreateEncryptor();
                Results = Encryptor.TransformFinalBlock(DataToEncrypt, 0, DataToEncrypt.Length);
            }
            finally
            {
                // Clear the TripleDes and Hashprovider services of any sensitive information 
                TDESAlgorithm.Clear();
                HashProvider.Clear();
            }

            // Step 6. Return the encrypted string as a base64 encoded string 
            return Convert.ToBase64String(Results);
        }
        public static string DecryptString(string Message, string Passphrase)
        {
            Message = Message.Replace(" ", "+");
            byte[] Results;
            System.Text.UTF8Encoding UTF8 = new System.Text.UTF8Encoding();

            // Step 1. We hash the passphrase using MD5 
            // We use the MD5 hash generator as the result is a 128 bit byte array 
            // which is a valid length for the TripleDES encoder we use below

            MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
            byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(Passphrase));

            // Step 2. Create a new TripleDESCryptoServiceProvider object 
            TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();

            // Step 3. Setup the decoder 
            TDESAlgorithm.Key = TDESKey;
            TDESAlgorithm.Mode = CipherMode.ECB;
            TDESAlgorithm.Padding = PaddingMode.PKCS7;

            // Step 4. Convert the input string to a byte[] 
            byte[] DataToDecrypt = Convert.FromBase64String(Message);

            // Step 5. Attempt to decrypt the string 
            try
            {
                ICryptoTransform Decryptor = TDESAlgorithm.CreateDecryptor();
                Results = Decryptor.TransformFinalBlock(DataToDecrypt, 0, DataToDecrypt.Length);
            }
            finally
            {
                // Clear the TripleDes and Hashprovider services of any sensitive information 
                TDESAlgorithm.Clear();
                HashProvider.Clear();
            }

            // Step 6. Return the decrypted string in UTF8 format 
            return UTF8.GetString(Results);
        }

        // URL-Safe encryption optimized for payment links
        // Uses AES-256 with URL-safe Base64 encoding (no padding issues)
        public static string EncryptForUrl(string plainText, string passphrase)
        {
            try
            {
                byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);

                using (Aes aes = Aes.Create())
                {
                    // Generate key from passphrase using PBKDF2 (more secure than MD5)
                    using (var keyDerivation = new Rfc2898DeriveBytes(passphrase, Encoding.UTF8.GetBytes("CabYaariSalt2024"), 10000))
                    {
                        aes.Key = keyDerivation.GetBytes(32); // 256-bit key
                        aes.IV = keyDerivation.GetBytes(16);  // 128-bit IV
                    }

                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        csEncrypt.Write(plainTextBytes, 0, plainTextBytes.Length);
                        csEncrypt.FlushFinalBlock();

                        byte[] encryptedBytes = msEncrypt.ToArray();

                        // Convert to URL-safe Base64 (replace +/= with -_~ for URL compatibility)
                        string base64 = Convert.ToBase64String(encryptedBytes);
                        return base64.Replace('+', '-').Replace('/', '_').Replace('=', '~');
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Encryption failed", ex);
            }
        }

        // URL-Safe decryption for payment links
        public static string DecryptFromUrl(string encryptedText, string passphrase)
        {
            try
            {
                // Convert back from URL-safe Base64
                string base64 = encryptedText.Replace('-', '+').Replace('_', '/').Replace('~', '=');

                // Add padding if needed
                int padding = 4 - (base64.Length % 4);
                if (padding != 4)
                {
                    base64 += new string('=', padding);
                }

                byte[] encryptedBytes = Convert.FromBase64String(base64);

                using (Aes aes = Aes.Create())
                {
                    // Generate same key and IV from passphrase
                    using (var keyDerivation = new Rfc2898DeriveBytes(passphrase, Encoding.UTF8.GetBytes("CabYaariSalt2024"), 10000))
                    {
                        aes.Key = keyDerivation.GetBytes(32); // 256-bit key
                        aes.IV = keyDerivation.GetBytes(16);  // 128-bit IV
                    }

                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(encryptedBytes))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Decryption failed", ex);
            }
        }

        // Short URL-safe ID generator for payment links (alternative approach)
        // Generates shorter, URL-friendly IDs using custom encoding
        public static string GenerateShortPaymentId(long bookingId, string secretKey)
        {
            try
            {
                // Add timestamp and random component for uniqueness
                long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                Random random = new Random();
                int randomComponent = random.Next(1000, 9999);

                // Combine booking ID, timestamp, and random component
                string data = $"{bookingId}|{timestamp}|{randomComponent}";

                // Create HMAC signature for integrity
                using (var hmac = new System.Security.Cryptography.HMACSHA256(Encoding.UTF8.GetBytes(secretKey)))
                {
                    byte[] dataBytes = Encoding.UTF8.GetBytes(data);
                    byte[] hashBytes = hmac.ComputeHash(dataBytes);

                    // Take first 8 bytes of hash for shorter ID
                    byte[] shortHash = new byte[8];
                    Array.Copy(hashBytes, shortHash, 8);

                    // Combine original data with short hash
                    byte[] combined = new byte[dataBytes.Length + shortHash.Length];
                    Array.Copy(dataBytes, combined, dataBytes.Length);
                    Array.Copy(shortHash, 0, combined, dataBytes.Length, shortHash.Length);

                    // Convert to URL-safe Base64
                    string base64 = Convert.ToBase64String(combined);
                    return base64.Replace('+', '-').Replace('/', '_').TrimEnd('=');
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Short ID generation failed", ex);
            }
        }

        // Decode short payment ID
        public static PaymentIdInfo DecodeShortPaymentId(string shortId, string secretKey)
        {
            try
            {
                // Convert back from URL-safe Base64
                string base64 = shortId.Replace('-', '+').Replace('_', '/');

                // Add padding if needed
                int padding = 4 - (base64.Length % 4);
                if (padding != 4)
                {
                    base64 += new string('=', padding);
                }

                byte[] combined = Convert.FromBase64String(base64);

                // Extract data and hash
                byte[] hashBytes = new byte[8];
                byte[] dataBytes = new byte[combined.Length - 8];

                Array.Copy(combined, dataBytes, dataBytes.Length);
                Array.Copy(combined, dataBytes.Length, hashBytes, 0, 8);

                // Verify HMAC signature
                using (var hmac = new System.Security.Cryptography.HMACSHA256(Encoding.UTF8.GetBytes(secretKey)))
                {
                    byte[] computedHash = hmac.ComputeHash(dataBytes);
                    byte[] computedShortHash = new byte[8];
                    Array.Copy(computedHash, computedShortHash, 8);

                    // Compare hashes
                    for (int i = 0; i < 8; i++)
                    {
                        if (hashBytes[i] != computedShortHash[i])
                        {
                            throw new InvalidOperationException("Invalid payment ID signature");
                        }
                    }
                }

                // Parse data
                string data = Encoding.UTF8.GetString(dataBytes);
                string[] parts = data.Split('|');

                if (parts.Length != 3)
                {
                    throw new InvalidOperationException("Invalid payment ID format");
                }

                return new PaymentIdInfo
                {
                    BookingId = long.Parse(parts[0]),
                    Timestamp = long.Parse(parts[1]),
                    RandomComponent = int.Parse(parts[2])
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Short ID decoding failed", ex);
            }
        }
    }

    // Helper class for payment ID information
    public class PaymentIdInfo
    {
        public long BookingId { get; set; }
        public long Timestamp { get; set; }
        public int RandomComponent { get; set; }

        public DateTime CreatedAt => DateTimeOffset.FromUnixTimeSeconds(Timestamp).DateTime;

        // Check if the payment ID is expired (optional security feature)
        public bool IsExpired(int expirationHours = 24)
        {
            return DateTime.UtcNow > CreatedAt.AddHours(expirationHours);
        }
    }
}
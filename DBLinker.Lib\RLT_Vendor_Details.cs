//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_Vendor_Details
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public RLT_Vendor_Details()
        {
            this.RLT_VENDOR_CAR_DETAILS = new HashSet<RLT_VENDOR_CAR_DETAILS>();
            this.RLT_Vendor_Docs = new HashSet<RLT_Vendor_Docs>();
        }
    
        public int PKID { get; set; }
        public string Vendor_Company_Name { get; set; }
        public string Vendor_Owner_Name { get; set; }
        public string Vendor_Photo { get; set; }
        public string Vendor_EmailID { get; set; }
        public string Vendor_Phone1 { get; set; }
        public string Vendor_Phone2 { get; set; }
        public string Phone1_IsWhatsup { get; set; }
        public string Vendor_Address { get; set; }
        public string Vendor_Member_Id { get; set; }
        public string Vendor_LoginID { get; set; }
        public string Vendor_Pwd { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<bool> Is_2FA_Activated { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Last_Modified_By { get; set; }
        public Nullable<int> CreatedBy { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RLT_VENDOR_CAR_DETAILS> RLT_VENDOR_CAR_DETAILS { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RLT_Vendor_Docs> RLT_Vendor_Docs { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_City
    {       

        public int PKID { get; set; }
        public Nullable<System.Guid> PK_GUID { get; set; }
        [Remote("Check_City_Name", "Common", AdditionalFields = "PKID", ErrorMessage = "City Name already exists!")]
        public string City_Name { get; set; }
        public string City_Abbr { get; set; }
        public string latitude { get; set; }
        public string longitude { get; set; }
        public string eLoc { get; set; }
        public Nullable<int> orderIndex { get; set; }
        public string score { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> State_PKID { get; set; }

    }
}

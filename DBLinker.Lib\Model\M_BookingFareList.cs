﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DBLinker.Lib.Model
{
    public class M_BookingFareList
    {
        public int PKID { get; set; }

        public Nullable<int> City_From_Id { get; set; }
        public Nullable<int> City_To_Id { get; set; }
        public Nullable<int> Trip_Type_Id { get; set; } 
        public Nullable<int> Car_Category_Id { get; set; }
       

        public string City_From { get; set; }
        public string City_To { get; set; }
        public string Trip_Type { get; set; }
        public string Car_Category { get; set; }
        public decimal? Basic_Fare { get; set; }
        public decimal? Driver_Charge { get; set; }
        public decimal? Toll_Charge { get; set; }
        public decimal? Total_Fare { get; set; }
      
        public decimal? Final_Fare { get; set; }
        public decimal? GST { get; set; }
        public decimal? GST_Amount { get; set; }
        public string Remark { get; set; }
        public bool? Is_Active { get; set; }
    }
}

﻿using DBLinker.Lib;
using DBLinker.Lib.Model;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Microsoft.Reporting.WebForms;

namespace AdminApp
{
    public partial class CommonRdlcReport : System.Web.UI.Page
    {

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                if (Request.QueryString["ReportID"] != null)
                {
                    if (Request.QueryString["ReportID"].ToString() == "1")// Booking Report
                    {
                        GetBookingReportData();
                    }
                    else if (Request.QueryString["ReportID"].ToString() == "2")// 2 Get Booking Invoice Report Data
                    {
                        GetBookingInvoiceReportData();
                    }
                }
            }
        }

        public void GetBookingReportData() // 1 Get Booking Report Data
        {
            M_Report _eReport = new M_Report();
            _eReport.Flag = 1;
            if (Request.QueryString["CityF"] != null)
                _eReport.CityFrom = Convert.ToInt16(Request.QueryString["CityF"]);
            if (Request.QueryString["CityT"] != null)
                _eReport.CityTo = Convert.ToInt16(Request.QueryString["CityT"]);
            if (Request.QueryString["FromD"] != null && Request.QueryString["ToD"] != null)
            {
                _eReport.FromDate = Convert.ToString(Request.QueryString["FromD"]);
                _eReport.ToDate = Convert.ToString(Request.QueryString["ToD"]);
            }
            if (Request.QueryString["BookingStatusId"] != null)
                _eReport.BookingStatusId = Convert.ToInt16(Request.QueryString["BookingStatusId"]);
            if (Request.QueryString["BookingId"] != null)
                _eReport.BookingId = Convert.ToString(Request.QueryString["BookingId"]);
            if (Request.QueryString["CustomerName"] != null)
                _eReport.CustomerName = Convert.ToString(Request.QueryString["CustomerName"]);
            if (Request.QueryString["CustomerMobile"] != null)
                _eReport.CustomerMobile = Convert.ToString(Request.QueryString["CustomerMobile"]);


            DataSet ds = new DataSet();
            ds = SP_GET_REPORT_DATA(_eReport);
            rvList.ProcessingMode = ProcessingMode.Local;
            LocalReport localReport = rvList.LocalReport;
            rvList.ShowPrintButton = true;
            rvList.LocalReport.ReportPath = Server.MapPath("~/Report/rpt_BookingReport.rdlc");
            ReportDataSource rds = new ReportDataSource();
            rds.Name = "ds_BookingReport";
            rds.Value = ds.Tables[0];
            rvList.LocalReport.DataSources.Clear();
            rvList.LocalReport.DataSources.Add(rds);
            rvList.DataBind();

        }

        public void GetBookingInvoiceReportData() // 2 Get Booking Invoice Report Data
        {
            M_Report _eReport = new M_Report();
            _eReport.Flag = 2;
           
            if (Request.QueryString["BookingId"] != null)
                _eReport.BookingId = Convert.ToString(Request.QueryString["BookingId"]);
           

            DataSet ds = new DataSet();
            ds = SP_GET_REPORT_DATA(_eReport);
            string strBasePath = "~/Docs/DriverPhoto/";
            strBasePath = new Uri(Server.MapPath(strBasePath)).AbsoluteUri;
            ReportParameter paramHD1 = new ReportParameter("ImagePath", strBasePath);
            rvList.ProcessingMode = ProcessingMode.Local;
            LocalReport localReport = rvList.LocalReport;
            rvList.ShowPrintButton = true;
             this.rvList.LocalReport.EnableExternalImages = true;
            rvList.LocalReport.ReportPath = Server.MapPath("Report/rpt_BookingInvoiceReport.rdlc");
            ReportDataSource rds = new ReportDataSource();
            rds.Name = "ds_Invoice";
            rds.Value = ds.Tables[0];
            rvList.LocalReport.DataSources.Clear();
            rvList.LocalReport.SetParameters(paramHD1);
            rvList.LocalReport.DataSources.Add(rds);
            rvList.DataBind();

        }

        public DataSet SP_GET_REPORT_DATA(M_Report _eReport)
        {
            DataSet ds = new DataSet();
            string strConn = ConfigurationManager.ConnectionStrings["RLTDBconnection"].ConnectionString;

            SqlConnection objConn = new SqlConnection(strConn);
            try
            {
                SqlParameter[] parameter =
                {
                   new SqlParameter("@Flag",_eReport.Flag),
                   new SqlParameter("@CityFrom",_eReport.CityFrom == 0 ? (object)DBNull.Value : _eReport.CityFrom),
                   new SqlParameter("@CityTo",_eReport.CityTo == 0 ? (object)DBNull.Value : _eReport.CityTo),
                   new SqlParameter("@FromDate", _eReport.FromDate == null?(object)DBNull.Value:Convert.ToDateTime(_eReport.FromDate)),
                   new SqlParameter("@ToDate", _eReport.ToDate == null?(object)DBNull.Value:Convert.ToDateTime(_eReport.ToDate)),
                   new SqlParameter("@BookingStatusId", _eReport.BookingStatusId == 0 ? (object)DBNull.Value : _eReport.BookingStatusId),
                   new SqlParameter("@BookingId", _eReport.BookingId),
                    new SqlParameter("@CustomerName", _eReport.CustomerName),
                     new SqlParameter("@CustomerMobile", _eReport.CustomerMobile)
                };
                ds = SqlHelper.ExecuteDataset(objConn.ConnectionString, CommandType.StoredProcedure, "SP_GET_REPORT_DATA", parameter);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            return ds;
        }
    }
}
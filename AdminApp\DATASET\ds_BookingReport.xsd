﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ds_BookingReport" targetNamespace="http://tempuri.org/ds_BookingReport.xsd" xmlns:mstns="http://tempuri.org/ds_BookingReport.xsd" xmlns="http://tempuri.org/ds_BookingReport.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ds_BookingReport" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="ds_BookingReport" msprop:Generator_UserDSName="ds_BookingReport">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="dt_Booking" msprop:Generator_TableClassName="dt_BookingDataTable" msprop:Generator_TableVarName="tabledt_Booking" msprop:Generator_RowChangedName="dt_BookingRowChanged" msprop:Generator_TablePropName="dt_Booking" msprop:Generator_RowDeletingName="dt_BookingRowDeleting" msprop:Generator_RowChangingName="dt_BookingRowChanging" msprop:Generator_RowEvHandlerName="dt_BookingRowChangeEventHandler" msprop:Generator_RowDeletedName="dt_BookingRowDeleted" msprop:Generator_RowClassName="dt_BookingRow" msprop:Generator_UserTableName="dt_Booking" msprop:Generator_RowEvArgName="dt_BookingRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Booking_Id" msprop:Generator_ColumnVarNameInTable="columnBooking_Id" msprop:Generator_ColumnPropNameInRow="Booking_Id" msprop:Generator_ColumnPropNameInTable="Booking_IdColumn" msprop:Generator_UserColumnName="Booking_Id" type="xs:string" minOccurs="0" />
              <xs:element name="City_From" msprop:Generator_ColumnVarNameInTable="columnCity_From" msprop:Generator_ColumnPropNameInRow="City_From" msprop:Generator_ColumnPropNameInTable="City_FromColumn" msprop:Generator_UserColumnName="City_From" type="xs:string" minOccurs="0" />
              <xs:element name="City_To" msprop:Generator_ColumnVarNameInTable="columnCity_To" msprop:Generator_ColumnPropNameInRow="City_To" msprop:Generator_ColumnPropNameInTable="City_ToColumn" msprop:Generator_UserColumnName="City_To" type="xs:string" minOccurs="0" />
              <xs:element name="Trip_Type" msprop:Generator_ColumnVarNameInTable="columnTrip_Type" msprop:Generator_ColumnPropNameInRow="Trip_Type" msprop:Generator_ColumnPropNameInTable="Trip_TypeColumn" msprop:Generator_UserColumnName="Trip_Type" type="xs:string" minOccurs="0" />
              <xs:element name="Car_Category" msprop:Generator_ColumnVarNameInTable="columnCar_Category" msprop:Generator_ColumnPropNameInRow="Car_Category" msprop:Generator_ColumnPropNameInTable="Car_CategoryColumn" msprop:Generator_UserColumnName="Car_Category" type="xs:string" minOccurs="0" />
              <xs:element name="Fare" msprop:Generator_ColumnVarNameInTable="columnFare" msprop:Generator_ColumnPropNameInRow="Fare" msprop:Generator_ColumnPropNameInTable="FareColumn" msprop:Generator_UserColumnName="Fare" type="xs:string" minOccurs="0" />
              <xs:element name="GST_Fare" msprop:Generator_ColumnVarNameInTable="columnGST_Fare" msprop:Generator_ColumnPropNameInRow="GST_Fare" msprop:Generator_ColumnPropNameInTable="GST_FareColumn" msprop:Generator_UserColumnName="GST_Fare" type="xs:string" minOccurs="0" />
              <xs:element name="Booking_Date" msprop:Generator_ColumnVarNameInTable="columnBooking_Date" msprop:Generator_ColumnPropNameInRow="Booking_Date" msprop:Generator_ColumnPropNameInTable="Booking_DateColumn" msprop:Generator_UserColumnName="Booking_Date" type="xs:string" minOccurs="0" />
              <xs:element name="PickUp_Address" msprop:Generator_ColumnVarNameInTable="columnPickUp_Address" msprop:Generator_ColumnPropNameInRow="PickUp_Address" msprop:Generator_ColumnPropNameInTable="PickUp_AddressColumn" msprop:Generator_UserColumnName="PickUp_Address" type="xs:string" minOccurs="0" />
              <xs:element name="DropOff_Address" msprop:Generator_ColumnVarNameInTable="columnDropOff_Address" msprop:Generator_ColumnPropNameInRow="DropOff_Address" msprop:Generator_ColumnPropNameInTable="DropOff_AddressColumn" msprop:Generator_UserColumnName="DropOff_Address" type="xs:string" minOccurs="0" />
              <xs:element name="PickUp_Date" msprop:Generator_ColumnVarNameInTable="columnPickUp_Date" msprop:Generator_ColumnPropNameInRow="PickUp_Date" msprop:Generator_ColumnPropNameInTable="PickUp_DateColumn" msprop:Generator_UserColumnName="PickUp_Date" type="xs:string" minOccurs="0" />
              <xs:element name="PickUp_Time" msprop:Generator_ColumnVarNameInTable="columnPickUp_Time" msprop:Generator_ColumnPropNameInRow="PickUp_Time" msprop:Generator_ColumnPropNameInTable="PickUp_TimeColumn" msprop:Generator_UserColumnName="PickUp_Time" type="xs:string" minOccurs="0" />
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" type="xs:string" minOccurs="0" />
              <xs:element name="Mobile_No1" msprop:Generator_ColumnVarNameInTable="columnMobile_No1" msprop:Generator_ColumnPropNameInRow="Mobile_No1" msprop:Generator_ColumnPropNameInTable="Mobile_No1Column" msprop:Generator_UserColumnName="Mobile_No1" type="xs:string" minOccurs="0" />
              <xs:element name="Mobile_No2" msprop:Generator_ColumnVarNameInTable="columnMobile_No2" msprop:Generator_ColumnPropNameInRow="Mobile_No2" msprop:Generator_ColumnPropNameInTable="Mobile_No2Column" msprop:Generator_UserColumnName="Mobile_No2" type="xs:string" minOccurs="0" />
              <xs:element name="Mail_Id" msprop:Generator_ColumnVarNameInTable="columnMail_Id" msprop:Generator_ColumnPropNameInRow="Mail_Id" msprop:Generator_ColumnPropNameInTable="Mail_IdColumn" msprop:Generator_UserColumnName="Mail_Id" type="xs:string" minOccurs="0" />
              <xs:element name="Payment_Method_Name" msprop:Generator_ColumnVarNameInTable="columnPayment_Method_Name" msprop:Generator_ColumnPropNameInRow="Payment_Method_Name" msprop:Generator_ColumnPropNameInTable="Payment_Method_NameColumn" msprop:Generator_UserColumnName="Payment_Method_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Vendor_Company_Name" msprop:Generator_ColumnVarNameInTable="columnVendor_Company_Name" msprop:Generator_ColumnPropNameInRow="Vendor_Company_Name" msprop:Generator_ColumnPropNameInTable="Vendor_Company_NameColumn" msprop:Generator_UserColumnName="Vendor_Company_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Car_Number" msprop:Generator_ColumnVarNameInTable="columnCar_Number" msprop:Generator_ColumnPropNameInRow="Car_Number" msprop:Generator_ColumnPropNameInTable="Car_NumberColumn" msprop:Generator_UserColumnName="Car_Number" type="xs:string" minOccurs="0" />
              <xs:element name="BOOKING_STATUS" msprop:Generator_ColumnVarNameInTable="columnBOOKING_STATUS" msprop:Generator_ColumnPropNameInRow="BOOKING_STATUS" msprop:Generator_ColumnPropNameInTable="BOOKING_STATUSColumn" msprop:Generator_UserColumnName="BOOKING_STATUS" type="xs:string" minOccurs="0" />
              <xs:element name="Booking_Remark" msprop:Generator_ColumnVarNameInTable="columnBooking_Remark" msprop:Generator_ColumnPropNameInRow="Booking_Remark" msprop:Generator_ColumnPropNameInTable="Booking_RemarkColumn" msprop:Generator_UserColumnName="Booking_Remark" type="xs:string" minOccurs="0" />
              <xs:element name="Driver_Name" msprop:Generator_ColumnVarNameInTable="columnDriver_Name" msprop:Generator_ColumnPropNameInRow="Driver_Name" msprop:Generator_ColumnPropNameInTable="Driver_NameColumn" msprop:Generator_UserColumnName="Driver_Name" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>
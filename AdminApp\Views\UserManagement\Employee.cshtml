﻿@model DBLinker.Lib.Model.M_Employee

@{
    ViewBag.Title = "Employee Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title" style="font-size:20px;">
                        Employee Manager <span > @(Model.PKID == 0 ? "add more information of "  : "edit more information of ") @Model.Employee_Name </span>
                    </div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add Employee</li>
                </ol>
            </div>
        </div>




        @using (Html.BeginForm("Employee", "UserManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormEmployee" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(x => x.PKID)
            <div class="row">
                <div class="col-sm-12">
                    <div class="card-box">

                        <section style="background-color: #fff; width: 80%">
                            <ul class="tab-nav tn-justified tn-icon" id="EmployeeTab" tabindex="0" style="overflow: hidden; outline: none;">
                                <li class="active">
                                    <a class="col-sx-4" href="#" id="LnkPersonal">Personal Information </a>
                                </li>
                                <li>
                                    <a class="col-sx-4" href="#" id="LnkJob">Job Information</a>

                                </li>
                                <li>
                                    <a class="col-sx-4" href="#" id="LnkEducation">Education Information</a>

                                </li>
                                <li>
                                    <a class="col-sx-4" href="#" id="LnkContact">Contact Information</a>

                                </li>

                            </ul>
                        </section>

                        <div class="card-body row" id="divPersonal">

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Gender, "Male", new { @id = "Gender1" })
                                            <label for="radio1"><span><span></span></span> Male</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Gender, "Female", new { @id = "Gender2" })
                                            <label for="radio2"><span><span></span></span>Female</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Gender</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Marrital_Status, "Single", new { @id = "Marrital_Status1" })
                                            <label for="radio1"><span><span></span></span> Single</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Marrital_Status, "Married", new { @id = "Marrital_Status2" })
                                            <label for="radio2"><span><span></span></span>Married</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Marrital Status</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Spouses_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label"><span>Spouses Name</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.DOB, "{0:yyyy-MM-dd}", new { @class = "mdl-textfield__input", type = "date" })
                                    <label class="mdl-textfield__label required"><span>DOB</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Phone_2, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label">Alternate Mobile No</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Father_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label "><span>Father Name</span></label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Mother_Name, new { @class = "mdl-textfield__input ", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Mother Name</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body row" id="divJob">
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Employee_ID, new { @class = "mdl-textfield__input", @maxlength = "10" })
                                    <label class="mdl-textfield__label"><span>Employee ID</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Designation, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Designation</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Department, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Department</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Supervisor, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Supervisor</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Work_Phone, new { @class = "mdl-textfield__input txtbx", @maxlength = "20" })
                                    <label class="mdl-textfield__label">Office Phone No</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Work_Email, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Office Email Id</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Work_StartDate, new { @class = "mdl-textfield__input", type = "date" })
                                    <label class="mdl-textfield__label"><span>Start Date</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Work_City, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Work City</span></label>
                                </div>
                            </div>
                        </div>

                        <div class="card-body row" id="divEducation">
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">10th(Secondary) </span>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C10th_School, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label"> School Name</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C10th_PassingYear, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label">Passing Year</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C10th_Percentage, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label"> Percentage</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">12th(Senior Secondary) </span>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C12th_School, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label"> School Name</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C12th_PassingYear, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label">PassingYear</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.C12th_Percentage, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label"> Percentage</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Degree </span>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Degree_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label"> Degree Name</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Degree_College, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">College</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Degree_PassingYear, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label"> PassingYear</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Degree_Percentage, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label"> Percentage</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Master Degree </span>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Master_Degree_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">Master Degree Name</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Master_Degree_College, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label">College</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Master_Degree_PassingYear, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label"> PassingYear</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Master_Degree_Percentage, new { @class = "mdl-textfield__input Decimaltxtbx", @maxlength = "6" })
                                    <label class="mdl-textfield__label"> Percentage</label>
                                </div>
                            </div>
                        </div>

                        <div class="card-body row" id="divContact">
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Current Address </span>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Current_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label "><span>Address 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Current_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label "><span>Address 2</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.StateList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Current_Address_State, new SelectList(ViewBag.StateList, "PKID", "State_Name"), "-- select State --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label"><span>State</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.Current_Address_City, new SelectList(string.Empty, "Value", "Text"), "-- select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    @Html.HiddenFor(x => x.Current_Address_City, new { @id = "hf_Current_Address_City" })
                                    <label class="mdl-textfield__label"><span>City</span></label>
                                </div>
                            </div>



                            <div class="col-lg-12 p-t-20">
                                <div style="float:left;width:34%">
                                    <span style="font-size:14px;">Permanent Address Same as Current Address ? </span>

                                </div>
                                <div style="float:left;width:7%">
                                    @Html.RadioButtonFor(x => x.Current_Permanent_Same, true, new { @id = "Current_Permanent_Same1" })
                                    <label for="radio1"><span><span></span></span> Yes</label>
                                </div>
                                <div style="float:left;width:10%">
                                    @Html.RadioButtonFor(x => x.Current_Permanent_Same, false, new { @id = "Current_Permanent_Same2" })
                                    <label for="radio2"><span><span></span></span> No</label>
                                </div>
                                <div style="clear:both;"></div>

                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Permanent Address </span>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Permanent_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label"><span>Address 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Permanent_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label"><span>Address 2</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.StateList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Permanent_Address_State, new SelectList(ViewBag.StateList, "PKID", "State_Name"), "-- select State --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label"><span>State</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.Permanent_Address_City, new SelectList(string.Empty, "Value", "Text"), "-- select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    @Html.HiddenFor(x => x.Permanent_Address_City, new { @id = "hf_Permanent_Address_City" })
                                    <label class="mdl-textfield__label"><span>City</span></label>
                                </div>
                            </div>

                            <div class="col-lg-12 p-t-20 text-center">
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Submit</button>

                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">Cancel</button>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        }

    </div>
</div>
<script src="~/Scripts/CrudFile/Employee.js"></script>

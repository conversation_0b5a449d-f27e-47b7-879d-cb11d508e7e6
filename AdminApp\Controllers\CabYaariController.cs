﻿using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DBLinker.Lib;
using AdminApp.Services;
using System.Collections.Generic;

namespace AdminApp.Controllers
{
    [RoleBaseAuthentication]
    public class CabYaariController : Controller
    {
        private RLTDBContext entity;

        // GET: CabYaariConfigurations
        public CabYaariController()
        {
            this.entity = new RLTDBContext();
            
        }

         

        #region Cab Yaari  Most Favourite Routes   
        public ActionResult Home()
        {
            ViewBag.CityList = entity.RLT_CITY.Where(x => x.Is_Active == true).OrderBy(x => x.City_Name).ToList();
            ViewBag.CarCategory = entity.RLT_CAR_CATEGORY.Where(x => x.Is_Active == true).ToList().Select(c => new { PKID = c.PKID, Car_Category = c.Car_Category_Abbr + " ( " + c.Per_KM_fare + " RS Per KM )" });
            ViewBag.TripTypeList = entity.RLT_TRIP_TYPES.Where(x => x.Is_Active == true).ToList();

            return View(entity.RLTMostFavoriteRoutes.ToList());
        }

        [HttpPost]
        [RoleBaseAuthentication("Home", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult Home(RLTMostFavoriteRoute data, HttpPostedFileBase City_Image)
        {
            if (City_Image != null)
            {
                string path = Guid.NewGuid() + "_" + City_Image.FileName;
                string Savepath = Server.MapPath("~/Docs/MostFavouriteCityImg/") + path;
                City_Image.SaveAs(Savepath);
                data.CityImage = path;
            }
            data.Facilities = entity.RLT_CAR_CATEGORY.Where(x => x.PKID == data.CarCategoryID).Select(x => x.Features).FirstOrDefault();
            data.CreatedDate = DateTime.Now; 
            data.CreatedBy = UserServices.getCurrentUserId(); 
            entity.RLTMostFavoriteRoutes.Add(data);
            bool result = entity.SaveChanges() > 0 ? true : false;
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("Home", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveMostFavouriteRoutes(int id = 0)
        {
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLTMostFavoriteRoutes.Where(x => x.ID == id).FirstOrDefault();
                if (data != null)
                {
                    data.UpdatedBy = UserServices.getCurrentUserId();
                    data.UpdatedDate = DateTime.Now;
                    data.IsActive = data.IsActive != null ? data.IsActive == true ? false : true : false;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("Home", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditMostFavouriteRoute(int id = 0)
        {
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLTMostFavoriteRoutes.Where(x => x.ID == id).FirstOrDefault();
                if (data != null)
                {
                    return Json(data, JsonRequestBehavior.AllowGet);
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("Home", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditMostFavouriteRoute(RLTMostFavoriteRoute data, HttpPostedFileBase City_Image)
        {
            bool result = false;
            if (data.ID > 0)
            {
                var mostFavoriteRoute = entity.RLTMostFavoriteRoutes.Where(x => x.ID == data.ID).FirstOrDefault();
                if (mostFavoriteRoute != null)
                {
                    if (City_Image != null)
                    {
                        string path = Guid.NewGuid() + "_" + City_Image.FileName;
                        string Savepath = Server.MapPath("~/Docs/MostFavouriteCityImg/") + path;
                        City_Image.SaveAs(Savepath);
                        mostFavoriteRoute.CityImage = path;
                    }
                    mostFavoriteRoute.UpdatedBy = UserServices.getCurrentUserId();
                    mostFavoriteRoute.UpdatedDate = DateTime.Now;
                    mostFavoriteRoute.PickUpCityID = data.PickUpCityID;
                    mostFavoriteRoute.DropOffCityID = data.DropOffCityID;
                    mostFavoriteRoute.TripType = data.TripType;
                    mostFavoriteRoute.Fare = data.Fare;
                    mostFavoriteRoute.GSTFare = data.GSTFare;
                    mostFavoriteRoute.Distance = data.Distance;
                    mostFavoriteRoute.TravelDuration = data.TravelDuration;
                    mostFavoriteRoute.IsActive = data.IsActive;
                    mostFavoriteRoute.Facilities = entity.RLT_CAR_CATEGORY.Where(x => x.PKID == data.CarCategoryID).Select(x => x.Features).FirstOrDefault();
                    mostFavoriteRoute.UpdatedDate = DateTime.Now;
                    mostFavoriteRoute.UpdatedBy = 0;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("Home", RoleBaseAuthentication.ActionType.View)]
        public JsonResult ValidateDublicateRecord(int fromCity, int toCity, int tripType, int carCategory, int id, bool isUpdate)
        {
            int count = -1;
            var data = entity.RLTMostFavoriteRoutes.Where(x => x.PickUpCityID == fromCity && x.DropOffCityID == toCity && x.TripType == tripType && x.CarCategoryID == carCategory).ToList();

            if (data.Count() == 0) return Json(count = 0, JsonRequestBehavior.AllowGet);

            if (isUpdate && data.Count() == 1)
            {
                if (id > 0)
                {
                    foreach (var item in data)
                    {
                        if (item.ID == id) return Json(count = 0, JsonRequestBehavior.AllowGet);
                        else return Json(count = 1, JsonRequestBehavior.AllowGet);
                    }
                }
            }
            return Json(count = data.Count(), JsonRequestBehavior.AllowGet);

        }

        #endregion Cab Yaari  Most Favourite Routes
        #region Cab Yaari  Services

        [HttpGet]
        public ActionResult Services()
        {
            return View(entity.RLT_Services.ToList());
        }

        [HttpPost]
        [RoleBaseAuthentication("Services", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult Services(RLT_Services data, HttpPostedFileBase ServiceImagePath)
        {
            if (ServiceImagePath != null)
            {
                string path = Guid.NewGuid() + "_" + ServiceImagePath.FileName;
                if (path.Length > 49)
                    path = path.Remove(37, (path.Length - 49));
                string Savepath = Server.MapPath("~/Docs/CabYaariServices/") + path;
                ServiceImagePath.SaveAs(Savepath);
                data.ServiceImagePath = path;
            }
            data.CreatedBy = UserServices.getCurrentUserId();
            data.CreatedDate = DateTime.Now;
            entity.RLT_Services.Add(data);
            bool result = entity.SaveChanges() > 0 ? true : false;
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("Services", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditServices(int id = 0)
        { 
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLT_Services.Where(x => x.PKID == id).FirstOrDefault();
                if (data != null)
                {
                    return Json(data, JsonRequestBehavior.AllowGet);
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("Services", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditServices(RLT_Services data, HttpPostedFileBase ServiceImagePath)
        {
            bool result = false;
            if (data.PKID > 0)
            {
                var service = entity.RLT_Services.Where(x => x.PKID == data.PKID).FirstOrDefault();
                if (service != null)
                {
                    if (ServiceImagePath != null)
                    {
                        string path = Guid.NewGuid() + "_" + ServiceImagePath.FileName;
                        if (path.Length > 49)
                            path = path.Remove(37, (path.Length - 49));
                        string Savepath = Server.MapPath("~/Docs/CabYaariServices/") + path;
                        ServiceImagePath.SaveAs(Savepath);
                        service.ServiceImagePath = path;
                    }
                    service.UpdatedDate = DateTime.Now;
                    service.UpdatedBy = UserServices.getCurrentUserId();
                    service.ServiceName = data.ServiceName;
                    service.ServiceIconClass = data.ServiceIconClass;
                    service.BriefIntroAboutService = data.BriefIntroAboutService;
                    service.AboutService = data.AboutService;
                    service.IsActive = data.IsActive;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("Services", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveServices(int id = 0)
        {
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLT_Services.Where(x => x.PKID == id).FirstOrDefault();
                if (data != null)
                {
                    data.UpdatedDate = DateTime.Now;
                    data.UpdatedBy = UserServices.getCurrentUserId();
                    data.IsActive = data.IsActive != null ? data.IsActive == true ? false : true : false;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion Cab Yaari  Services
 
        

    }
}
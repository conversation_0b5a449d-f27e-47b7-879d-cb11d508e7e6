﻿$(document).ready(function () {
   

    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


  
});



function ActivateDeactivate(Id)
{
    $("#ID").val('');
    $("#ContactPersonName").val($("#name_" + Id).val());
    $("#ContactPersonEmailID").val($("#email_" + Id).val());
    $("#ContactPersonPhone").val($("#phone_" + Id).val());
    $("#ContactPersonSubject").val($("#subject_" + Id).val());
    $("#ContactPersonMessage").val($("#message_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());

    $("#ID").val(Id);

    if ($("#status_" + Id).val() == false)
    {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("#ID").val() != "") {

        $.ajax({
            url: 'ContactManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}



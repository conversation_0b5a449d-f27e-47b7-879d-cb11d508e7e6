//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_CAR_CHARGES_FECILITIES_DETAILS
    {
        public int PKID { get; set; }
        public Nullable<int> Car_Category_PKID { get; set; }
        public Nullable<double> Car_Charges_PKM { get; set; }
        public Nullable<double> Car_Waiting_Charges_PM { get; set; }
        public Nullable<double> Car_Driver_Charges_PD { get; set; }
        public Nullable<bool> Is_Contain_AC { get; set; }
        public string Total_Number_Seats { get; set; }
        public Nullable<bool> Is_Smoking_Allow { get; set; }
        public Nullable<bool> Is_Luggage_Allow { get; set; }
        public Nullable<bool> Is_Pet_Allow { get; set; }
        public string Inclusive_Charges_Comment { get; set; }
        public string Extras_Charges_Comment { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> CreatedBy { get; set; }
        public Nullable<int> UpdatedBy { get; set; }
    
        public virtual RLT_CAR_CATEGORY RLT_CAR_CATEGORY { get; set; }
    }
}

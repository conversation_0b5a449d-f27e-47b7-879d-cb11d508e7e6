﻿$(document).ready(function () {

    $("#CarOwnerManager").parent().parent().find("a").next().slideToggle(500);
    $("#CarOwnerManager").parent().parent().toggleClass('toggled');
    $("#CarOwnerManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();


    $("#btnAdd").click(function () {
        $("#hdnOperation").val("A");
        $("#Is_Active").val(true);
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });
     
    $("#btnSave").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormOwnerAdd")[0]);
            $.ajax({
                url: 'AddCarOwner',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Owner Informations Successfully Saved!", "Car Owner Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) { 
            var formData = new FormData($("#idFormOwnerAdd")[0]);
            $.ajax({
                url: 'CarOwnerEdit',
                type: "POST", 
                data: formData,
                contentType: false,
                processData: false, 
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Owner Informations Successfully Updated!", "Car Owner Update");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error"); 
                    }
                },
                failure: function (response) { 
                },
                error: function (response) {
                    alert(response.responseText);
                }
            }); 
        }
    });

    $("#btnReset").click(function () { 
        $("#hdnOperation").val("")
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
});

function Edit(Id) {
    $.ajax({
        url: 'CarOwnerEdit',
        type: "GET",
        data: { pkid: Id },
        dataType: "json",
        success: function (data) {
            if (data != null) {
                // fill data 
                $("#Vendor_PKID").val(data.Vendor_PKID);
                $("#PKID").val(data.PKID);
                $("#Is_Active").val(data.Is_Active);
                $("#Car_Owner_Name").val(data.Car_Owner_Name);
                $("#Car_Owner_Email").val(data.Car_Owner_Email);
                $("#Car_Owner_Photo").val(data.Car_Owner_Photo);
                $("#Car_Owner_Photo_View").fadeIn("fast").attr('src', data.Car_Owner_Photo != null ? "/Docs/CarOwnerPhoto/" + data.Car_Owner_Photo : "/Content/img/no-user-image.gif");
                $("#Car_Owner_Phone1").val(data.Car_Owner_Phone1);
                $("#Car_Owner_Phone2").val(data.Car_Owner_Phone2);
                $("#Car_Owner_DL").val(data.Car_Owner_DL);
                $("#Car_Owner_Aadhaar").val(data.Car_Owner_Aadhaar);
                $("#Car_Owner_Address").val(data.Car_Owner_Address);
                data.Phone1_IsWhatsup.toLowerCase() == "yes" ? $("#Phone1_IsWhatsup1").prop("checked", true) : $("#Phone1_IsWhatsup2").prop("checked", true)
                //focus 
                $("#Car_Category_Name").focus();
                // set default values
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}


function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Vendor_PKID").val($("#Vendor_PKID_" + Id).val());
    $("#Car_Owner_Name").val($("#Car_Owner_Name_" + Id).val());
    $("#Car_Owner_Email").val($("#Car_Owner_Email_" + Id).val());
    $("#Car_Owner_Photo_EditFile").val($("#Car_Owner_Photo_" + Id).val());
    $("#Car_Owner_Photo_View").fadeIn("fast").attr('src', "/Docs/CarOwnerPhoto/" + $("#Driver_Photo_" + Id).val());
    $("#Car_Owner_Phone1").val($("#Car_Owner_Phone1_" + Id).val());
    $("#Car_Owner_Phone2").val($("#Car_Owner_Phone2_" + Id).val());
    $("#Car_Owner_DL").val($("#Car_Owner_DL_" + Id).val());
    $("#Car_Owner_Aadhaar").val($("#Car_Owner_Aadhaar_" + Id).val());
    $("#Car_Owner_Address").val($("#Car_Owner_Address_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {
        $.ajax({
            url: 'AddCarOwner',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            contentType: false,
            enctype: 'multipart/form-data',
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Owner Successfully Deleted!", "Car Owner Deleted");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

 

function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'CarOwnerActiveDeactive',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Car Owner Status Successfully Updated!", "Car Owner Status");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                toastr.error(response.responseText, "Error"); 
            }
        });
    }
}

function resetForm() {

    $("#Vendor_PKID").val('');
    $("#Car_Owner_Name").val('');
    $("#Car_Owner_Email").val('');
    $("#Car_Owner_Phone1").val('');
    $("#Car_Owner_Aadhaar").val('');
    $("#Car_Owner_Address").val('');

}

function IsValidate() {
    if ($("#Vendor_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select vendor name!", "Required!");
        return false;
    }
    if ($("#Car_Owner_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Owner name !", "Required!");
        return false;
    }
    
    if ($("#hdnOperation").val() == "A" || $("#hdnOperation").val() == "") {
        if ($("#CarOwnerPhoto").val() == "") {
            toastr.clear();
            toastr.error("Please upload Photo !", "Required!");
            return false;
        }
    } 
    if ($("#Car_Owner_Phone1").val() == "") {
        toastr.clear();
        toastr.error("Please enter Phone no !");
        return false;
    }
    if ($("#Car_Owner_Aadhaar").val() == "") {
        toastr.clear();
        toastr.error("Please enter Aadhaar !", "Required!");
        return false;
    }
    if ($("#Car_Owner_Address").val() == "") {
        toastr.clear();
        toastr.error("Please enter Address !", "Required!");
        return false;
    }

    return true;
}




$("#CarOwnerPhoto").change(function (event) {
    $("#Car_Owner_Photo_View").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
});










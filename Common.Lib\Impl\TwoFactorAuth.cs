﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Lib.Model;
using Google.Authenticator;

namespace Common.Lib.Impl
{
    public class TwoFactorAuth : ITwoFactorAuth
    {
        private const string key = "S@2a88();{}#~.,"; // any 10-12 char string for use as private key in google authenticator
        public TwoFactorModel GenerateQRCode(string Username, string Password, string EmailID)
        {
            TwoFactorModel model = new TwoFactorModel();

            string message = "";
            bool status = false;

            //check username and password form our database here
            //for demo I am going to use Admin as Username and Password1 as Password static value
            if (!string.IsNullOrEmpty(Username) && !string.IsNullOrEmpty(Password))
            {
                status = true; // show 2FA form
                message = "CAR Rental Factor Verification";

                //2FA Setup
                TwoFactorAuthenticator tfa = new TwoFactorAuthenticator();
                model.UserUniqueKey = (Username + EmailID + key); //as Its a demo, I have done this way. But you should use any encrypted value here which will be unique value per user.


                var setupInfo = tfa.GenerateSetupCode("CAR Rental  2FA Setup :" + EmailID, Username, model.UserUniqueKey,true);
                model.QrCodeSetupImageUrl = setupInfo.QrCodeSetupImageUrl;
                model.ManualEntryKey = setupInfo.ManualEntryKey;
                model.TwoFactorMessage = "Valid credential";
            }
            else
            {
                model.TwoFactorMessage = "Invalid credential";
            }

            return model;
        }

        public bool Verify2FA(string passcode, string UserUniqueKey)
        {
            var token = passcode;
            bool isValiddate = false;
            TwoFactorAuthenticator tfa = new TwoFactorAuthenticator();
            bool isValid = tfa.ValidateTwoFactorPIN(UserUniqueKey, token);
            if (isValid)
            {
                return isValiddate = true;
            }
            else
            {
                return isValiddate = false;
            }
        }
    }
}

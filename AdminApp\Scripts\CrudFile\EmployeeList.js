﻿$(document).ready(function () {

    $("#EmployeeList").parent().parent().find("a").next().slideToggle(500);
    $("#EmployeeList").parent().parent().toggleClass('toggled');
    $("#EmployeeList").find("a").addClass("selectedMenu");
    
});




function ActivateDeactivate(Id) {
    var status = false;
    if ($("#Is_Active_" + Id).val() == false) {
       status=true;
    }
   
    if (Id != "") {
        $.ajax({
            url: 'EmployeeEnableDisable?PKID=' + Id + '&status=' + status,
            type: "POST",
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    alert("Employee Status Successfully Updated!");
                    document.location.reload();
                }
                else {
                    alert("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}


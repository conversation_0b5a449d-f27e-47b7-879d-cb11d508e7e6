﻿@model DBLinker.Lib.RLT_SUBSCRIBERS

@{
    ViewBag.Title = "SubscriberManager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="https://cdn.datatables.net/select/1.2.2/css/select.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.2/js/dataTables.select.min.js"></script>
<link href="~/Content/toastr.min.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<section class="content">
    .
    <div class="row">
        <div class="col-md-12">
            <!--breadcrumbs start -->
            <ul class="breadcrumb">
                <li><a href="#"><i class="fa fa-user"></i> Admin</a></li>
                <li><a href="#"><i class="fa fa-address-card"></i>Subscriber List</a></li>

            </ul>
            <!--breadcrumbs end -->
        </div>
    </div>
    @using (Html.BeginForm())
    {

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.IsValid)
        @Html.HiddenFor(x => x.ID)
    @Html.HiddenFor(x => x.SubsciberMailID)

        



        <section class="panel tasks-widget">
            <header class="panel-heading">
              Subscriber List
            </header>
            <div class="panel-body">

                <div class="task-content">

                    <table id="example" class="display" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th></th>
                                <th>Email ID</th>
                                <th>IsValid</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th></th>
                                <th>Email ID</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </tfoot>
                        <tbody>
                            @if (ViewBag.newsGridList != null)
                            {
                                foreach (var item in ViewBag.newsGridList)
                                {
                                    <tr>
                                        <input type="hidden" id="<EMAIL>" value="@item.SubsciberMailID" />
                                        
                                        <input type="hidden" id="<EMAIL>" value="@item.IsValid" />
                                        <td></td>
                                        <td>@item.SubsciberMailID</td>
                                        <td>@item.IsValid</td>
                                        <td>
                                            <button class="btn btn-default btn-xs" onclick="ActivateDeactivate(@item.ID)" type="button"><i class="fa fa-check"></i></button>
                                         </td>
                                    </tr>
                                }

                            }
                        </tbody>
                    </table>
                </div>


            </div>
        </section>
    }
</section>

<script src="~/Scripts/CrudFile/SubscriberCrudJs.js"></script>

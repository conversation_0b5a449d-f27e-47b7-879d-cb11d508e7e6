-- <PERSON><PERSON>t to add payment_link field to RLT_BOOKING table
-- Execute this script on your database to add the new column

USE [CabYaari] -- Database name from connection string
GO

-- Add payment_link column to RLT_BOOKING table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RLT_BOOKING]') AND name = 'payment_link')
BEGIN
    ALTER TABLE [dbo].[RLT_BOOKING]
    ADD [payment_link] NVARCHAR(500) NULL
    
    PRINT 'payment_link column added successfully to RLT_BOOKING table'
END
ELSE
BEGIN
    PRINT 'payment_link column already exists in RLT_BOOKING table'
END
GO

-- Optional: Add index on payment_link for better performance if needed
-- CREATE NONCLUSTERED INDEX [IX_RLT_BOOKING_payment_link] ON [dbo].[RLT_BOOKING] ([payment_link])
-- GO

PRINT 'Database update completed successfully!'

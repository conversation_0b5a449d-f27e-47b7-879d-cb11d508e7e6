﻿@model DBLinker.Lib.RLT_ADMIN_USER
@{
    Layout = null;
}

<!DOCTYPE html>

<html lang="en">

<head>
    <title>Login</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" rel="stylesheet" type="text/css" />
    <!-- icons -->
    <link href="~/Content/assets/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="~/Content/assets/plugins/iconic/css/material-design-iconic-font.min.css">
    <!-- bootstrap -->
    <link href="~/Content/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- style -->
    <link rel="stylesheet" href="~/Content/assets/css/exrtapages/pages.css">
    <!-- favicon -->
    <link rel="shortcut icon" href="~/Content/img/RLT TOURS.png" />
</head>
<body>
    <div class="limiter">
        <div class="container-login page-background">
            <div class="wrap-login">
                <div class="login-form validate-form">
                    <span class="login-form-logo">
                        @*<i class="zmdi zmdi-flower"></i>*@
                        <img src="~/Content/img/RLT TOURS.png" />
                    </span>



                    @using (Html.BeginForm())
                    {
                        @Html.AntiForgeryToken()



                        <span class="login-form-title p-b-34 p-t-27">
                            Admin Panel Login
                        </span>
                        <div class="wrap-input validate-input" data-validate="Enter username">

                            @Html.TextBoxFor(x => x.UserName, new { @placeholder = "Username or Email!", @class = "input" })

                            <span class="focus-input" data-placeholder="&#xf207;"></span>
                        </div>
                        <div class="wrap-input validate-input" data-validate="Enter password">
                            @Html.PasswordFor(x => x.UserPWD, new { @placeholder = "Password!", @class = "input" })
                            <span class="focus-input" data-placeholder="&#xf191;"></span>
                        </div>
                        <div class="contact-form-checkbox">
                            <input class="input-checkbox" id="ckb1" type="checkbox" name="remember-me">
                            <label class="label-checkbox" for="ckb1">
                                Remember me
                            </label>
                        </div>
                        <div class="container-login-form-btn">
                            <button class="login-form-btn" type="submit">
                                Login
                            </button>
                        </div>
                        <div class="text-center p-t-90">
                            <a class="txt1" href="#">
                                Forgot Password?
                            </a>
                        </div>


                    }


                </div>
            </div>
        </div>
    </div>
    <!-- start js include path -->
    <script src="~/Content/assets/plugins/jquery/jquery.min.js"></script>
    <!-- bootstrap -->
    <script src="~/Content/assets/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="~/Content/assets/js/validation/login.js"></script>
    <!-- end js include path -->
</body>
</html>

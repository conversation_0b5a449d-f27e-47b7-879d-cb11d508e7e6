﻿$(document).ready(function () {
    $("#CARCompany").parent().parent().find("a").next().slideToggle(500);
    $("#CARCompany").parent().parent().toggleClass('toggled');
    $("#CARCompany").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function ()
    {
        resetForm();
        $("#hdnOperation").val();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function ()
    {

        if (IsValidate()) {

            $.ajax({
                url: 'CARCompany',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Company Successfully Saved!","Car Company Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function ()
    {

        
        if (IsValidate()) {

            $.ajax({
                url: 'CARCompany',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Company Successfully Updated!","Car Company Update");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id)
{
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Company_Name").val($("#name_" + Id).val());
    $("#Is_Active").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Company_Name").val($("#name_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'CARCompany',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Company Successfully Deleted!","Car Company Deleted");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id)
{
    $("#PKID").val(Id);
    $("#Company_Name").val($("#name_" + Id).val());
    $("#Is_Active").val($("#status_" + Id).val());
    $("#ID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');
    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'CARCompany',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response)
            {
                if (response > 0) {
                    toastr.success("Car Company Status Successfully Updated!","Car Company Status");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !","Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    $("#Company_Name").val('');

}

function IsValidate() {
    if ($("#Company_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter car company name !", "Required!");
        return false;
    }
    /*Repeat Validation*/
    if ($("#Company_Name").val() != "") {
        if ($("#Company_Name-error").html() !== undefined) {
            toastr.clear();
            toastr.error("Company Name already exists!", "Required!");
            return false;
        }
    }
    return true;
}
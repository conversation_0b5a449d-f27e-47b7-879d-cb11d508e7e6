﻿@model DBLinker.Lib.Model.M_Vendor_Details

@{
    ViewBag.Title = "Vendor View Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<script>
    $(document).ready(function () {

        $("#VendorManager").parent().parent().find("a").next().slideToggle(500);
        $("#VendorManager").parent().parent().toggleClass('toggled');
        $("#VendorManager").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">View Vendors Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/VendorManager">Vendors Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">View Vendors Details</li>
                </ol>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>@Model.Vendor_Company_Name Details</header>

                    </div>
                    <div class="card-body row">
                        <div class="col-lg-12 p-t-20">

                            @if (!string.IsNullOrEmpty(Model.Vendor_Photo))
                            {
                                <img src=/Docs/VendorPhoto/@Model.Vendor_Photo id="Vendor_Photo" height="120" width="100" />
                            }
                            else
                            {
                                <img src="~/Content/img/no-user-image.gif" id="Vendor_Photo" height="80" width="120" />
                            }
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Member_ID, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Vendor Memebr ID</label>
                            </div>
                        </div>


                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Vendor Name</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Phone1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Vendor Mobile Number</label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Phone2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Vendor Alternative Mobile Number</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_EmailId, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Vendor Email ID</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Vendor_Company_Address, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Vendor Address</label>
                            </div>
                        </div>



                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header> Documents</header>
                    </div>
                    <div class="card-body ">
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                <thead>
                                    <tr>

                                        <th width="40%">Document  Name</th>
                                        <th width="20%">View</th>
                                        <th width="20%">Verified</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @if (Model.M_Vendor_DocsList != null)
                                    {
                                        if (Model.M_Vendor_DocsList.Count != 0)
                                        {
                                            for (int i = 0; i < Model.M_Vendor_DocsList.Count; i++)
                                            {
                                                <tr class="odd gradeX">
                                                    <td>
                                                        @Html.DisplayFor(a => Model.M_Vendor_DocsList[i].Vendor_Doc_Name)
                                                    </td>

                                                    <td>
                                                        @if (!string.IsNullOrEmpty(Model.M_Vendor_DocsList[i].Vendor_Doc_Path))
                                                        {
                                                            <a onClick='return popupSubWindow(this)' href=/Docs/VendorDocs/@Model.M_Vendor_DocsList[i].Vendor_Doc_Path><img src='~/Content/img/attachment.png' height='25' width='25' title="Click here to view the document."></a>
                                                        }
                                                    </td>

                                                    <td>
                                                        <div class="col-md-10">
                                                            @if (Model.M_Vendor_DocsList[i].Is_Verified == true)
                                                            {
                                                                <label> Yes</label>
                                                            }
                                                            else
                                                            {
                                                                <label> No</label>
                                                            }
                                                        </div>

                                                    </td>
                                                </tr>
                                            }
                                            /**/
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header> Bank List</header>
                    </div>
                    <div class="card-body ">
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                <thead>
                                    <tr>

                                        <th width="30%">Bank Name</th>
                                        <th width="10%">Account Number</th>
                                        <th width="10%">IFSC CODE</th>
                                        <th width="30%">Account Holder Name</th>
                                        <th width="10%">Bank Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.M_Vendor_BankList != null)
                                    {
                                        if (Model.M_Vendor_BankList.Count != 0)
                                        {
                                            for (int i = 0; i < Model.M_Vendor_BankList.Count; i++)
                                            {

                                                <tr class="odd gradeX">

                                                    <td> @Html.DisplayFor(a => Model.M_Vendor_BankList[i].Bank_Name)</td>
                                                    <td>@Html.DisplayFor(a => Model.M_Vendor_BankList[i].Account_Number)</td>
                                                    <td>@Html.DisplayFor(a => Model.M_Vendor_BankList[i].IFSC_CODE)</td>
                                                    <td>@Html.DisplayFor(a => Model.M_Vendor_BankList[i].Account_Holder_Name)</td>
                                                    <td>
                                                        @{
                                                            if (Model.M_Vendor_BankList[i].Bank_Status != null || Model.M_Vendor_BankList[i].Bank_Status != false)
                                                            {
                                                                <span> Secondary </span>
                                                            }
                                                            else
                                                            {
                                                                <span> Primary </span>
                                                            }
                                                        }

                                                    </td>



                                                </tr>
                                            }

                                        }
                                    }

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/VendorManager">Back</a>
            </div>
        </div>

    </div>
</div>


﻿@model DBLinker.Lib.RLT_UserFeedBack

@{
    ViewBag.Title = "UserFeedBack";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/css/jquery.dataTables.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/select/1.2.2/css/select.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.2/js/dataTables.select.min.js"></script>
<link href="~/Content/toastr.min.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<section class="content">
    .
    <div class="row">
        <div class="col-md-12">
            <!--breadcrumbs start -->
            <ul class="breadcrumb">
                <li><a href="#"><i class="fa fa-user"></i> Admin</a></li>
                <li><a href="#"><i class="fa fa-comment-o"></i> Comments Manager</a></li>

            </ul>
            <!--breadcrumbs end -->
        </div>
    </div>
    @using (Html.BeginForm("UserFeedBack", "Admin", null, FormMethod.Post, new { enctype = "multipart/form-data" }))
    {

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.IsActive)
        @Html.HiddenFor(x => x.PKID)
        <div class="row" id="dvAddUpdate">
            <div class="col-md-12">
                <div class="col-lg-12">
                    <section class="panel">
                        <header class="panel-heading">
                            Add Update Comments
                        </header>
                        <div class="panel-body">

                            <div class="form-group">
                                <div class="col-sm-2">
                                    Name
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.Name, new { @class = "form-control", @placeholder = "enter redirect url !" })
                                </div>
                                <div class="col-sm-2">
                                   Phone
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.PhoneNo, new { @class = "form-control", @placeholder = "enter phone no !" })
                                </div>
                            </div>

                            <br />
                            <br />

                            <div class="form-group">
                                <div class="col-sm-2">
                                    Email Address
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.EmailID, new { @class = "form-control", @placeholder = "enter email address !" })

                                </div>
                                <div class="col-sm-2">
                                    Comment
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextAreaFor(x => x.Message, new { @class = "form-control",@cols=10, @placeholder = "enter comment !" })
                                </div>
                            </div>
                            <br />
                            <br />
                            <div class="form-group">
                                <div class="col-sm-2">
                                    Image
                                </div>
                                <div class="col-lg-4">
                                    <input type="file" name="imgstate" id="imgstate" class="form-control" />
                                    <label id="lblFileName" name="lblFileName" style="display:none"></label>
                                    @Html.HiddenFor(x => x.Photo)

                                </div>
                                <div class="col-sm-2">

                                </div>
                                <div class="col-lg-4">

                                </div>

                            </div>
                        </div>
                        <footer class="panel-footer">
                            <center>
                                <button type="submit" class="btn btn-info" id="btnSave" onclick="return IsValidate();" style="display:none">Save</button>
                                <button type="submit" class="btn btn-info" id="btnUpdate" onclick="return IsValidate();" style="display:none">Update</button>
                                <button type="reset" class="btn btn-danger" id="btnReset">cancel</button>
                            </center>
                        </footer>
                    </section>
                </div>

            </div>
        </div>


        <section class="panel tasks-widget">
            <header class="panel-heading">
                Comments Manager
                <button type="button" id="btnAdd" class="btn btn-info" style="float:right;margin-top: -5px;">Add Comment</button>
            </header>
            <div class="panel-body">

                <div class="task-content">

                    <table id="example" class="display" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th></th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Comment</th>
                                <th>Image</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th></th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Comment</th>
                                <th>Image</th>

                                <th></th>
                            </tr>
                        </tfoot>
                        <tbody>
                            @if (ViewBag.FeddbackList != null)
                            {

                                foreach (var item in ViewBag.FeddbackList)
                                {

                                    <tr>

                                        <input type="hidden" id="<EMAIL>" value="@item.Name" />
                                        <input type="hidden" id="<EMAIL>" value="@item.EmailID" />
                                        <input type="hidden" id="<EMAIL>" value="@item.PhoneNo" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Photo" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Message" />
                                        <input type="hidden" id="<EMAIL>" value="@item.IsActive" />

                                        <td></td>
                                        <td>@item.Name</td>
                                        <td>@item.PhoneNo</td>
                                        <td>@item.EmailID</td>
                                        <td>@item.IsActive</td>
                                        <td>@item.Message</td>
                                        <td><img src="~/UserFeedBack/@item.Photo" style="width:100px;height:100px" /></td>
                                        <td>
                                            <button class="btn btn-default btn-xs" onclick="ActivateDeactivate(@item.ID)" type="submit"><i class="fa fa-check"></i></button>
                                            <button class="btn btn-default btn-xs" onclick="Edit(@item.ID)" type="button"><i class="fa fa-pencil"></i></button>
                                            <button class="btn btn-default btn-xs" onclick="Delete(@item.ID)" type="submit"><i class="fa fa-times"></i></button>
                                        </td>
                                    </tr>
                                }
                            }


                        </tbody>
                    </table>
                </div>


            </div>
        </section>

    }



</section>
<script src="~/Scripts/CrudFile/UserFeedBackCrudJs.js"></script>



﻿@model DBLinker.Lib.Model.M_CarDriver_Details
@using AdminApp.Mapper;

@{
    ViewBag.Title = "Car Driver Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Driver Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add Car Driver</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddCarDriver", "VendorManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormCarDriverAdd" }))
        {
            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Car Driver</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.vendorList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Vendor_PKID, new SelectList(ViewBag.vendorList, "PKID", "Vendor_Company_Name"), "-- Select Vendor --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Vendor Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.carOwnerList != null)
                                    {
                                        @Html.DropDownListFor(x => x.Car_Owner_PKID, new SelectList(ViewBag.carOwnerList, "PKID", "Car_Owner_Name"), "-- Select Car Owner --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Car Owner</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.carList != null)
                                    {
                                        @Html.DropDownListFor(x => x.Car_PKID, new SelectList(ViewBag.carList, "PKID", "Car_Number"), "-- Select Car --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Car</span></label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Name, new { @class = "mdl-textfield__input", @onblur = "isTextSpecialValid(this);", @maxlength = "100" })
                                    <label class="mdl-textfield__label required"><span>Driver Name</span></label>
                                </div>
                            </div>

                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <img src="~/Content/img/no-user-image.gif" id="Driver_Photo_View" height="80" width="100" />
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Photo, new { @class = "mdl-textfield__input", @type = "file", @style = "height: 60px !important;border: 1px dashed rgba(0, 0, 0, 0.3);*/: ; " })
                                    @Html.HiddenFor(x => x.Driver_Photo_EditFile)
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_DOB, new { @class = "mdl-textfield__input", type = "date" })
                                    <label class="mdl-textfield__label required"><span>Driver DOB</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_DL, new { @class = "mdl-textfield__input", @maxlength = "25" })
                                    <label class="mdl-textfield__label required"><span>Driver Commercial DL</span></label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Aadhaar, new { @class = "mdl-textfield__input txtbx", @maxlength = "12" })
                                    <label class="mdl-textfield__label required"><span>Driver Aadhaar</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Drive_Phone_1, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label required"><span>Phone 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Phone_1_IsWhatsup, "Yes", new { @id = "Phone_1_IsWhatsup1" })
                                            <label for="radio1"><span><span></span></span> Yes</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Phone_1_IsWhatsup, "No", new { @id = "Phone_1_IsWhatsup2" })
                                            <label for="radio2"><span><span></span></span>No</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label"><span>Is Whatsup No</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Drive_Phone_2, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label">Phone 2</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Email, new { @class = "mdl-textfield__input", @maxlength = "100" })
                                    <label class="mdl-textfield__label">Mail Id</label>
                                </div>
                            </div>



                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Father_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Father Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Mother_Name, new { @class = "mdl-textfield__input", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Mother Name</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.DropDownListFor(x => x.Driver_Religion, new List<SelectListItem>{
new SelectListItem{ Text="Hindus", Value = "Hindus" },new SelectListItem{ Text="Muslims", Value = "Muslims" },
new SelectListItem{ Text="Jains", Value = "Jains" },new SelectListItem{ Text="Christians", Value = "Christians" },
new SelectListItem{ Text="Sikhs", Value = "Sikhs" },new SelectListItem{ Text="Other", Value = "Other" },
}, "-- Select Religion --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    <label class="mdl-textfield__label required"><span>Religion</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Driver_Gender, "Male", new { @id = "Driver_Gender1" })
                                            <label for="radio1"><span><span></span></span> Male</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Gender, "Female", new { @id = "Driver_Gender2" })
                                            <label for="radio2"><span><span></span></span>Female</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Driver Gender</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Driver_Marrital_Status, "Married", new { @id = "Driver_Marrital_Status1" })
                                            <label for="radio1"><span><span></span></span> Married</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Marrital_Status, "Unmarried", new { @id = "Driver_Marrital_Status2" })
                                            <label for="radio2"><span><span></span></span>Unmarried</label>
                                        </div>

                                        <div style="clear:both;"></div>
                                    </div>
                                    <label class="mdl-textfield__label required"><span>Marrital Status</span></label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Driver_Drinking_Status, "Drinker", new { @id = "Driver_Drinking_Status1" })
                                            <label for="radio1"><span><span></span></span> Drinker</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Drinking_Status, "Non-Drinker", new { @id = "Driver_Drinking_Status2" })
                                            <label for="radio2"><span><span></span></span>Non-Drinker</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                    </div>

                                    <label class="mdl-textfield__label required"><span>Drinking Status</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Driver_Smoking_Status, "Smoker", new { @id = "Driver_Smoking_Status1" })
                                            <label for="radio1"><span><span></span></span> Smoker</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Smoking_Status, "Non-Smoker", new { @id = "Driver_Smoking_Status2" })
                                            <label for="radio2"><span><span></span></span> Non-Smoker</label>
                                        </div>

                                        <div style="clear:both;"></div>
                                    </div>
                                    <label class="mdl-textfield__label required"><span>Smoking Status</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Eating_Type, "Vegetarian", new { @id = "Driver_Eating_Type1" })
                                            <label for="radio1"><span><span></span></span> Vegetarian</label>
                                        </div>
                                        <div style="float:left;width:50%">
                                            @Html.RadioButtonFor(x => x.Driver_Eating_Type, "Non Vegetarian", new { @id = "Driver_Eating_Type2" })
                                            <label for="radio2"><span><span></span></span> Non Vegetarian</label>
                                        </div>
                                        <div style="clear:both;"></div>
                                    </div>
                                    <label class="mdl-textfield__label required"><span>Driver Eating Type</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Current Address </span>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Current_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label required"><span>Address 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Current_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label required"><span>Address 2</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.StateList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Driver_Current_Address_State, new SelectList(ViewBag.StateList, "PKID", "State_Name"), "-- select State --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>State</span></label>
                                </div>
                            </div>
                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.Driver_Current_Address_City, new SelectList(string.Empty, "Value", "Text"), "-- select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })

                                    <label class="mdl-textfield__label required"><span>City</span></label>
                                </div>
                            </div>



                            <div class="col-lg-12 p-t-20">
                                <div style="float:left;width:34%">
                                    <span style="font-size:14px;">Permanent Address Same as Current Address ? </span>

                                </div>
                                <div style="float:left;width:7%">
                                    @Html.RadioButtonFor(x => x.Driver_Current_Permanent_Same, true, new { @id = "Driver_Current_Permanent_Same1" })
                                    <label for="radio1"><span><span></span></span>Yes</label>
                                </div>
                                <div style="float:left;width:10%">
                                    @Html.RadioButtonFor(x => x.Driver_Current_Permanent_Same, false, new { @id = "Driver_Current_Permanent_Same2" })
                                    <label for="radio2"><span><span></span></span>No</label>
                                </div>
                                <div style="clear:both;"></div>

                            </div>
                            <div class="col-lg-12 p-t-20">
                                <span style="font-size:15px;font-weight:bold;">Permanent Address </span>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Permanent_Address1, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label required"><span>Address 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Permanent_Address2, new { @class = "mdl-textfield__input", @maxlength = "300" })
                                    <label class="mdl-textfield__label required"><span>Address 2</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.StateList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Driver_Permanent_Address_State, new SelectList(ViewBag.StateList, "PKID", "State_Name"), "-- select State --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>State</span></label>
                                </div>
                            </div>
                            <div class="col-lg-2 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.Driver_Permanent_Address_City, new SelectList(string.Empty, "Value", "Text"), "-- select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })

                                    <label class="mdl-textfield__label required"><span>City</span></label>
                                </div>
                            </div>




                            <div class="col-lg-12 p-t-20">

                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Special_Remarking, new { @class = "mdl-textfield__input", @maxlength = "100" })
                                    <label class="mdl-textfield__label required"><span>Driver Profile Remarking</span></label>
                                </div>


                            </div>

                            <div class="col-lg-12 p-t-20 text-center">

                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Drivers</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a>") : Html.Raw(""))
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Vendor</th>
                                        <th>Driver Name</th>
                                        <th>Driver DOB</th>
                                        <th>Phone</th>
                                        <th>Driver Gender</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (ViewBag.CarDriverList != null)
                                    {
                                        foreach (var item in ViewBag.CarDriverList)
                                        {


                                            <tr class="odd gradeX">
                                                <td>
                                                    <img onerror="this.src='../Docs/No_image_available.png'" src="/Docs/DriverPhoto/@item.Driver_Photo" height="60" width="60" /> 
                                                </td>
                                                <td>@item.Vendor_Name</td>
                                                <td>@item.Driver_Name</td>
                                                <td>@item.Driver_DOB</td>
                                                <td>@item.Drive_Phone_1</td>
                                                <td>@item.Driver_Gender</td>
                                                <td>
                                                    @Html.Raw(item.Is_Active == null
                                               ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                               ? "<span class='label label-sm label-success'> Active </span>"
                                               : "<span class='label label-sm label-danger'> InActive </span>")

                                                </td>

                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID+ ")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit(" + item.PKID + ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Add ? Html.Raw("<a href='../VendorManagement/CarDriverDocs?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "CarDriver") + "' title='Upload Documents'><i class='material-icons'>attachment</i> Upload Docs</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_View ? Html.Raw("<a href='../VendorManagement/CarDriverView?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "CarDriver") + "' title='View Car Driver Info'><i class='material-icons'>remove_red_eye</i> View</a>") : Html.Raw(""))
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>

<script src="~/Scripts/CrudFile/CarDriverCrud.js"></script>


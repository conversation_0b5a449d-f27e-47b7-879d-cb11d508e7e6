﻿@model Common.Lib.Model.RLT_ADMIN_USER

@{
    ViewBag.Title = "Two Factor Auth";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Security Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Security Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Enable/Disable 2Factor Authentication</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

           
            @Html.AntiForgeryToken()
            @Html.HiddenFor(x => x.Is2TfaAuthentication)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>2 Factor Authenticator</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <label class="mdl-textfield__input"> @Model.UserLastName</label>


                                    <label class="mdl-textfield__label"> User Name</label>

                                    
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                   
                                    <label class="mdl-textfield__input">  @Model.UserEmailID</label>


                                    <label class="mdl-textfield__label"> Email Address</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (Convert.ToBoolean(Model.Is2TfaAuthentication))
                                    {
                                        <label class="mdl-textfield__input">
                                            <span class="label label-sm label-success">(Active)</span>
                                        </label>
                                        
                                    }
                                    else
                                    {
                                        <label class="mdl-textfield__input">
                                            <span class="label label-sm label-danger">(InActive)</span>
                                        </label>
                                        
                                    }
                                    <label class="mdl-textfield__label"> 2FA Status</label>
                                </div>
                            </div>


                            <div class="col-lg-12 p-t-20 text-center">

                                @if (Convert.ToBoolean(Model.Is2TfaAuthentication))
                                {

                                    @Html.ActionLink("Disable", "ActiveInactiveTwoFactorAuth", "Admin", new { @status = "false", @class = "btn btn-info" }, null)
                                }
                                else
                                {
                                    @*<button type="button" class="btn btn-info" id="btnEnable">Enable</button>*@
                                    @Html.ActionLink("Enable", "ActiveInactiveTwoFactorAuth", "Admin", new { @status = "true", @class = "btn btn-info" }, null)
                                }

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        
    </div>
</div>
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_Admin_User_Info
    {
        public long PKID { get; set; }
        public string Employee_Name { get; set; }
        public string Photo { get; set; }
        public Nullable<System.DateTime> DOB { get; set; }
        public string Phone_1 { get; set; }
        public string Phone_2 { get; set; }
        public string Phone_1_IsWhatsup { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public string Marrital_Status { get; set; }
        public string Spouses_Name { get; set; }
        public string Father_Name { get; set; }
        public string Mother_Name { get; set; }
        public Nullable<bool> Current_Permanent_Same { get; set; }
        public string Current_Address { get; set; }
        public string Permanent_Address { get; set; }
        public string Permanent_Address1 { get; set; }
        public string Permanent_Address2 { get; set; }
        public Nullable<int> Permanent_Address_State { get; set; }
        public Nullable<int> Permanent_Address_City { get; set; }
        public string Current_Address1 { get; set; }
        public string Current_Address2 { get; set; }
        public Nullable<int> Current_Address_State { get; set; }
        public Nullable<int> Current_Address_City { get; set; }
        public string C10th_School { get; set; }
        public string C10th_PassingYear { get; set; }
        public string C10th_Percentage { get; set; }
        public string C12th_School { get; set; }
        public string C12th_PassingYear { get; set; }
        public string C12th_Percentage { get; set; }
        public string Degree_Name { get; set; }
        public string Degree_College { get; set; }
        public string Degree_PassingYear { get; set; }
        public string Degree_Percentage { get; set; }
        public string Master_Degree_Name { get; set; }
        public string Master_Degree_College { get; set; }
        public string Master_Degree_PassingYear { get; set; }
        public string Master_Degree_Percentage { get; set; }
        public string Employee_ID { get; set; }
        public string Department { get; set; }
        public string Designation { get; set; }
        public string Supervisor { get; set; }
        public Nullable<int> Work_City { get; set; }
        public string Work_Phone { get; set; }
        public string Work_Email { get; set; }
        public Nullable<System.DateTime> Work_StartDate { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }
        public Nullable<int> UserId { get; set; }
    }
}

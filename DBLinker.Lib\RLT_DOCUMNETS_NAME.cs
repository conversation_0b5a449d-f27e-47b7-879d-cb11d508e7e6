//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLT_DOCUMNETS_NAME
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public RLT_DOCUMNETS_NAME()
        {
            this.RLT_Vendor_Docs = new HashSet<RLT_Vendor_Docs>();
            this.Vendor_Car_Driver_Docs = new HashSet<Vendor_Car_Driver_Docs>();
        }
    
        public int PKID { get; set; }
        public string Doc_For { get; set; }
        public string Doc_Name { get; set; }
        public Nullable<bool> Is_Doc_Req { get; set; }
        public Nullable<bool> Is_Doc_Expiry_Date { get; set; }
        public bool Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> CreatedBy { get; set; }
        public Nullable<int> UpdatedBy { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RLT_Vendor_Docs> RLT_Vendor_Docs { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Vendor_Car_Driver_Docs> Vendor_Car_Driver_Docs { get; set; }
    }
}

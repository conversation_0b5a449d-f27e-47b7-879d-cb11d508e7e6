﻿$("#dvAddUpdate").slideUp();
$(document).ready(function () {
    // Events  
    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });
    $("#btnSave").click(function () {
       ValidateForm(false) 
    });
    $("#btnUpdate").click(function () {
        ValidateForm(true);
    });
    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
    //End Events
});

//Functions 
function ValidateForm(isEdit) {
    if ($("#MenuName").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Menu Name!", "Error");
        $("#MenuName").focus();
        return false;
    }
    else if ($("#MenuURL").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Menu Url!", "Error");
        $("#MenuURL").focus();
        return false;
    }
    else if ($("#PageId").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Menu Page Id!", "Error");
        $("#PageId").focus();
        return false;
    }
    else if ($("#ParentMenuId").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Parent Menu Id!", "Error");
        $("#ParentMenuId").focus();
        return false;
    }
    else if ($("#ControllerName").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Controller Name!", "Error");
        $("#ControllerName").focus();
        return false;
    }
    else if ($("#ActionName").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Action Name!", "Error");
        $("#ActionName").focus();
        return false;
    }
    else if ($("#OrderNumber").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Order Number!", "Error");
        $("#OrderNumber").focus();
        return false;
    }
    ValidateDublicateRecord(isEdit);
    return true;
}
function ValidateDublicateRecord(isEdit) {
    var formData = new FormData($("#FormUserManagementMenuList")[0]);
    $.ajax({
        type: 'POST',
        url: 'CheckDuplicateMenuRecord',
        data: formData,
        contentType: false,
        processData: false,
        success: function (result) {
            if (result != null) {
                if (result.IsDublicate) {
                    var msg = "Dublicate Record Found! <br />";
                    if (result.OrderNumber) {
                        msg += " :- Order Number Is Dublicate"
                        $("#OrderNumber").focus();
                    }
                    if (result.PageId) {
                        msg += " :- PageId Is Dublicate <br />"
                        $("#PageId").focus();
                    }
                    if (result.MenuURL) {
                        msg += " :- Menu URL Is Dublicate <br />"
                        $("#MenuURL").focus();
                    }
                    toastr.clear();
                    toastr.warning(msg, "Warning");
                }
                else {
                    if (isEdit) updateData();
                    else saveData();
                }
            }
            else {
                toastr.clear();
                toastr.error("Somthing went wrong please try again!", "Error");
            }
        },
        onerror: function (error) {
            toastr.clear();
            toastr.error("Somthing went wrong please try again!");
        }
    });
}
function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'ActiveDeactiveMenuList',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Menu List Status successfully changed!", "Menu List");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.error("Sorry Status Not Updated, Please Try it Again !", "Error");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                toastr.error("Sorry Status Not Updated, Please Try it Again !" + response.responseText, "Error");
            }
        });
    }
}
function resetForm() {
    $("#PKID").val('');
    $("#MenuName").val('');
    $("#MenuIcon").val('');
    $("#MenuURL").val('');
    $("#PageId").val('');
    $("input[type=radio][name=IsActive]").prop("checked", false);
    $("#ParentMenuId").val('');
    $("#ControllerName").val('');
    $("#ActionName").val('');
    $("#OrderNumber").val('');
    $("#ActiveMenuClass").val('');
}
function Edit(Id) {
    $.ajax({
        url: 'EditMenu',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                // fill data 
                $("#PKID").val(data.PKID);
                $("#MenuName").val(data.MenuName);
                $("#MenuIcon").val(data.MenuIcon);
                $("#MenuURL").val(data.MenuURL);
                $("#PageId").val(data.PageId);
                $("#ParentMenuId").val(data.ParentMenuId);
                $("#ControllerName").val(data.ControllerName);
                $("#ActionName").val(data.ActionName);
                $("#OrderNumber").val(data.OrderNumber);
                $("#ActiveMenuClass").val(data.ActiveMenuClass);
                if (data.IsActive != null && data.IsActive == true) $("#IsActiveTrue").prop("checked", true);
                else $("#IsActiveFalse").prop("checked", true);
                //end checkboxes
                // set default values 
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !", "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}
function saveData() {
    var formData = new FormData($("#FormUserManagementMenuList")[0]);
    $.ajax({
        type: 'POST',
        url: 'MenuList',
        data: formData,
        contentType: false,
        processData: false,
        success: function (result) {
            if (result) {
                resetForm();
                toastr.clear();
                toastr.success("Menu Added successfully!");
                window.setTimeout(function () { location.reload() }, 1000)
            }
            else {
                toastr.clear();
                toastr.error("Record Not added successfully please try again!");
            }
        },
        onerror: function (error) {
            toastr.clear();
            toastr.error("Somthing went wrong please try again!");
        }
    });
}
function updateData() {
    var formData = new FormData($("#FormUserManagementMenuList")[0]);
    $.ajax({
        url: 'EditMenu',
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
            if (response > 0) {
                toastr.success("Menu successfully Updated!", "Menu List");
                window.setTimeout(function () { location.reload() }, 1000)
            } else {
                toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");
            }
        },
        failure: function (response) {
        },
        error: function (response) {
            toastr.error("Sorry Data Not Updated,Please Try it Again !<br />" + response.responseText, "Error");
        }
    });
}
//End Function


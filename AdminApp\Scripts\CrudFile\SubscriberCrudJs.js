﻿$(document).ready(function () {


    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });



});



function ActivateDeactivate(Id) {
    $("ID").val('');
    $("#SubsciberMailID").val($("#emailid_" + Id).val());
    $("#IsValid").val($("#status_" + Id).val());

    $("ID").val(Id);

    if ($("#status_" + Id).val() == false) {
        $("#IsValid").val(true);
    }
    else {
        $("#IsValid").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("ID").val() != "") {

        $.ajax({
            url: 'SubscriberManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}



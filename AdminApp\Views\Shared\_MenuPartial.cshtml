﻿@using AdminApp.Filters;
@using DBLinker.Lib.Model;
<style>
    a.nav-link.not-display {
        display: none !important; 
    }
</style>
<!-- start sidebar menu -->
<div class="sidebar-container">
    <div class="sidemenu-container navbar-collapse collapse fixed-menu">
        <div id="remove-scroll">
            <ul class="sidemenu page-header-fixed p-t-20" data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200">
                <li class="sidebar-toggler-wrapper hide">
                    <div class="sidebar-toggler">
                        <span></span>
                    </div>
                </li>
                <li class="sidebar-user-panel">
                    <div class="user-panel">
                        @*<div class="row">
                                <div class="sidebar-userpic">
                                    <img alt="" src="~/Content/img/RLT TOURS.png" style="width:150px;height:50px">

                                </div>
                            </div>*@
                        <div class="profile-usertitle">

                            <div class="sidebar-userpic-name"> @Convert.ToString(((AdminApp.Filters.CustomPrincipal)(User)).UserName)</div>
                            <div class="profile-usertitle-job"> @Convert.ToString(((AdminApp.Filters.CustomPrincipal)(User)).UserEmailID) </div>

                        </div>
                        <div class="sidebar-userpic-btn">
                            <a class="tooltips" href="#" data-placement="top" data-original-title="Profile">
                                <i class="material-icons">person_outline</i>
                            </a>
                            <a class="tooltips" href="#" data-placement="top" data-original-title="Mail">
                                <i class="material-icons">mail_outline</i>
                            </a>
                            <a class="tooltips" href="#" data-placement="top" data-original-title="settings">
                                <i class="material-icons">settings</i>
                            </a>
                            <a class="tooltips" href="~/Admin/LogOut" data-placement="top" data-original-title="Logout">
                                <i class="material-icons">input</i>
                            </a>
                        </div>
                    </div>
                </li>

                @if (Session["MenuList"] != null)
                {
                    List<M_Role_Menu_Mapping> MenuList = (List<M_Role_Menu_Mapping>)(Session["MenuList"]);
                    string menuString = "";
                    foreach (var parent in MenuList)
                    {
                        if (parent.ParentMenuId == 0)
                        {
                            var getChilds = MenuList.FindAll(x => x.ParentMenuId == parent.Menu_Id);
                            menuString += "<li class='nav-item'><a href = '" + (parent.MenuURL != null ? parent.MenuURL : "#") + "' class='nav-link nav-toggle'><i class='" + parent.MenuIcon + "'></i><span class='title'>" + parent.MenuName + "</span>" + (getChilds.Count > 0 ? "<span class='arrow'></span>" : "") + "</a>";
                            if (getChilds.Count > 0)
                            {
                                menuString += "<ul class='sub-menu'>";
                                foreach (var child in getChilds)
                                {
                                    menuString += "<li class='nav-item' id='" + child.PageId + "'><a href='" + child.MenuURL + "' class='nav-link " + child.ActiveMenuClass + "'><span class='title'>" + child.MenuName + "</span></a></li>";
                                }
                                menuString += "</ul>";
                            }
                            menuString += "</li>";
                        }
                    }
                    @Html.Raw(menuString);
                }

            </ul>
        </div>
    </div>
</div>
<!-- end sidebar menu -->

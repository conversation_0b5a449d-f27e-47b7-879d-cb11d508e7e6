﻿
@{
    ViewBag.Title = "TempBookingResult";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Bookings Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Bookings Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                  
                </ol>
            </div>
        </div>

       
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Bookings</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            
                            
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th> Name</th>
                                        <th>Mobile</th>
                                        <th>Pickup Location</th>
                                        <th>Drop Off Location</th>
                                        <th>Travel Date</th>
                                        <th>Pickup Time</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.BookingRepo != null)
                                    {
                                        foreach (var item in ViewBag.BookingRepo)
                                        {

                                    <tr class="odd gradeX">


                                        <td>@item.User_Name</td>

                                        <td>@item.Mobile_Number</td>

                                        <td>@item.PickUp_Address</td>

                                        <td>@item.DropOff_Address</td>

                                        <td>@item.Travel_Date</td>

                                        <td>@item.Travel_Time</td>


                                    </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Discount_Coupon
    {  
        public int PKID { get; set; }
        [Remote("Check_Discount_Coupon_Code", "Common", ErrorMessage = "Discount Coupon Code already exists!")]
        public string Discount_Coupon { get; set; }
        public Nullable<decimal> Discount { get; set; }
        public string Coupon_Name { get; set; }
        public string Coupon_Remark { get; set; }
        public Nullable<int> Max_Discount { get; set; }
        public Nullable<int> Fare_When_Applied { get; set; }
        public Nullable<System.DateTime> Coupon_Last_Date { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Updated_By { get; set; }
        public int? CouponMaxTimeAllowance { get; set; }

    }
}

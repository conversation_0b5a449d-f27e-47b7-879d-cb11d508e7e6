﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();

    $("#lblFileName").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function ()
    {
        $("#lblFileName").slideUp();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

   

});

function Edit(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#lblFileName").slideDown();
    $("#lblFileName").show();

    $("#PackageBannerImage").val($("#img_" + Id).val());
    $("#lblFileName").text($("#img_" + Id).val());

    $("#PackageName").val($("#name_" + Id).val());
    $("#PackageLink").val($("#link_" + Id).val());
    $("#IsActive").val(true);
    $("#PackageBannerImageCaption").val($("#caption_" + Id).val());
    $('#AboutPackage').val($("#about_" + Id).val());

    $("#PackageMetaTag").val($("#tag_" + Id).val());
    $("#StateID").val($("#StateID_" + Id).val());
    $("#PackageMetaDescription").val($("#desc_" + Id).val());


    tinyMCE.get('AboutPackage').setContent($("#about_" + Id).val());

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id)
{

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#lblFileName").slideDown();
    $("#lblFileName").show();

    $("#PackageBannerImage").val($("#img_" + Id).val());
    $("#lblFileName").text($("#img_" + Id).val());

    $("#PackageName").val($("#name_" + Id).val());
    $("#PackageLink").val($("#link_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());
    $("#PackageBannerImageCaption").val($("#caption_" + Id).val());
    $('#AboutPackage').val($("#about_" + Id).val());

    $("#PackageMetaTag").val($("#tag_" + Id).val());
    $("#StateID").val($("#StateID_" + Id).val());
    $("#PackageMetaDescription").val($("#desc_" + Id).val());

    if ($("#status_" + Id).val()==false)
    {
        $("#IsActive").val(true);
    }
    else
    {
        $("#IsActive").val(false);
    }

  
    $("#hdnStatus").val(false);
}

function ActivateDeactivate(Id)
{

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#lblFileName").slideDown();
    $("#lblFileName").show();

    $("#PackageBannerImage").val($("#img_" + Id).val());
    $("#lblFileName").text($("#img_" + Id).val());

    $("#PackageName").val($("#name_" + Id).val());
    $("#PackageLink").val($("#link_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());
    $("#PackageBannerImageCaption").val($("#caption_" + Id).val());
    $('#AboutPackage').val($("#about_" + Id).val());

    $("#PackageMetaTag").val($("#tag_" + Id).val());
    $("#StateID").val($("#StateID_" + Id).val());
    $("#PackageMetaDescription").val($("#desc_" + Id).val());
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnStatus").val(false);
}

function resetForm() {
   

    $("#PackageName").val('');
    $("#PackageLink").val('');
    $("#PackageBannerImageCaption").val('');
    $("#PackageMetaTag").val('');
    $("#StateID").val('');
    $("#PackageMetaDescription").val('');

    tinyMCE.get('AboutPackage').getContent('');
  
}

function IsValidate()
{
    var data =tinyMCE.get('AboutPackage').getContent(); 
    if ($("#PackageName").val() == "") {
        toastr.clear();
        toastr.error("Please enter package name !");
        return false;
    }
    if ($("#PackageLink").val() == "") {
        toastr.clear();
        toastr.error("Please enter redirect url !");
        return false;
    }
   

    if ($("#PackageMetaTag").val() == "") {
        toastr.clear();
        toastr.error("Please enter package meta tag !");
        return false;
    }
    if ($("#StateID").val() == "") {
        toastr.clear();
        toastr.error("Please select state !");
        return false;

    }
    if ($("#PackageMetaDescription").val() == "") {
        toastr.clear();
        toastr.error("Please enter package meta description  !");
        return false;

    }
    if ($("#PackageBannerImageCaption").val() == "") {
        toastr.clear();
        toastr.error("Please enter image caption !");
        return false;

    }
    if (data == "")
    {
        toastr.clear();
        toastr.error("Please enter package details !");
        return false;

    }

    return true;
}
﻿using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Lib
{
  public interface IResizeImage
    {

        ImageCodecInfo GetEncoder(ImageFormat format);
         void AddWatermark(string filename, string filename1, int type);
        void ResizeImage1(String fullpath, String destpath, int width, int height);

        void ResizeImage2(String fullpath, String destpath, int width, int height);

        void ResizeImage3(String fullpath, String destpath, int width, int height);

        void ResizeByHeight(String fullpath, String destpath, int height);
        void cropimage(int Width, int Height, string sourceFilePath, string saveFilePath);

        void ResizeImage4(String fullpath, String destpath, int width, int height);

         void ResizeImage5(String fullpath, String destpath, int width, int height);

        void ResizeImage6(String fullpath, String destpath, int width, int height);
    }
}

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DBLinker.Lib
{
    using System;
    using System.Collections.Generic;
    
    public partial class RLTMostFavoriteRoute
    {
        public int ID { get; set; }
        public Nullable<int> PickUpCityID { get; set; }
        public Nullable<int> DropOffCityID { get; set; }
        public Nullable<int> CarCategoryID { get; set; }
        public Nullable<int> TripType { get; set; }
        public Nullable<decimal> Fare { get; set; }
        public Nullable<decimal> GSTFare { get; set; }
        public Nullable<decimal> Distance { get; set; }
        public string TravelDuration { get; set; }
        public string Facilities { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<int> CreatedBy { get; set; }
        public Nullable<int> UpdatedBy { get; set; }
        public string CityImage { get; set; }
    }
}

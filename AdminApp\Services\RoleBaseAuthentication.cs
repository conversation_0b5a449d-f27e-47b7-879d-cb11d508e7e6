﻿using AdminApp.Filters;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace AdminApp.Services
{
    public class RoleBaseAuthentication : ActionFilterAttribute, IExceptionFilter
    {

        private RLTDBContext entity;
        private string ParentAction, ActionTpe;
        private bool _IsIgnore;
        public RoleBaseAuthentication()
        {
            this.entity = new RLTDBContext();
        }

        public RoleBaseAuthentication(object IsIgnore)
        {
            this._IsIgnore = IsIgnore != null && IsIgnore.ToString().ToLower() == "ignore" ? true : false;
        }

        public RoleBaseAuthentication(string ParentAction, object ActionType)
        {
            this.ParentAction = ParentAction.ToLower();
            this.ActionTpe = ActionType.ToString().ToLower();
        }

        public enum ActionType
        {
            View,
            Add,
            Edit,
            Delete,
        }

        public enum Mode
        {
            Ignore
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (!_IsIgnore)
            {
                string controller = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName.ToString().ToLower(), action = filterContext.ActionDescriptor.ActionName.ToString().ToLower(), url = "../" + controller + "/" + action;
                bool IsAccessible = false;
                if (HttpContext.Current.User != null)
                {
                    var currentUser = HttpContext.Current.User as CustomPrincipal;
                    var menuList = HttpContext.Current.Session["MenuList"] as List<M_Role_Menu_Mapping>;
                    if (menuList != null)
                    {
                        var getCurrentAccessedMenu = menuList.Where(x => x.MenuURL?.ToLower() == url ||
                            (x.ActionName?.ToLower() == action && x.ControllerName?.ToLower() == controller)).FirstOrDefault();
                        if (getCurrentAccessedMenu != null && this.ActionTpe == null)
                            HttpContext.Current.Session["CurrentAccessedMenu"] = getCurrentAccessedMenu;
                        else
                        {
                            url = "../" + controller + "/" + this.ParentAction;
                            var getCurrentAccessedMenuByChildAction = menuList.Where(x => x.MenuURL?.ToLower() == url ||
                                (x.ActionName?.ToLower() == this.ParentAction && x.ControllerName?.ToLower() == controller)).FirstOrDefault();
                            if (getCurrentAccessedMenuByChildAction != null)
                            {
                                HttpContext.Current.Session["CurrentAccessedMenu"] = getCurrentAccessedMenuByChildAction;
                                if (this.ActionTpe == "view") IsAccessible = getCurrentAccessedMenuByChildAction.Is_View ? true : false;
                                else if (this.ActionTpe == "add") IsAccessible = getCurrentAccessedMenuByChildAction.Is_Add ? true : false;
                                else if (this.ActionTpe == "edit") IsAccessible = getCurrentAccessedMenuByChildAction.Is_Edit ? true : false;
                                else if (this.ActionTpe == "delete") IsAccessible = getCurrentAccessedMenuByChildAction.Is_Delete ? true : false;
                                if (!IsAccessible) RedirectToRoutes(filterContext, false);
                            }
                            else RedirectToRoutes(filterContext, false);
                        }
                    }
                    else RedirectToRoutes(filterContext, true);
                }
            }
        }

        private void RedirectToRoutes(ActionExecutingContext filterContext, bool isRedirectToLogin)
        {
            if (filterContext.HttpContext.Request.IsAjaxRequest() && !isRedirectToLogin)
                filterContext.Result = new JsonResult { Data = new { HttpStatusCode.Unauthorized }, JsonRequestBehavior = JsonRequestBehavior.AllowGet };

            else if (isRedirectToLogin && !filterContext.HttpContext.Request.IsAjaxRequest())
                filterContext.Result = new RedirectResult("~/Login/Index?returnUrl=" + HttpContext.Current.Server.UrlEncode(HttpContext.Current.Request.Url.AbsoluteUri));
            else if (!isRedirectToLogin && !filterContext.HttpContext.Request.IsAjaxRequest())
                filterContext.Result = new RedirectResult("~/error/Not-Authorized");
        }

        public static M_Role_Menu_Mapping getPermittedAction()
        {
            if (HttpContext.Current.Session["CurrentAccessedMenu"] != null)
                return HttpContext.Current.Session["CurrentAccessedMenu"] as M_Role_Menu_Mapping;
            else return new M_Role_Menu_Mapping();
        }

        public static M_Role_Menu_Mapping getAddtionalPermittedAction(string action, string controller)
        {
            var menuList = HttpContext.Current.Session["MenuList"] as List<M_Role_Menu_Mapping>;
            var currentUser = HttpContext.Current.User as CustomPrincipal;
            var CurrentAccessedMenu = new M_Role_Menu_Mapping();
            string url = "../" + controller.ToLower() + "/" + action.ToLower();
            if (menuList != null && currentUser != null)
            {
                var getCurrentAccessedMenu = menuList.Where(x => x.MenuURL?.ToLower() == url || (x.ActionName?.ToLower() == action && x.ControllerName?.ToLower() == controller)).FirstOrDefault();
                return getCurrentAccessedMenu;
            }
            return CurrentAccessedMenu;
        }

        public void OnException(ExceptionContext filterContext)
        {
            string massage = "\n" + filterContext.RouteData.Values["controller"].ToString() + " -> "
                + filterContext.RouteData.Values["action"].ToString() + " -> "
                + DateTime.Now.ToString() + "\n"
                + " Exception -> " + filterContext.Exception.StackTrace;
            LogExecation(massage);
        }

        private void LogExecation(string data)
        {
            File.AppendAllText(HttpContext.Current.Server.MapPath("~/Services/Data.txt"), data);
        }


    }
}
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{839A2F95-B7C8-40F1-ACA8-3DE4E7D5E340}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DBLinker.Lib</RootNamespace>
    <AssemblyName>DBLinker.Lib</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="C__EFMigrationsHistory.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="C__EFMigrationsHistory1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\M_BookingRules.cs" />
    <Compile Include="Model\M_Bookings.cs" />
    <Compile Include="Model\M_Driver_Enquiries.cs" />
    <Compile Include="Model\M_RLTMostFavoriteRoutes.cs" />
    <Compile Include="Model\M_Template_Mail_Message.cs" />
    <Compile Include="Model\M_Template.cs" />
    <Compile Include="Model\M_Employee.cs" />
    <Compile Include="Model\M_Report.cs" />
    <Compile Include="Model\M_BookingFareList.cs" />
    <Compile Include="Model\M_BookingMode.cs" />
    <Compile Include="Model\M_Booking.cs" />
    <Compile Include="Model\M_Booking_Status.cs" />
    <Compile Include="Model\M_Admin_User.cs" />
    <Compile Include="Model\M_Role_Menu_Mapping.cs" />
    <Compile Include="Model\M_Admin_Role.cs" />
    <Compile Include="Model\M_Trip_Types.cs" />
    <Compile Include="Model\M_Discount_Coupon.cs" />
    <Compile Include="Model\M_Car_Model.cs" />
    <Compile Include="Model\M_Company.cs" />
    <Compile Include="Model\M_Document_Name.cs" />
    <Compile Include="Model\M_Entity_Codes.cs" />
    <Compile Include="Model\M_ENTITY_GENERALS.cs" />
    <Compile Include="Model\M_Vendor_Bank_Details.cs" />
    <Compile Include="Model\M_CarDriver_Details.cs" />
    <Compile Include="Model\M_Car_Details.cs" />
    <Compile Include="Model\M_CarOwner.cs" />
    <Compile Include="Model\M_City.cs" />
    <Compile Include="Model\M_Vendor_Details.cs" />
    <Compile Include="Model\M_Car_Docs.cs" />
    <Compile Include="Model\M_CarDriver_Docs.cs" />
    <Compile Include="Model\M_Vendor_Docs.cs" />
    <Compile Include="Proc_GetUserDtl_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Product.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RefreshToken.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Repository\IGenericRepository.cs" />
    <Compile Include="Repository\Impl\GenericRepository.cs" />
    <Compile Include="RLTBookingAuditTrail.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTCARMODEL.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RLTCARMODEL.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTCARMODEL.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTCARMODEL.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RLTCARMODEL.edmx</DependentUpon>
    </Compile>
    <Compile Include="RLTConfigurationAuditTrail.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTMostFavoriteRoute.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTResource.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLTResourceStau.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_AboutUs.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ADMIN_ROLE.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ADMIN_USER.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Admin_User_Info.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BANK_NAMES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKINGS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_FARE.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_GST.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_MODE.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_PAYMENT_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_RULES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_BOOKING_STATUS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_CATEGORY.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_CHARGES_FECILITIES_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_COMPANY.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_DOCS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_DRIVER_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_DRIVER_DOCS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_FUEL_TYPES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_MODEL.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_OWNER_BANK_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_OWNER_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CAR_SEGMENT.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CITY.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CLASS_MASTER.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Company_Details.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ContactInformation.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_CONTACT_ENQUIRIES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_COUNTRY.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Department_Name.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_DISCOUNT_COUPON.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_DOCUMNETS_NAME.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_DRIVER_APPROVE_STATUS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_DRIVER_ENQUIRIES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ENTITY_GENERALS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_FAQ_Details.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_LOCATION_CODE.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_LOG.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_MENU_MASTER.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_NextId.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_PAYMENT_METHOD.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_PrivacyPolicy.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ROLE_MENU_MAPPING.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ROUTES_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_ROUTE_PLAN.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Services.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_STATE.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_SUBSCRIBERS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Template_Mail_Message.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Template_Master.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_TermsConditions.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_TRIP_TYPES.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_UserFeedBack.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_USER_LOGIN_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Vendor_Bank_Details.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_VENDOR_CAR_DETAILS.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Vendor_Details.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RLT_Vendor_Docs.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Role.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Role1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RoleClaim.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="RoleClaims1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="SP_GET_REPORT_DATA_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="SqlHelper.cs" />
    <Compile Include="TempBooking.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="User.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="User1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserClaim.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserClaims1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserDiscountAvailed.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserLogin.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserLogins1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserToken.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="UserTokens1.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Car_Category_Details_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Cities_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Comapny_Info_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Discount_Coupon_Details_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Driver_Booking_info_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Services_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_Template_Master_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_UserLogin_Details_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="usp_Get_User_Details_Result.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
    <Compile Include="Vendor_Car_Driver_Docs.cs">
      <DependentUpon>RLTCARMODEL.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config">
      <SubType>Designer</SubType>
    </None>
    <EntityDeploy Include="RLTCARMODEL.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>RLTCARMODEL.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="packages.config" />
    <None Include="RLTCARMODEL.edmx.diagram">
      <DependentUpon>RLTCARMODEL.edmx</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="RLTCARMODEL.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>RLTCARMODEL.edmx</DependentUpon>
      <LastGenOutput>RLTCARMODEL.Context.cs</LastGenOutput>
    </Content>
    <Content Include="RLTCARMODEL.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>RLTCARMODEL.edmx</DependentUpon>
      <LastGenOutput>RLTCARMODEL.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>
.file-uploader-lable:hover {
    cursor: pointer;
}

.file-uploader {
    border: 2px dashed #c7c1c1;
    height: 160px;
    width: 250px;
    padding: 5px;
    margin-top: 10px;
    border-radius: 10px;
    position: relative;
    outline: none;
}


.file-uploder-img {
    position: absolute;
    height: 144px;
    width: 96%;
    overflow: hidden;
    object-fit: fill;
    border-radius: 10px;
}

.file-uploader-text {
    position: absolute;
    top: 35%;
    left: 15%;
    font-size: 20px;
    color: #6f6e6c;
    text-align: center;
}

.searchableLinksContainer {
    width: 300px;
}

.searchableLinks {
    position: fixed;
    top: 60px;
    padding: 5px;
    left: 290px;
    background: #fff;
    border-radius: 0 0 10px 10px;
    z-index: 9999;
    width: inherit;
    overflow: auto;
    overflow-x: hidden;
    max-height: 200px;
}

    .searchableLinks > a.nav-link {
        background: #ff5c5cb3;
        padding: 2px 5px;
        border-radius: 5px;
        outline: none;
        margin-bottom: 3px;
        overflow-x: hidden;
        cursor: pointer;
        border-top: 2px solid #ff5c5c;
        outline: none;
    } 

        .searchableLinks > a.nav-link:hover {
            cursor: pointer;
            margin: 0px 5px 3px;
            transition: all 0.3s cubic-bezier(0.8, -0.02, 0, 0.16);
            color: #fff;
        }

span.small-text {
    font-size: 10px;
    color: white;
    display: flex;
    position: relative;
    left: 25px;
    top: -5px;
}

.searchableLinks > a > i {
    position: relative;
    top: 8px;
}

.search-result-found {
    position: relative;
    background: #46dcbe;
    padding: 0;
    margin: 0;
    padding: 2px 0px 2px 30px;
    border-radius: 5px;
    outline: none;
    margin: 3px 3px 5px 3px;
    color: #fff;
    text-transform: capitalize;
}
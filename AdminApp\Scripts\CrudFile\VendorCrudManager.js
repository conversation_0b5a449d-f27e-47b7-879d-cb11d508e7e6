﻿$(document).ready(function () {

    $("#VendorManager").parent().parent().find("a").next().slideToggle(500);
    $("#VendorManager").parent().parent().toggleClass('toggled');
    $("#VendorManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();


    $("#btnAdd").click(function () {
        MemeberId_Pwd_Generator();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

    });


    $("#btnSave").click(function () {
        VendorManagerAdd();
    });

    $("#btnUpdate").click(function () {
        VendorManagerEdit();
    }); 
    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    }); 
    $("#Vendor_Member_ID").click(function () { 
        MemeberId_Pwd_Generator();
    });
});

function Edit(Id) {
    $.ajax({
        url: 'VendorManagerEdit',
        type: "GET",
        data: { pkid: Id },
        dataType: "json",
        success: function (data) {
            if (data != null) {
                // fill data 
                $("#PKID").val(data.PKID);
                $("#Vendor_Name").val(data.Vendor_Name);
                $("#Vendor_Company_Name").val(data.Vendor_Company_Name);
                $("#Vendor_Member_ID").val(data.Vendor_Member_ID);
                $("#Vendor_EmailId").val(data.Vendor_EmailId);
                $("#Vendor_Phone1").val(data.Vendor_Phone1);
                if (data.Phone1_IsWhatsup == null) $("#Phone1_IsWhatsup2").prop("checked", true);
                else {
                    if (data.Phone1_IsWhatsup.toLowerCase() == "no") $("#Phone1_IsWhatsup2").prop("checked", true);
                    else $("#Phone1_IsWhatsup1").prop("checked", true);
                }
                $("#Vendor_Phone2").val(data.Vendor_Phone2);
                $("#Vendor_Company_Address").val(data.Vendor_Company_Address);
                $("#Vendor_PWD").val(data.Vendor_PWD);
                $("#Vendor_Photo_EditFile").val(data.Vendor_Photo_EditFile);
                $("#Vendor_Photo_View").attr("src", "/Docs/VendorPhoto/" + data.Vendor_Photo_EditFile);
                $("#Is_Active").val(data.Is_Active);
                //focus 
                $("#Vendor_Name").focus();
                // set default values
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    }); 
}

function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Vendor_Name").val($("#Vendor_Owner_Name_" + Id).val());
    $("#Vendor_Company_Name").val($("#Vendor_Company_Name_" + Id).val());
    $("#Vendor_Member_ID").val($("#Vendor_Member_Id_" + Id).val());
    $("#Vendor_EmailId").val($("#Vendor_EmailID_" + Id).val());
    $("#Vendor_Phone1").val($("#Vendor_Phone1_" + Id).val());
    $("#Vendor_Phone2").val($("#Vendor_Phone2_" + Id).val());
    $("#Vendor_Company_Address").val($("#Vendor_Address_" + Id).val());
    $("#Vendor_PWD").val($("#Vendor_Pwd_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {


        $.ajax({
            url: 'AddVendor',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            contentType: false,
            enctype: 'multipart/form-data',
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Vendor Successfully Deleted!", "Vendor Deleted");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    $.ajax({
        url: 'VendorManagerActivedDeactivate',
        type: "POST", 
        data: { pkid: Id },
        dataType: "json",
        success: function (response) {
            if (response) {
                toastr.success("Vendor Status Successfully Updated!", "Vendor Status Update");
                document.location.reload();
            }
            else if (response == true) {
                toastr.success("Vendor Status Successfully Updated!", "Vendor Status Update");
                document.location.reload();
            }
            else {
                toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

            }
        },
        failure: function (response) {

        },
        error: function (response) {
            alert(response.responseText);
        }
    }); 
}

function resetForm() {

    $("#Vendor_Name").val('');
    $("#Vendor_Company_Name").val('');
    $("#Vendor_Member_ID").val('');
    $("#Vendor_EmailId").val('');
    $("#Vendor_Phone1").val('');
    $("#Vendor_Phone2").val('');
    $("#Vendor_Company_Address").val('');
    $("#Vendor_PWD").val('');

}

function IsValidate(isUpdate) {


    if ($("#Vendor_Member_ID").val() == "") {
        toastr.clear();
        toastr.error("Please enter Vendor Member ID!", "Required!");
        return false;
    }
   
    if ($("#Vendor_PWD").val() == "") {
        toastr.clear();
        toastr.error("Please enter Vendor Password!", "Required!");
        return false;
    }
    if ($("#Vendor_Company_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter  vendor company name!", "Required!");
        return false;
    }
    if (!isUpdate) {
        /*Repeat Validation*/
        if ($("#Vendor_Company_Name").val() != "") {
            if ($("#Vendor_Company_Name-error").html() !== undefined) {
                toastr.clear();
                toastr.error("Vendor Company Name already exists!", "Required!");
                return false;
            }
        }
        /*Repeat Validation*/
        if ($("#Vendor_Member_ID").val() != "") {
            if ($("#Vendor_Member_ID-error").html() !== undefined) {
                toastr.clear();
                toastr.error("Vendor Member ID already exists!", "Required!");
                return false;
            }
        }
    }
    if ($("#Vendor_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter vendor name !", "Required!");
        return false;
    }
    if ($("#Vendor_Phone1").val() == "") {
        toastr.clear();
        toastr.error("Please enter vendor number!", "Required!");
        return false;
    }
    // if ($("#Vendor_EmailId").val() == "") {
    // toastr.clear();
    // toastr.error("Please enter vendor email id !", "Required!");
    // return false;
    // }

    if ($("#Vendor_Company_Address").val() == "") {
        toastr.clear();
        toastr.error("Please enter vendor address!", "Required!");
        return false;
    }
    return true;
}

function MemeberId_Pwd_Generator() {

    $.ajax({
        url: 'GenerateMemberIDPwd',
        type: "POST",
        traditional: true,
        data: $("form").serialize(),
        dataType: "json",
        success: function (response) {
            $("#Vendor_Member_ID").val(response[0]);
            $("#Vendor_PWD").val(response[1]);
            if (response.length > 0) {

                toastr.success("Member Id and Pwd generated!");

            }
            else {
                toastr.error("Sorry Data Not Saved,please try it again !" + response.length());

            }
        },
        failure: function (response) {

        },
        error: function (response) {
            alert(response.responseText);
        }
    });
}

function myFunction() {
    var x = document.getElementById("Vendor_PWD");
    if (x.type === "password") {
        x.type = "text";
    } else {
        x.type = "password";
    }
}

function VendorManagerAdd() {
    if (IsValidate(false)) { 
        var formData = new FormData($("#idFormVendorAdd")[0]);
        $.ajax({
            url: 'VendorManagerAdd',
            type: "POST", 
            data: formData,
            contentType: false,
            processData: false, 
            success: function (response) {
                if (response > 0) {
                    toastr.success("Vendor Informations Successfully Saved!", "Vendor Add");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Vendor Informations Not Saved,please try it again !", "Error"); 
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                toastr.error(response.responseText, "Error");  
            }
        });
    }
}
function VendorManagerEdit() {
    if (IsValidate(true)) { 
        var formData = new FormData($("#idFormVendorAdd")[0]);
        $.ajax({
            url: 'VendorManagerEdit',
            type: "POST", 
            data: formData,
            contentType: false,
            processData: false, 
            success: function (response) {
                if (response > 0) {
                    toastr.success("Vendor Informations Successfully Updated!", "Vendor Update");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Vendor Informations Not Updated,Please Try it Again !", "Error"); 
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                toastr.error(response.responseText, "Error");  
            }
        });



    }
}

$("#Vendor_Photo").change(function (event) {

    if (PhotoCheck($("#Vendor_Photo").val()) == false) {
        var tmppath = URL.createObjectURL(event.target.files[0]);
        $("#Vendor_Photo_View").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
    }
    else {
        $("#Vendor_Photo").val("");
        $("#Vendor_Photo_View").fadeIn("fast").attr('src', "/Content/img/no-user-image.gif");
        toastr.clear();
        toastr.error("Please upload only JPG,PNG Image !");
        return false;
    }
});





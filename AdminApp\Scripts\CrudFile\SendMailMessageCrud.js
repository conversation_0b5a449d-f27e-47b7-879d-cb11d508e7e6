﻿$(document).ready(function () {

    $("#SendMailMessageManager").parent().parent().find("a").next().slideToggle(500);
    $("#SendMailMessageManager").parent().parent().toggleClass('toggled');
    $("#SendMailMessageManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();

    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'AddSendMailMessageManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#SendToType").change(function () {
       
        $("#DivSendToId").show();
        GetSendToId();
    });
    $("#SendToId").change(function () {
        $("#DivTemplate").show();
        GetTemplate();
    });
    $("#TemplateId").change(function () {
        if ($("input[name='Mail_SMS_Type']:checked").val() == "Mail") {
            $("#DivMail").show();
            $("#DivMailSubject").show();
        }
        else if ($("input[name='Mail_SMS_Type']:checked").val() == "SMS") {
            $("#DivMobile").show();
        }
        GetTemplateDetails();


    });

});


function Delete(Id) {
    $("#PKID").val('');
    $("#PKID").val(Id);
   
    $("#hdnOperation").val('D');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'AddSendMailMessageManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Deleted!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {
    $("#Template_Name").val('');
    $("#Subject").val('');
    $("#Message_Body").val('');
    tinyMCE.get('Message_Body').setContent('');

}

function IsValidate() {
    if ($("#Mail_SMS_Type1").prop("checked") == false && $("#Mail_SMS_Type2").prop("checked") == false) {
        toastr.clear();
        toastr.error("Please Select Template For!", "Required!");
        return false;
    }
    if ($("#SendToType").val() == "") {
        toastr.clear();
        toastr.error("Please select Send To !", "Required!");
        return false;
    }
    if ($("#SendToId").val() == "") {
        toastr.clear();
        toastr.error("Please select Name !", "Required!");
        return false;
    }
    if ($("#TemplateId").val() == "") {
        toastr.clear();
        toastr.error("Please select Template !", "Required!");
        return false;
    }
   
    if (tinyMCE.get('Message_Body').getContent() == "") {
        toastr.clear();
        toastr.error("Please enter Message Body !", "Required!");
        return false;
    }


    $("#Message_Body").val(tinyMCE.get('Message_Body').getContent());

    return true;
}


function GetSendToId() {
    $("#SendToId").empty();
    $.ajax({
        type: 'POST',
        url: '../MailSMS/GetSendToId', // Calling json method  
        dataType: 'json',
        data: { id: $("#SendToType").val() },
        success: function (Data) {
            $("#SendToId").append('<option value=>-- Select Name --</option>');
            $.each(Data, function (i, Data) {
                $("#SendToId").append('<option value="' + Data.Value + '">' +
                    Data.Text + '</option>');
            });


        }
    });
    return false;
}


function GetTemplate() {
    $("#TemplateId").empty();
    $.ajax({
        type: 'POST',
        url: '../MailSMS/GetTemplate', // Calling json method  
        dataType: 'json',
        data: { id: $("input[name='Mail_SMS_Type']:checked").val() },
        success: function (Data) {
            $("#TemplateId").append('<option value=>-- Select Template --</option>');
            $.each(Data, function (i, Data) {
                $("#TemplateId").append('<option value="' + Data.Value + '">' +
                    Data.Text + '</option>');
            });


        }
    });
    return false;
}

function GetTemplateDetails() {
    $.ajax({
        type: 'POST',
        url: '../MailSMS/GetTemplateDetails?Mail_SMS_Type=' + $("input[name='Mail_SMS_Type']:checked").val()
        + '&SendToType=' + $("#SendToType").val() + '&SendToId=' + $("#SendToId").val()
        + '&TemplateId=' + $("#TemplateId").val(),
        dataType: 'json',
        success: function (Template_Mail_Message) {
            if (Template_Mail_Message != "") {
                if ($("input[name='Mail_SMS_Type']:checked").val() == "Mail") {
                    $("#MailId").val(Template_Mail_Message.MailId);
                    $("#Subject").val(Template_Mail_Message.Subject);
                }
                else if ($("input[name='Mail_SMS_Type']:checked").val() == "SMS") {
                    $("#MobileNo").val(Template_Mail_Message.MobileNo);
                }
              
               
                tinyMCE.get('Message_Body').setContent(Template_Mail_Message.Message_Body);
                
            }
            else {
                $("#MobileNo").val("");
                $("#MailId").val("");
                $("#Subject").val("");
                $("#Message_Body").val("");

                toastr.clear();
                toastr.error("No Template record found!");
                return false;
            }
        }
    });
    return false;
}
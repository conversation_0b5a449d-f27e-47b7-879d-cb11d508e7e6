﻿$(document).ready(function ()
{

    $("#StateManager").parent().parent().find("a").next().slideToggle(500);
    $("#StateManager").parent().parent().toggleClass('toggled');
    $("#StateManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    

    $("#btnAdd").click(function ()
    {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'StateManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'StateManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id)
{
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#Is_Active").val(true);
    $("#Counrty_PKID").val($("#countryID_" + Id).val());
    $("#State_Name").val($("#name_" + Id).val());
    $("#State_Abbr").val($("#code_" + Id).val());
   
    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id) {

    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#Counrty_PKID").val($("#countryID_" + Id).val());
    $("#State_Name").val($("#name_" + Id).val());
    $("#State_Abbr").val($("#code_" + Id).val());

    

    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false)
    {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);
}

function ActivateDeactivate(Id)
{
  
    $("#Counrty_PKID").val($("#countryID_" + Id).val());
    $("#State_Name").val($("#name_" + Id).val());
    $("#State_Abbr").val($("#code_" + Id).val());

    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false)
    {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');
}


function resetForm()
{
    $("#dvname").slideUp();

    $("#Counrty_PKID").val('');
    $("#State_Abbr").val('');
    $("#State_Name").val('');
   
}

function IsValidate()
{
    if ($("#Counrty_PKID").val() == "")
    {
        toastr.clear();
        toastr.error("Please select country !");
        return false;
    }
    if ($("#State_Name").val() == "")
    {
        toastr.clear();
        toastr.error("Please enter state name !");
        return false;
    }
    if ($("#State_Abbr").val() == "")
    {
        toastr.clear();
        toastr.error("Please enter state code !");
        return false;

    }
    
   
    return true;
}
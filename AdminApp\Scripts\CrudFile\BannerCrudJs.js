﻿$(document).ready(function () {
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $("#lblFileName").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function ()
    {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#lblFileName").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });

    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

   

});

function Edit(Id)
{
    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();

    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#IsActive").val($("#status_" + Id).val());
    $('#RefID').val($("#refid_" + Id).val());
    $('#BannerTitle').val($("#title_" + Id).val());
    //$('#PageLink').val($("#link_" + Id).val());
    $('#BannerMessage').val($("#message_" + Id).val());
    $('#BannerImagePath').val($("#img_" + Id).val());

    $("#lblFileName").slideDown();
    $("#lblFileName").show();
    $("#lblFileName").text($("#img_" + Id).val());
}

function Delete(Id)
{
    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#IsActive").val($("#status_" + Id).val());
    $('#RefID').val($("#refid_" + Id).val());
    $('#BannerTitle').val($("#title_" + Id).val());
    //$('#PageLink').val($("#link_" + Id).val());
    $('#BannerMessage').val($("#message_" + Id).val());
    $('#BannerImagePath').val($("#img_" + Id).val());
  
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

}

function ActivateDeactivate(Id)
{


    $("#ID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    $("#IsActive").val($("#status_" + Id).val());
    $('#RefID').val($("#refid_" + Id).val());
    $('#BannerTitle').val($("#title_" + Id).val());
    //$('#PageLink').val($("#link_" + Id).val());
    $('#BannerMessage').val($("#message_" + Id).val());
    $('#BannerImagePath').val($("#img_" + Id).val());

    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

}

function resetForm()
{
    $("#RefID").val('');
    $("#BannerTitle").val('');
    //$("#PageLink").val('');
    $("#BannerImagePath").val('');
    $("#BannerMessage").val('');
}

function IsValidate()
{
    if ($("#RefID").val() == "") {
        toastr.clear();
        toastr.error("Please select product !");
        return false;
    }
    if ($("#BannerTitle").val() == "") {
        toastr.clear();
        toastr.error("Please enter banner title !");
        return false;
    }
    //if ($("#PageLink").val() == "") {
    //    toastr.clear();
    //    toastr.error("Please enter page link !");
    //    return false;

    //}
    //if ($("#BannerImagePath").val() == "") {
    //    toastr.clear();
    //    toastr.error("Please select banner image !");
    //    return false;
    //}

    return true;
}
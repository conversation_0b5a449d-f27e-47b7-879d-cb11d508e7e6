﻿$(document).ready(function ()
{

    $("#Booking").parent().parent().find("a").next().slideToggle(500);
    $("#Booking").parent().parent().toggleClass('toggled');
    $("#Booking").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnUpdate").slideUp();

    // Clear pickup address when "From City" changes
    $('#City_From_Id').change(function() {
        $('#PickUp_Address').val('');
        $('#PickUpAddressLatitude').val('');
    });

    // Clear drop-off address when "To City" changes
    $('#City_To_Id').change(function() {
        $('#DropOff_Address').val('');
        $('#PickUpAddressLongitude').val('');
    });

    $('#PickUp_Address').autocomplete({
        source: function (request, response) {
            $('#PickUpAddressLatitude').val('');

            // Check if a city is selected
            var cityFromId = $("#City_From_Id").val();
            if (!cityFromId || cityFromId === '') {
                response([]);
                return;
            }

            var autocompleteUrl = '../GoogleMap/AutoSuggestion?placeName=' + $("#PickUp_Address").val() + '&CityId=' + cityFromId;
            $.ajax({
                url: autocompleteUrl,
                type: 'GET',
                cache: false,
                dataType: 'json',
                success: function (json) {
                    response($.map(json, function (data, id) {
                        return {
                            label: data.placeAddress,
                            value: data.latlong
                        };
                    }));
                },
                error: function (xmlHttpRequest, textStatus, errorThrown) {
                    console.log('some error occured', textStatus, errorThrown);
                }
            });
        },
        minLength: 1,
        select: function (event, ui) {
            $('#PickUp_Address').val(ui.item.label);
            $('#PickUpAddressLatitude').val(ui.item.value);
            return false;
        }
    });



    $('#DropOff_Address').autocomplete({

        source: function (request, response) {
            $('#PickUpAddressLongitude').val('');

            // Check if a city is selected
            var cityToId = $("#City_To_Id").val();
            if (!cityToId || cityToId === '') {
                response([]);
                return;
            }

            var autocompleteUrl = '../GoogleMap/AutoSuggestion?placeName=' + $("#DropOff_Address").val() + '&CityId=' + cityToId;
            $.ajax({
                url: autocompleteUrl,
                type: 'GET',
                cache: false,
                dataType: 'json',
                success: function (json) {
                    response($.map(json, function (data, id) {

                        return {
                            label: data.placeAddress,
                            value: data.latlong
                        };
                    }));
                },
                error: function (xmlHttpRequest, textStatus, errorThrown) {
                    console.log('some error occured', textStatus, errorThrown);
                }
            });
        },
        minLength: 1,
        select: function (event, ui) {

            $('#DropOff_Address').val(ui.item.label);
            $('#PickUpAddressLongitude').val(ui.item.value);
            return false;
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {
            $.ajax({
                url: 'UpdateBooking',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#Vendor_PKID").change(function () {
        BindCar(0);
    });
    $("#btnSearchCab").click(function () {
        showLoader();
        calculateFare();
        setTimeout(function () {
            hideLoader();
        }, 1000);
    });


    

});

function Edit(Id) {
    
    var statusId = $("#Booking_Status_Id_" + Id).val();
    let status = statusId == "1" ? "true" : statusId == "2" ? "true" : "false";
    if (status == "false") {

        toastr.clear();
        toastr.error("Sorry! You can't edit this booking except new booking request or cab assign! ");
        $("#btnUpdate").slideDown();
        $("#btnReset").slideDown();
        return false;
    }

    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

     $("#City_From_Id").val($("#City_From_Id_" + Id).val());
     $("#City_To_Id").val($("#City_To_Id_" + Id).val());
     $("#Trip_Type_Id").val($("#Trip_Type_Id_" + Id).val());
     $("#Car_Category_Id").val($("#Car_Category_Id_" + Id).val());

     $("#Basic_Fare").val($("#Basic_Fare_" + Id).val());
     $("#Driver_Charge").val($("#Driver_Charge_" + Id).val());
     $("#Toll_Charge").val($("#Toll_Charge_" + Id).val());
     $("#GST").val($("#GST_" + Id).val());
    $("#Distance").val($("#distance_" + Id).val());
    $("#Duration").val($("#duration_" + Id).val());
     $("#GST_Fare").val($("#GST_Fare_" + Id).val());
     $("#Fare").val($("#Fare_" + Id).val());
    $("#PickUp_Address").val($("#PickUp_Address_" + Id).val());
    $("#DropOff_Address").val($("#DropOff_Address_" + Id).val());
    $("#BookingEditRemark").val($("#BookingEditRemark_" + Id).val());

    $("#PickUpAddressLatitude").val($("#PickUp_AddressLatLong_" + Id).val());
    $("#PickUpAddressLongitude").val($("#DropOff_AddressLatLong_" + Id).val());

    $("#completepickupaddress").val($("#completepickupaddress_" + Id).val());
    $("#completedropoffpaddress").val($("#completedropoffpaddress_" + Id).val());

     if ($("#PickUp_Date_" + Id).val() != "") {
         var arr = $("#PickUp_Date_" + Id).val().split('/');
         var NewDate = arr[2] + '/' + arr[1] + '/' +arr[0];
         $("#PickUp_Date").val(NewDate);
     }

    //$("#PickUp_Date").val($("#PickUp_Date_" + Id).val());
     $("#PickUp_Time").val($("#PickUp_Time_" + Id).val());
     $("#Name").val($("#Name_" + Id).val());
     $("#Mobile_No1").val($("#Mobile_No1_" + Id).val());
     $("#Mobile_No2").val($("#Mobile_No2_" + Id).val());
     $("#Mail_Id").val($("#Mail_Id_" + Id).val());
     $("#Mode_Of_Payment_Id").val($("#Mode_Of_Payment_Id_" + Id).val());
    $("#Vendor_PKID").val($("#Vendor_PKID_" + Id).val());
    BindCar($("#Car_PKID_" + Id).val());


   
   

    $("#Is_Active").val(true);
    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function ActivateDeactivate(Id) {
    $("#PKID").val('');
    $("#Is_Active").val($("#status_" + Id).val());
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('E');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'UpdateBooking',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    $("#City_From_Id").val('');
    $("#City_To_Id").val('');
    $("#Trip_Type_Id").val('');
    $("#Car_Category_Id").val('');
    }

function IsValidate() {  
        
    
        if ($("#City_From_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select City From!");
            return false;
        }
        if ($("#City_To_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select City To!");
            return false;
        }
        if ($("#Trip_Type_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Trip Type!");
            return false;
        }
        if ($("#Car_Category_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Car Category!");
            return false;
        }
        if ($("#Fare").val() == "") {
            toastr.clear();
            toastr.error("Please enter fare!");
            return false;
        }
  
        if ($("#PickUp_Date").val() == "") {
            toastr.clear();
            toastr.error("Please enter pick up date!");
            return false;
        }
        if ($("#PickUp_Time").val() == "") {
            toastr.clear();
            toastr.error("Please enter pick up time!");
            return false;
        }
        else if ($("#PickUp_Address").val() == "" && $("#PickUpAddressLatitude").val() == "") {
            toastr.clear();
            toastr.error("Please select pickup address!");
            return false;
        }
        else if ($("#DropOff_Address").val() == "" && $("#PickUpAddressLongitude").val() == "") {
            toastr.clear();
            toastr.error("Please select drop off address!");
            return false;
        }
    
        if ($("#Name").val() == "") {
            toastr.clear();
            toastr.error("Please enter name!");
            return false;
        }
        if ($("#Mail_Id").val() == "") {
            toastr.clear();
            toastr.error("Please enter mail id!");
            return false;
        }
        if ($("#Mobile_No1").val() == "") {
            toastr.clear();
            toastr.error("Please enter mobile!");
            return false;
        }

   
        if ($("#Mode_Of_Payment_Id").val() == "") {
            toastr.clear();
            toastr.error("Please select Payment Mode!");
            return false;
        }

    


    return true;
}


function BindCar(selectId) {
    $("#Car_PKID").empty();
    $.ajax({
        type: 'POST',
        url: '../VendorManagement/GetCarByVendor', // Calling json method  
        dataType: 'json',
        data: { id: $("#Vendor_PKID").val() },
        success: function (Car) {
            $("#Car_PKID").append('<option value=>-- Select Car --</option>');
            $.each(Car, function (i, Car) {
                $("#Car_PKID").append('<option value="' + Car.Value + '">' +
                    Car.Text + '</option>');
            });
            if (selectId != 0)
                $("#Car_PKID").val(selectId);

        }
    });
    return false;
}

//function calculateFare() {
//    $.ajax({
//        type: 'POST',
//        url: '../BookingManagement/calculateFare?City_From_Id=' + $("#City_From_Id").val()
//        + '&City_To_Id=' + $("#City_To_Id").val() + '&Trip_Type_Id=' + $("#Trip_Type_Id").val()
//        + '&Car_Category_Id=' + $("#Car_Category_Id").val(),
//        dataType: 'json',
//        success: function (FareChart) {
//            if (FareChart != "") {
//                $("#Fare").val(FareChart.Final_Fare);
//                $("#GST").val(FareChart.GST_Amount);
//                $("#Basic_Fare").val(FareChart.Basic_Fare);
//                $("#Driver_Charge").val(FareChart.Driver_Charge);
//                $("#Toll_Charge").val(FareChart.Toll_Charge);
//                $("#GST").val(FareChart.GST);
//            }
//            else {
//                $("#Fare").val("");
//                $("#GST").val("");
//                $("#Basic_Fare").val("");
//                $("#Driver_Charge").val("");
//                $("#Toll_Charge").val("");
//                $("#GST").val("");

//                toastr.clear();
//                toastr.error("No booking fare record found!");
//                return false;
//            }
//        }
//    });
//    return false;
//}



function calculateFare() {

    if ($("#PickUp_Address").val() == "" || $("#PickUpAddressLatitude").val() == "") {
        toastr.clear();
        toastr.error("Please select pickup address!");
        return false;
    }
    else if ($("#DropOff_Address").val() == "" || $("#PickUpAddressLongitude").val() == "") {
        toastr.clear();
        toastr.error("Please select drop off address!");
        return false;
    }

    $.ajax({
        type: 'POST',
        url: '../GoogleMap/GetDistanceTimeDetailsBasedOnPickUpDropOffLocation?pickUpAddressLatLong=' + $("#PickUpAddressLatitude").val()
            + '&dropOffAddressLatLong=' + $("#PickUpAddressLongitude").val() + '&Trip_Type_Id=' + $("#Trip_Type_Id").val()
            + '&Car_Category_Id=' + $("#Car_Category_Id").val(),
        dataType: 'json',
        success: function (FareChart) {
            if (FareChart != "") {
                $("#Distance").val(FareChart.Distance);
                $("#Basic_Fare").val(FareChart.Basic_Fare);
                $("#GST_Fare").val(FareChart.GST_Fare);

                $("#Duration").val(FareChart.Duration);
                $("#FixRateNote").html(FareChart.FixRateNote);
                $("#Fare").val(Math.round(FareChart.Fare));

                // Store the suggested partial payment amount from API (if needed for future use)
                if (FareChart.PartialPaymentAmount) {
                    // Can be used for partial payment functionality in booking list if needed
                    console.log("Suggested partial payment amount: ₹" + FareChart.PartialPaymentAmount);
                }

            }
            else {
                $("#Distance").val("");
                $("#Basic_Fare").val("");
                $("#GST_Fare").val("");
                $("#Fare").val("");
                $("#Duration").val("");

                toastr.clear();
                toastr.error("No booking fare record found!");
                return false;
            }
        }
    });
    return false;
}

function calculateDiscount() {
    $.ajax({
        type: 'POST',
        url: '../BookingManagement/calculateDiscount?Coupon_Code=' + $("#Coupon_Code").val(),
        dataType: 'json',
        success: function (CouponData) {
            if (CouponData != "") {

                if (CouponData.Is_Active == true) {

                    $('#hd_OldBasic_Fare').val($('#Basic_Fare').val());
                    $('#hd_OldGST_Fare').val($('#GST_Fare').val());
                    $('#hd_OldFare').val($('#Fare').val());
                    var Basic_Fare = (isNaN(parseFloat($('#Basic_Fare').val()))) ? 0 : parseFloat($('#Basic_Fare').val());
                    if (Basic_Fare >= CouponData.Fare_When_Applied) {



                        var Discount = (Basic_Fare * CouponData.Discount) / 100;
                        if (Discount > CouponData.Max_Discount) {
                            Discount = CouponData.Max_Discount;
                        }
                        var finalBasicAmount = Basic_Fare - Discount;
                        $('#Basic_Fare').val(finalBasicAmount.toFixed(2));

                        var GST_Fare = (finalBasicAmount * 5) / 100;
                        $('#GST_Fare').val(GST_Fare.toFixed(2));
                        var Fare = finalBasicAmount + GST_Fare;
                        $('#Fare').val(Math.round(Fare));

                        $("#Coupon_Code_Applied").html(Discount.toFixed(2) + " rupee discount applied sucessfully");
                        $('#Coupon_Discount').val(Discount.toFixed(2));

                        $("#btnRemoveDiscount").show();
                        $("#btnApplyDiscount").hide();
                    }
                    else {

                        toastr.clear();
                        toastr.error("Basic fare Must be " + CouponData.Fare_When_Applied + " for this coupon");
                        return false;
                    }

                }

                else {

                    toastr.clear();
                    toastr.error("sorry this Coupon is expired!");
                    return false;
                }

            }
            else {

                toastr.clear();
                toastr.error("No Coupon found!");
                return false;
            }
        }
    });
    return false;
}
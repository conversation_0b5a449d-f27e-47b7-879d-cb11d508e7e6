﻿@model DBLinker.Lib.Model.M_City

@{
    ViewBag.Title = "City Manager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
 


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">City Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">City Manager</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add City Details</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {


            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add City Details</header>

                        </div>
                        <div class="card-body row">
                            <div class="col-lg-4 p-t-20">
                                <div class="search_categories"> 
                                    @if (ViewBag.StateList != null)
                                    {

                                        @Html.DropDownListFor(x => x.State_PKID, new SelectList(ViewBag.StateList, "PKID", "State_Name"), "-- Select State --", new { @class = "mdl-textfield__input", @style = "padding-top: 41px;" })
                                    }
                                    <label for="sample2" class="mdl-textfield__label"> State</label>
                                </div>
                            </div> 
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.City_Name, new { @class = "mdl-textfield__input" })
                                    @Html.ValidationMessageFor(model => model.City_Name, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label">City Name</label>
                                </div>
                            </div>
                            <div class="col-lg-2 p-t-20">
                                <button type="button" style="position: absolute;bottom:20px;left:25px;" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSearch">Get Data</button> 
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.latitude, new { @class = "mdl-textfield__input", @readonly = "readonly" })
                                    <label class="mdl-textfield__label">Latitude</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.longitude, new { @class = "mdl-textfield__input", @readonly = "readonly" })
                                    <label class="mdl-textfield__label">Longitude</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.eLoc, new { @class = "mdl-textfield__input", @readonly = "readonly" })
                                    <label class="mdl-textfield__label">eLoc</label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.score, new { @class = "mdl-textfield__input", @readonly = "readonly" })
                                    <label class="mdl-textfield__label">Score</label>
                                </div>
                            </div>
                            <div class="col-lg-12 p-t-20 text-center">


                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Cities</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th>City Name</th>
                                        <th>City latitude</th>
                                        <th>City longitude</th>
                                        <th>State Name</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.cityGridList != null)
                                    {
                                        foreach (var item in ViewBag.cityGridList)
                                        {

                                            <tr class="odd gradeX">
                                                <input type="hidden" id="<EMAIL>" value="@item.City_Name" />
                                                <input type="hidden" id="<EMAIL>" value="@item.latitude" />
                                                <input type="hidden" id="<EMAIL>" value="@item.longitude" />
                                                <input type="hidden" id="<EMAIL>" value="@item.eLoc" />
                                                <input type="hidden" id="<EMAIL>" value="@item.score" />
                                                <input type="hidden" id="<EMAIL>" value="@item.State_PKID" />
                                                <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />

                                                <td>@item.City_Name</td>


                                                <td>@item.latitude</td>
                                                <td>@item.longitude</td>
                                                @{
                                                    if (item.RLT_STATE != null)
                                                    {
                                                        <td>@item.RLT_STATE.State_Name</td>
                                                    }
                                                    else
                                                    {
                                                        <td></td>
                                                    }
                                                }
                                                <td>
                                                    @{
                                                        if (item.Is_Active != null && item.Is_Active != false)
                                                        {
                                                            <span class="label label-sm label-success"> Active </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="label label-sm label-danger"> In-Active </span>
                                                        }
                                                    }
                                                </td>


                                                <td align="center">
                                                    <a class="btn btn-tbl-edit btn-xs" onclick="ActivateDeactivate(@item.PKID)" title="Enable">
                                                        <i class="fa fa-check"></i>
                                                    </a>
                                                    <a class="btn btn-tbl-delete btn-xs" onclick="Edit(@item.PKID)" title="Edit">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>

                                                    @*<a class="btn btn-tbl-delete btn-xs" onclick="Delete(@item.PKID)" title="Delete">
                                                            <i class="fa fa-trash-o "></i>
                                                        </a>*@


                                                </td>
                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Scripts/CrudFile/CityCrudJs.js"></script>




﻿$(document).ready(function () {
    $("#CarDriverManager").parent().parent().find("a").next().slideToggle(500);
    $("#CarDriverManager").parent().parent().toggleClass('toggled');
    $("#CarDriverManager").find("a").addClass("selectedMenu");
    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

    });
    $("#btnSave").click(function () {
        if (IsValidate()) {
            var formData = new FormData($("#idFormCarDriverAdd")[0]);
            $.ajax({
                url: 'AddCarDriver',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Driver Informations Successfully Saved!", "Driver Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });
    $("#btnUpdate").click(function () {
        if (IsValidate(true)) {
            var formData = new FormData($("#idFormCarDriverAdd")[0]);
            $.ajax({
                url: 'CarDriverManagerEdit',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Driver Informations Successfully Updated!", "Driver Update");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });
    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
    $("#Vendor_PKID").change(function () {
        BindCarOwner(0);
        $("#Car_Owner_PKID").val($("#Car_Owner_PKID option:first").val());
        $("#Car_PKID").val($("#Car_PKID option:first").val());
    });
    $("#Car_Owner_PKID").change(function () {
        BindCar(0);
        $("#Car_PKID").val($("#Car_PKID option:first").val());
    });
    $("#Driver_Current_Address_State").change(function () {
        GetCityByStateCurrent(0);
    });
    $("#Driver_Permanent_Address_State").change(function () {
        GetCityByStateParmanent(0);
    });
    $("#Driver_Current_Permanent_Same1").change(function () {
        $("#Driver_Permanent_Address1").val($("#Driver_Current_Address1").val());
        $("#Driver_Permanent_Address2").val($("#Driver_Current_Address2").val());
        $("#Driver_Permanent_Address_State").val($("#Driver_Current_Address_State").val());
        GetCityByStateParmanent($("#Driver_Current_Address_City").val());

        $("#Driver_Permanent_Address1").attr("readonly", "true");
        $("#Driver_Permanent_Address2").attr("readonly", "true");
        $("#Driver_Permanent_Address_State").attr("readonly", "true");
        $("#Driver_Permanent_Address_City").attr("readonly", "true");
    });
    $("#Driver_Current_Permanent_Same2").change(function () {
        $("#Driver_Permanent_Address1").val('');
        $("#Driver_Permanent_Address2").val('');
        $("#Driver_Permanent_Address_State").val('');
        $("#Driver_Permanent_Address_City").empty();
        $("#Driver_Permanent_Address_City").append('<option value=>-- Select City --</option>');
        $("#Driver_Permanent_Address1").removeAttr("readonly");
        $("#Driver_Permanent_Address2").removeAttr("readonly");
        $("#Driver_Permanent_Address_State").removeAttr("readonly");
        $("#Driver_Permanent_Address_City").removeAttr("readonly");
    });

});

function Edit(Id) {
    $.ajax({
        url: 'CarDriverManagerEdit',
        type: "GET",
        data: { pkid: Id },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                // fill data 
                //hidden fields
                $("#PKID").val(data.PKID);
                $("#Is_Active").val(true);
                //end hidden fields
                // texboxes and dropdowns
                $("#Vendor_PKID").val(data.Vendor_PKID);
                BindCarOwner(data.Car_Owner_PKID, data.Car_PKID);
                $("#Driver_Name").val(data.Driver_Name);
                var driverDob = new Date(parseInt(data.Driver_DOB.substr(6)));
                if (Date.parse(driverDob)) {
                    $("#Driver_DOB").val(driverDob.getFullYear() + "-" + ("0" + (driverDob.getMonth() + 1)).slice(-2) + "-" + ("0" + (driverDob.getDate())).slice(-2));
                }
                $("#Drive_Phone_1").val(data.Drive_Phone_1);
                $("#Drive_Phone_2").val(data.Drive_Phone_2);
                $("#Driver_DL").val(data.Driver_DL);
                $("#Driver_Aadhaar").val(data.Driver_Aadhaar);
                $("#Driver_Father_Name").val(data.Driver_Father_Name);
                $("#Driver_Mother_Name").val(data.Driver_Mother_Name);
                $("#Driver_Email").val(data.Driver_Email);
                $('#Driver_Religion option:contains("' + data.Driver_Religion + '")').prop('selected', true)
                $("#Special_Remarking").val(data.Special_Remarking);
                $("#Driver_Current_Address1").val(data.Driver_Current_Address1);
                $("#Driver_Current_Address2").val(data.Driver_Current_Address2);
                $("#Driver_Current_Address_State").val(data.Driver_Current_Address_State);
                GetCityByStateCurrent(data.Driver_Current_Address_City);
                $("#Driver_Permanent_Address1").val(data.Driver_Permanent_Address1);
                $("#Driver_Permanent_Address2").val(data.Driver_Permanent_Address2);
                $("#Driver_Permanent_Address_State").val(data.Driver_Permanent_Address_State);
                GetCityByStateParmanent(data.Driver_Permanent_Address_City);
                $("#Driver_Photo_EditFile").val(data.Driver_Photo);
                $("#Driver_Photo_View").fadeIn("fast").attr('src', "/Docs/DriverPhoto/" + data.Driver_Photo);
                // end texboxes and dropdowns
                // checkboxes
                if (data.Driver_Gender == "male") $("#Driver_Gender1").prop("checked", true);
                else $("#Driver_Gender2").prop("checked", true);

                if (data.Driver_Marrital_Status == "Married") $("#Driver_Marrital_Status1").prop("checked", true);
                else $("#Driver_Marrital_Status2").prop("checked", true);

                if (data.Driver_Smoking_Status == "Smoker") $("#Driver_Drinking_Status1").prop("checked", true);
                else $("#Driver_Drinking_Status2").prop("checked", true);

                if (data.Driver_Drinking_Status == "Drinker") $("#Driver_Smoking_Status1").prop("checked", true);
                else $("#Driver_Smoking_Status2").prop("checked", true);

                if (data.Driver_Eating_Type == "Vegetarian") $("#Driver_Eating_Type1").prop("checked", true);
                else $("#Driver_Eating_Type2").prop("checked", true);

                if (data.Phone_1_IsWhatsup == "Yes") $("#Phone_1_IsWhatsup1").prop("checked", true);
                else $("#Phone_1_IsWhatsup2").prop("checked", true);

                if (data.Driver_Current_Permanent_Same) $("#Driver_Current_Permanent_Same1").prop("checked", true);
                else $("#Driver_Current_Permanent_Same2").prop("checked", true);
                //end checkboxes
                // set default values
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}

function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#Car_Owner_PKID").val($("#Car_Owner_PKID_" + Id).val());
    $("#Car_PKID").val($("#Car_PKID_" + Id).val());

    $("#Special_Remarking").val($("#Driver_Special_Remarking_" + Id).val());
    $("#Driver_Name").val($("#Driver_Name_" + Id).val());
    $("#Driver_DOB").val($("#Driver_DOB_" + Id).val());
    $("#Drive_Phone_1").val($("#Drive_Phone_1_" + Id).val());
    $("#Drive_Phone_2").val($("#Drive_Phone_2_" + Id).val());
    $("#Driver_DL").val($("#Driver_DL_" + Id).val());
    $("#Driver_Aadhaar").val($("#Driver_Aadhaar_" + Id).val());
    $("#Driver_Father_Name").val($("#Driver_Father_Name_" + Id).val());
    $("#Driver_Mother_Name").val($("#Driver_Mother_Name_" + Id).val());

    if ($("#Driver_Marrital_Status_" + Id).val() == "Married")
        $("#Driver_Marrital_Status1").prop("checked", true);
    else
        $("#Driver_Marrital_Status2").prop("checked", true);

    if ($("#Driver_Gender_" + Id).val() == "Male")
        $("#Driver_Gender1").prop("checked", true);
    else
        $("#Driver_Gender2").prop("checked", true);

    if ($("#Driver_Smoking_Status_" + Id).val() == "Smoker")
        $("#Driver_Smoking_Status").prop("checked", true);
    else
        $("#Driver_Smoking_Status").prop("checked", true);

    if ($("#Driver_Drinking_Status_" + Id).val() == "Drinker")
        $("#Driver_Drinking_Status").prop("checked", true);
    else
        $("#Driver_Drinking_Status").prop("checked", true);

    if ($("#Driver_Eating_Type_" + Id).val() == "Vegetarian")
        $("#Driver_Eating_Type").prop("checked", true);
    else
        $("#Driver_Eating_Type").prop("checked", true);


    //$("#Driver_Current_Address").val($("#Driver_Current_Address_" + Id).val());
    //$("#Driver_Permanent_Address").val($("#Driver_Permanent_Address_" + Id).val());

    $("#Driver_Photo_EditFile").val($("#Driver_Photo_" + Id).val());
    $("#Driver_Photo_View").fadeIn("fast").attr('src', "/Docs/DriverPhoto/" + $("#Driver_Photo_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {


        $.ajax({
            url: 'AddCarDriver',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            contentType: false,
            enctype: 'multipart/form-data',
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Driver Successfully Deleted!", "Driver Deleted");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'CarDriverActivateDeactivated',
            type: "POST",
            data: { pkid: Id },
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Driver Status Successfully Updated!", "Driver Status Update");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error"); 
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}

function resetForm() {
    $("#Vendor_PKID").val($("#Vendor_PKID option:first").val());
    $("#Car_Owner_PKID").val($("#Car_Owner_PKID option:first").val());
    $("#Car_PKID").val($("#Car_PKID option:first").val());
    $("#Driver_Name").val('');
    $("#Driver_Photo").val('');
    $("#Vendor_Member_ID").val('');
    $("#Drive_Phone_1").val('');
    $("#Drive_Phone_2").val('');
    $("#Driver_DOB").val('');
    $("#Driver_DL").val('');
    $("#Driver_Aadhaar").val('');
    $("#Driver_Father_Name").val('');
    $("#Driver_Mother_Name").val('');
}

function IsValidate(IsEdit) {
    if ($("#Vendor_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Vendor name!", "Required!");
        return false;
    }
    if ($("#Car_Owner_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Owner name!", "Required!");
        return false;
    }
    if ($("#Car_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car!", "Required!");
        return false;
    }
    if ($("#Driver_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver name!", "Required!");
        return false;
    }
    if ($("#Driver_DOB").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver DOB!", "Required!");
        return false;
    }
    if ($("#Driver_DOB").val() != "") {
        var Driver_DOB = $("#Driver_DOB").val();
        var Driver_DOB = Driver_DOB.split("/").reverse().join("-");
        var Driver_DOB = new Date(Driver_DOB).getFullYear();
        var date = new Date().getFullYear() - 18;

        if (Driver_DOB > date) {
            toastr.clear();
            toastr.error("Driver age must be more then 18 Year,Please change DOB", "Required!");
            return false;
        }
    }
    if (IsEdit == null) {
        if ($("#Driver_Photo").val() == "") {
            toastr.clear();
            toastr.error("Please enter Driver Photo !", "Required!");
            return false;
        }
    }
    if ($("#Drive_Phone_1").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Drive Phone 1 !", "Required!");
        return false;
    }
    if ($("#Driver_DL").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver DL !", "Required!");
        return false;
    }
    if ($("#Driver_Aadhaar").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Aadhaar !", "Required!");
        return false;
    }
    if ($("#Driver_Father_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Father Name !", "Required!");
        return false;
    }
    if ($("#Driver_Mother_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Mother Name !", "Required!");
        return false;
    }
    if ($("#Driver_Current_Address").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Current Address !", "Required!");
        return false;
    }
    if ($("#Driver_Permanent_Address").val() == "") {
        toastr.clear();
        toastr.error("Please enter Driver Permanent Address !", "Required!");
        return false;
    }
    if (!$("input[type=radio][name=Driver_Gender]").is(':checked')) {
        toastr.clear();
        toastr.error("Please select driver gender !", "Required!");
        return false;
    }
    if (!$("input[type=radio][name=Driver_Marrital_Status]").is(':checked')) {
        toastr.clear();
        toastr.error("Please select driver marrital status !", "Required!");
        return false;
    }
    if (!$("input[type=radio][name=Driver_Drinking_Status]").is(':checked')) {
        toastr.clear();
        toastr.error("Please select driver drinking status !", "Required!");
        return false;
    }
    if (!$("input[type=radio][name=Driver_Smoking_Status]").is(':checked')) {
        toastr.clear();
        toastr.error("Please select driver smoking status !", "Required!");
        return false;
    }
    if (!$("input[type=radio][name=Driver_Eating_Type]").is(':checked')) {
        toastr.clear();
        toastr.error("Please select driver eating type !", "Required!");
        return false;
    }
    if ($("#Driver_Current_Address1").val().length < 5) {
        toastr.clear();
        toastr.error("Please enter valid address 1 !", "Required!");
        return false;
    }
    if ($("#Driver_Current_Address2").val().length < 5) {
        toastr.clear();
        toastr.error("Please enter valid address 2 !", "Required!");
        return false;
    }
    if ($("#Driver_Current_Address_State option:selected").text() == "-- select State --" || $("#Driver_Current_Address_State option:selected").val() == "") {
        toastr.clear();
        toastr.error("Please select current address state !", "Required!");
        return false;
    }
    if ($("#Driver_Current_Address_City option:selected").text() == "-- Select City --" || $("#Driver_Current_Address_City option:selected").val() == "") {
        toastr.clear();
        toastr.error("Please select current address city !", "Required!");
        return false;
    }
    if ($("#Driver_Permanent_Address1").val().length < 5) {
        toastr.clear();
        toastr.error("Please enter valid Permanent address 1!", "Required!");
        return false;
    }
    if ($("#Driver_Permanent_Address2").val().length < 5) {
        toastr.clear();
        toastr.error("Please enter valid Permanent address 2!", "Required!");
        return false;
    }
    if ($("#Driver_Permanent_Address_State option:selected").text() == "-- select State --" || $("#Driver_Permanent_Address_State option:selected").val() == "") {
        toastr.clear();
        toastr.error("Please select Permanent address state !", "Required!");
        return false;
    }
    if ($("#Driver_Permanent_Address_City option:selected").text() == "-- Select City --" || $("#Driver_Current_Address_City option:selected").val() == "") {
        toastr.clear();
        toastr.error("Please select Permanent address city !", "Required!");
        return false;
    }
    if ($("#Special_Remarking").val().length < 2) {
        toastr.clear();
        toastr.error("Please enter valid remarking for driver!", "Required!");
        return false;
    }
    if (!PhotoCheck(document.getElementById("Driver_Photo"))) {



    };

    if (IsEdit) {
        var result = PhotoCheck(document.getElementById("Driver_Photo"));
        if (!result) return false;
    }
    else {
        var result = PhotoCheck(document.getElementById("Driver_Photo"));
        if (!result || result == "no-file") return false;
    }

    return true;
}

function myFunction() {
    var x = document.getElementById("Vendor_PWD");
    if (x.type === "password") {
        x.type = "text";
    } else {
        x.type = "password";
    }
}

$("#Driver_Photo").change(function () {
    if (PhotoCheck(document.getElementById("Driver_Photo"))) {
        $("#Driver_Photo_View").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
    }
});

function PhotoCheck(input) {
    if (input.files.length > 0) {
        for (var i = 0; i < input.files.length; i++) {
            var file = input.files[i];
            var fileType = file["type"];
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            if ($.inArray(fileType, validImageTypes) < 0) {
                toastr.clear();
                toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
                return false;
            }
            else return true;
        }
    }
    else return "no-file";
};

function BindCarOwner(selectCarOwnerId, selectedCarId) {
    $("#Car_Owner_PKID").empty();
    var Id = $("#Vendor_PKID").val();
    $.ajax({
        type: 'POST',
        url: 'GetOwner', // Calling json method  
        dataType: 'json',
        data: { id: Id },
        success: function (CarOwner) {
            $("#Car_Owner_PKID").append('<option value=>-- Select Car Owner --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Car_Owner_PKID").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectCarOwnerId != 0) {
                $("#Car_Owner_PKID").val(selectCarOwnerId);
                if (selectedCarId != null && selectedCarId !== undefined) BindCar(selectedCarId);
            }
        }
    });
    return false;
}

function BindCar(selectedCarId) {
    $("#Car_PKID").empty();
    var Id = $("#Car_Owner_PKID").val();
    $.ajax({
        type: 'POST',
        url: 'GetCarByOwner', // Calling json method  
        dataType: 'json',
        data: { id: Id },
        success: function (Car) {
            $("#Car_PKID").append('<option value=>-- Select Car --</option>');
            $.each(Car, function (i, Car) {
                $("#Car_PKID").append('<option value="' + Car.Value + '">' +
                    Car.Text + '</option>');
            });
            if (selectedCarId != 0) $("#Car_PKID").val(selectedCarId);
        }
    });
    return false;
}

function GetCityByStateCurrent(selectId) {
    $("#Driver_Current_Address_City").empty();
    $.ajax({
        type: 'POST',
        url: 'GetCityByState', // Calling json method  
        dataType: 'json',
        data: { id: $("#Driver_Current_Address_State").val() },
        success: function (CarOwner) {
            $("#Driver_Current_Address_City").append('<option value=>-- Select City --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Driver_Current_Address_City").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0)
                $("#Driver_Current_Address_City").val(selectId);

        }
    });
    return false;
}

function GetCityByStateParmanent(selectId) {
    $("#Driver_Permanent_Address_City").empty();
    $.ajax({
        type: 'POST',
        url: 'GetCityByState', // Calling json method  
        dataType: 'json',
        data: { id: $("#Driver_Permanent_Address_State").val() },
        success: function (CarOwner) {
            $("#Driver_Permanent_Address_City").append('<option value=>-- Select City --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Driver_Permanent_Address_City").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0)
                $("#Driver_Permanent_Address_City").val(selectId);

        }
    });
    return false;
}
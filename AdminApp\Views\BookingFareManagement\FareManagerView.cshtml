﻿@model DBLinker.Lib.Model.M_BookingFareList
@using AdminApp.Mapper;

@{
    ViewBag.Title = "Fare Manager View";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $("#FareManager").parent().parent().find("a").next().slideToggle(500);
        $("#FareManager").parent().parent().toggleClass('toggled');
        $("#FareManager").find("a").addClass("selectedMenu");
    });


</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">View Fare Manager Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../BookingFareManagement/FareManager">Fare Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Fare Manager Details</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Fare Manager View</header>
                       
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.City_From, new { @class = "mdl-textfield__input"})

                                <label class="mdl-textfield__label">City From</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.City_To, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">City To</label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Trip_Type, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Trip Type</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Category, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">CAR Category</label>
                            </div>
                        </div>

                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Basic_Fare, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Basic Fare</label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Charge, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Driver Charge</label>
                            </div>
                        </div>

                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Toll_Charge, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Toll Charge</label>
                            </div>
                        </div>

                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                @Html.TextBoxFor(x => x.Total_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                <label class="mdl-textfield__label">Total Fare</label>
                            </div>
                        </div>



                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.GST, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> GST(%)</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                @Html.TextBoxFor(x => x.GST_Amount, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                <label class="mdl-textfield__label"> GST Amount</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                @Html.TextBoxFor(x => x.Final_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                <label class="mdl-textfield__label"> Final Fare</label>
                            </div>
                        </div>
                        <div class="col-lg-12 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                @Html.TextBoxFor(x => x.Remark, new { @class = "mdl-textfield__input", Style = "color:black;" })
                                <label class="mdl-textfield__label"> Remark</label>
                            </div>
                        </div>
                       
                    </div>



                </div>
            </div>
        </div>




    </div>
</div>


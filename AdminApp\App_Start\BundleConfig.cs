﻿using System.Web;
using System.Web.Optimization;

namespace AdminApp
{
    public class BundleConfig
    {
        // For more information on bundling, visit https://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            //bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
            //            "~/Scripts/jquery-{version}.js"));

            //bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
            //            "~/Scripts/jquery.validate*"));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at https://modernizr.com to pick only the tests you need.
            //bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
            //            "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                          "~/Content/assets/js/common.min.js",
                          "~/Content/assets/js/uikit_custom.min.js",
                          "~/Content/assets/js/altair_admin_common.min.js",
                          "~/Content/bower_components/ckeditor/ckeditor.js",
                          "~/Content/bower_components/ckeditor/adapters/jquery.js",
                          "~/Content/assets/js/pages/forms_wysiwyg.min.js",
                          "~/Content/bower_components/datatables/media/js/jquery.dataTables.min.js",
                          "~/Content/bower_components/datatables-buttons/js/dataTables.buttons.js",

                          "~/Content/assets/js/custom/datatables/buttons.uikit.js",
                          "~/Content/bower_components/jszip/dist/jszip.min.js",
                          "~/Content/bower_components/pdfmake/build/pdfmake.min.js",
                          "~/Content/bower_components/pdfmake/build/vfs_fonts.js",
                          "~/Content/bower_components/datatables-buttons/js/buttons.colVis.js",
                          "~/Content/bower_components/datatables-buttons/js/buttons.html5.js",

                          "~/Content/assets/js/custom/datatables/datatables.uikit.min.js",
                          "~/Content/assets/js/pages/plugins_datatables.min.js",
                          "~/Content/bower_components/parsleyjs/dist/parsley.min.js",

                                "~/Content/assets/js/custom/wizard_steps.min.js",
                                      "~/Content/assets/js/pages/forms_wizard.min.js"
                          ));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bower_components/weather-icons/css/weather-icons.min.css",
                      "~/Content/bower_components/metrics-graphics/dist/metricsgraphics.css",
                      "~/Content/bower_components/chartist/dist/chartist.min.css",
                       "~/Content/bower_components/uikit/css/uikit.almost-flat.min.css",
                       "~/Content/assets/icons/flags/flags.min.css",
                        "~/Content/assets/css/main.min.css",
                         "~/Content/assets/css/themes/themes_combined.min.css",
                          "~/Content/assets/css/login_page.min.css"
                         ));
        }
    }
}

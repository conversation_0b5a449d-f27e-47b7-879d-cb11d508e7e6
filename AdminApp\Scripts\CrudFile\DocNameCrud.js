﻿$(document).ready(function () {
    $("#DocumentName").parent().parent().find("a").next().slideToggle(500);
    $("#DocumentName").parent().parent().toggleClass('toggled');
    $("#DocumentName").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();

    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'DocumentName',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'DocumentName',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#Doc_For").val($("#Doc_For_" + Id).val());
    $("#Doc_Name").val($("#name_" + Id).val());


    if ($("#Is_Doc_Req_" + Id).val() == false) {      
        $("#Is_Doc_Req2").prop("checked", true);
    }
    else {
        $("#Is_Doc_Req1").prop("checked", true);
    }

    if ($("#Is_Doc_Expiry_Date_" + Id).val() == false)
        $("#Is_Doc_Expiry_Date2").prop("checked", true);
    else
        $("#Is_Doc_Expiry_Date1").prop("checked", true);

    $("#Is_Active").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}

function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#Doc_For").val($("#Doc_For_" + Id).val());
    $("#Doc_Name").val($("#name_" + Id).val());
    $("#IsActive").val($("#status_" + Id).val());


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'DocumentName',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    $("#PKID").val('');
    $("#Doc_For").val($("#Doc_For_" + Id).val());
    $("#Doc_Name").val($("#name_" + Id).val());
    if ($("#Is_Doc_Req_" + Id).val() == false) {
        $("#Is_Doc_Req2").prop("checked", true);
    }
    else {
        $("#Is_Doc_Req1").prop("checked", true);
    }

    if ($("#Is_Doc_Expiry_Date_" + Id).val() == false)
        $("#Is_Doc_Expiry_Date2").prop("checked", true);
    else
        $("#Is_Doc_Expiry_Date1").prop("checked", true);


    $("#Is_Active").val($("#status_" + Id).val());
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'DocumentName',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {
    $("#Doc_For").val('');
    $("#Doc_Name").val('');


}

function IsValidate() {

    
    if ($("#Doc_For").val() == "") {
        toastr.clear();
        toastr.error("Please select document for!");
        return false;
    }
    if ($("#Doc_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter document name !");
        return false;
    }
    /*Repeat Validation*/
    if ($("#Doc_Name").val() != "") {
        if ($("#Doc_Name-error").html() !== undefined) {
            toastr.clear();
            toastr.error("document Name already exists!", "Required!");
            return false;
        }
    }
    if ($("#Is_Doc_Req1").prop('checked') != true && $("#Is_Doc_Req2").prop('checked') != true) {
        toastr.clear();
        toastr.error("Please check document required?");
        return false;
    }
    if ($("#Is_Doc_Expiry_Date1").prop('checked') != true && $("#Is_Doc_Expiry_Date2").prop('checked') != true) {
        toastr.clear();
        toastr.error("Please select document have expiry date!");
        return false;
    }
    return true;
}
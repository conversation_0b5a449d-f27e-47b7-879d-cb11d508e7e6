﻿@model DBLinker.Lib.Model.M_Vendor_Bank_Details

@{
    ViewBag.Title = "Vendor Bank Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Vendor Bank Details</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/VendorManager">Vendor Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Vendor Bank Details</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.Is_active)
        @Html.HiddenFor(x => x.PKID)
        @Html.HiddenFor(x => x.Vendor_Ref_PKID)
        <div class="row" id="dvAddUpdate">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Add @Model.Vendor_Company_Name Bank Details</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                            data-mdl-for="panel-button">
                            <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                            <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                            <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                        </ul>
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @if (ViewBag.BankList != null)
                            {

                                @Html.DropDownListFor(x => x.Bank_Name_PKID, new SelectList(ViewBag.BankList, "PKID", "Bank_Name"), "-- Select Bank --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                        }
                                <label class="mdl-textfield__label required"><span>Bank Name</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Account_Number, new { @class = "mdl-textfield__input" })
                                @Html.ValidationMessageFor(model => model.Account_Number, "", new { @class = "text-danger" })
                                <label class="mdl-textfield__label required"><span>Account Number</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.IFSC_CODE, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>IFSC CODE</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Account_Holder_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label required"><span>Account Holder Name</span></label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <div class="mdl-textfield__input">
                                    <div style="float:left;width:30%">
                                        @Html.RadioButtonFor(x => x.Bank_Status, true, new { @id = "Bank_Status1" })
                                        <label for="radio1"><span><span></span></span> Primary</label>
                                    </div>
                                    <div style="float:left;width:50%">
                                        @Html.RadioButtonFor(x => x.Bank_Status, false, new { @id = "Bank_Status2" })
                                        <label for="radio2"><span><span></span></span>Secondary</label>
                                    </div>

                                    <div style="clear:both;"></div>

                                </div>

                                <label class="mdl-textfield__label required"><span>Bank Status</span> </label>
                            </div>
                        </div>

                        <div class="col-lg-12 p-t-20 text-center">
                            @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                            @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))
                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
}
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>@Model.Vendor_Company_Name  Bank List</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            @*<div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>*@
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>

                                        <th>Bank Name</th>
                                        <th>Account Number</th>
                                        <th>IFSC CODE</th>
                                        <th>Account Holder Name</th>
                                        <th>Bank Status</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (ViewBag.VendorBankList != null)
                                {
                                    foreach (var item in ViewBag.VendorBankList)
                                    {

                                        <tr class="odd gradeX">
                                            <input type="hidden" id="<EMAIL>" value="@item.Bank_Name_PKID" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Bank_Name" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Account_Number" />
                                            <input type="hidden" id="<EMAIL>" value="@item.IFSC_CODE" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Account_Holder_Name" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Bank_Status.ToString()" />
                                            <input type="hidden" id="<EMAIL>" value="@item.Is_active" />


                                            <td>@item.Bank_Name</td>
                                            <td>@item.Account_Number</td>
                                            <td>@item.IFSC_CODE</td>
                                            <td>@item.Account_Holder_Name</td>
                                            <td>
                                                @{
                                                if (item.Bank_Status != null && item.Bank_Status != false)
                                                {
                                                    <span>  Primary</span>
                                            }
                                            else
                                            {
                                                <span> Secondary </span>
                                        }
                                                }

                                            </td>

                                            <td>
                                                @{
                                                if (item.Is_active != false)
                                                {
                                                    <span class="label label-sm label-success"> Active </span>
                                            }
                                            else
                                            {
                                                <span class="label label-sm label-danger"> In-Active </span>
                                        }
                                                }
                                            </td>

                                            <td align="center">
                                                <a class="btn btn-tbl-edit btn-xs" onclick="ActivateDeactivate(@item.PKID)" title="Enable">
                                                    <i class="fa fa-check"></i>
                                                </a>
                                                <a class="btn btn-tbl-delete btn-xs" onclick="Edit(@item.PKID)" title="Edit">
                                                    <i class="fa fa-pencil"></i>
                                                </a>



                                            </td>



                                        </tr>
                                }

                            }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" style="text-align:center;">
            <div class="col-md-12">
                <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/VendorManager">Back</a>
            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/CrudFile/VendorBankCrud.js"></script>


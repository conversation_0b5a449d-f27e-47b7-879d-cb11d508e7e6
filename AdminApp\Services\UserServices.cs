﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using AdminApp.Filters;
namespace AdminApp.Services
{
    public class UserServices
    {
        public static int getCurrentUserId()
        {
            if (HttpContext.Current.User != null)
            {
                var user = HttpContext.Current.User as CustomPrincipal;
                if (user != null)
                {
                    return user.userId;
                }
            }
            return 0;
        }

        public static string getCurrentUserEmail()
        {
            if (HttpContext.Current.User != null)
            {
                var user = HttpContext.Current.User as CustomPrincipal;
                if (user != null)
                {
                    return user.UserEmailID;
                }
            }
            return "";
        }
    }
}
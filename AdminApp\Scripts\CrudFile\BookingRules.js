﻿$(document).ready(function () {
    $("#BookingRulesManager").parent().parent().find("a").next().slideToggle(500);
    $("#BookingRulesManager").parent().parent().toggleClass('toggled');
    $("#BookingRulesManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();

    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    }); 

    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'AddBookingRules',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        window.setTimeout(function () {
                            location.reload()
                        }, 1000)
                    }
                    else {
                        toastr.error("Sorry No Record Saved, Please Try it Again!", "Bokking Rule"); 
                    }
                },
                failure: function (response) {
                    toastr.error(response.responseText, "Bokking Rule");
                },
                error: function (response) {
                    toastr.error(response.responseText, "Bokking Rule");
                }
            }); 
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {
            $.ajax({
                url: 'EditBookingRules',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        window.setTimeout(function () {
                            location.reload()
                        }, 1000)
                    }
                    else {
                        toastr.error("Sorry No Record Updated, Please Try it Again!", "Bokking Rule");
                    }
                },
                failure: function (response) {
                    toastr.error(response.responseText, "Bokking Rule");
                },
                error: function (response) {
                    toastr.error(response.responseText, "Bokking Rule");
                }
            }); 
        }
    });

    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
});

function Edit(Id) {
    $.ajax({
        url: 'EditBookingRules',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                $("#PKID").val(data.PKID);
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                // fill data   
                $("#Trip_Type_Id").val(data.Trip_Type_Id);
                $("#Car_Category_Id").val(data.Car_Category_Id);
                $("#Distance_From").val(data.Distance_From);
                $("#Distance_To").val(data.Distance_To);
                $("#NewDistance").val(data.NewDistance);
                $("#Is_Active").val(data.Is_Active);
                // set default values 
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            } else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}

function ActivateDeactivate(Id) {
    $.ajax({
        url: 'ActiveDeactiveBookingRules',
        type: "POST",
        data: { id: Id },
        dataType: "json",
        success: function (response) {
            if (response > 0) {
                toastr.success("Bokking Rule Status successfully Changed!", "Bokking Rule");
                window.setTimeout(function () {
                    location.reload()
                }, 1000)
            }
            else {
                toastr.error("Sorry Status Not Updated,Please Try it Again !", "Bokking Rule");

            }
        },
        failure: function (response) {
            toastr.error(response.responseText, "Bokking Rule");
        },
        error: function (response) {
            toastr.error(response.responseText, "Bokking Rule");
        }
    });
}

function resetForm() {
    $("#Car_Category_Id").val('');
    $("#Trip_Type_Id").val('');
    $("#Distance_From").val('');
    $("#Distance_To").val('');
    $("#NewDistance").val('');
}

function IsValidate() {
    if ($("#Car_Category_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Category !");
        return false;
    }
    if ($("#Trip_Type_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select Trip Type !");
        return false;
    }

    if ($("#Distance_From").val() == "") {
        toastr.clear();
        toastr.error("Please enter Distance From !");
        return false;
    }
    if ($("#Distance_To").val() == "") {
        toastr.clear();
        toastr.error("Please enter Distance To !");
        return false;
    }
    if ($("#NewDistance").val() == "") {
        toastr.clear();
        toastr.error("Please enter New Distance !");
        return false;
    }

    return true;
}
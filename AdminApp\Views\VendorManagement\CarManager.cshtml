﻿@model DBLinker.Lib.Model.M_Car_Details
@using AdminApp.Mapper;

@{
    ViewBag.Title = "Car Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Car Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add Car</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddCar", "VendorManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "idFormCarAdd" }))
        {

            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Add Car</header>
                            <button id="panel-button"
                                    class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                    data-upgraded=",MaterialButton">
                                <i class="material-icons">more_vert</i>
                            </button>
                            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                                data-mdl-for="panel-button">
                                <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                                <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                                <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                            </ul>
                        </div>
                        <div class="card-body row">


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.vendorList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Vendor_PKID, new SelectList(ViewBag.vendorList, "PKID", "Vendor_Company_Name"), "-- Select Vendor --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Vendor Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.DropDownListFor(x => x.Car_Owner_PKID, new SelectList(string.Empty, "Value", "Text"), "-- Select Car Owner --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })

                                    <label class="mdl-textfield__label required"><span>Car Owner</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Car_Number, new { @class = "mdl-textfield__input", @maxlength = "15" })
                                    @Html.ValidationMessageFor(model => model.Car_Number, "", new { @class = "text-danger" })
                                    <label class="mdl-textfield__label required"><span>Car Number</span> </label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Car_Purchase_Year, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label required"><span>Car Purchase Year</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CarCompanyList != null)
                                    {
                                        @Html.DropDownListFor(x => x.Car_Company_PKID, new SelectList(ViewBag.CarCompanyList, "PKID", "Company_Name"), "-- Select Car Company Name --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Car Comapny</span> </label>
                                </div>
                            </div>


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CarModelList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Model_PKID, new SelectList(ViewBag.CarModelList, "PKID", "Car_Model_Name"), "-- Select Car Model --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"> <span>Car Model</span></label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Registered_City_PKID, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Car Registered City</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Car_Manufacturing_Year, new { @class = "mdl-textfield__input txtbx", @maxlength = "4" })
                                    <label class="mdl-textfield__label required"><span>Car Manufacturing Year</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <div class="mdl-textfield__input">
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "CNG", new { @id = "Car_Fuel_Type_Status1" })
                                            <label for="radio1"><span><span></span></span> CNG</label>
                                        </div>
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "Petrol", new { @id = "Car_Fuel_Type_Status2" })
                                            <label for="radio2"><span><span></span></span>Petrol</label>
                                        </div>
                                        <div style="float:left;width:30%">
                                            @Html.RadioButtonFor(x => x.Car_Fuel_Type_Status, "Diesel", new { @id = "Car_Fuel_Type_Status3" })
                                            <label for="radio2"><span><span></span></span>Diesel</label>
                                        </div>

                                        <div style="clear:both;"></div>

                                        <label class="mdl-textfield__label required"><span>Car Fule Type</span></label>
                                    </div>
                                </div>
                            </div>



                            <div class="col-lg-12 p-t-20 text-center">
                                @(PermittedAction.Is_Add ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw(""))
                                @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))


                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        }
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Car</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='#' id='btnAdd' class='btn btn-info'>Add New<i class='fa fa-plus'></i></a>") : Html.Raw(""))
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th width="20%">Vendor</th>
                                        <th width="15%">Owner</th>
                                        <th width="10%">Car Number</th>
                                        <th width="10%">Car Comapny</th>
                                        <th width="15%">Car Model</th>
                                        <th width="10%">Fule Type</th>
                                        <th width="10%">Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.CarList != null)
                                    {
                                        foreach (var item in ViewBag.CarList)
                                        {
                                            <tr class="odd gradeX">
                                                <td>@item.Vendor_Name</td>
                                                <td>@item.Car_Owner_Name</td>
                                                <td>@item.Car_Number</td>
                                                <td>@item.Company_Name</td>
                                                <td>@item.Car_Model_Name</td>
                                                <td>@item.Car_Fuel_Type_Status</td>
                                                <td>
                                                    @Html.Raw(item.Is_Active == null
                                                ? "<span class='label label-sm label-warning'> Not Set </span>" : item.Is_Active == true
                                                ? "<span class='label label-sm label-success'> Active </span>"
                                                : "<span class='label label-sm label-danger'> InActive </span>")
                                                   
                                                </td>


                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID+ ")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Add && PermittedAction.Is_Edit? Html.Raw("<a href='../VendorManagement/CarDocs?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Car") + "' title='Upload Documents'><i class='material-icons'>attachment</i> Upload Docs</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit(" + item.PKID + ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw(""))
                                                            </li>
                                                            <li>
                                                                @(PermittedAction.Is_View ? Html.Raw("<a href='../VendorManagement/CarView?id=" + QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Car") + "' title='View Vendor Info'><i class='material-icons'>remove_red_eye</i> View</a>") : Html.Raw("")) 
                                                            </li>

                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>

<script src="~/Scripts/CrudFile/CarDetailsCrud.js"></script>


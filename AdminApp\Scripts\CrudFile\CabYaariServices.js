﻿$("#dvAddUpdate").slideUp();
setString();
$(document).ready(function () {
    FileUploader();
    //Events
    $("#ServiceImagePath").change(function () {
        if (PhotoCheck(document.getElementById("ServiceImagePath"))) {
            $("#img-viwer").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
            FileUploader();
        }
    });
    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });
    $("#btnSave").click(function () {
        if (ValidateForm(false)) {
            saveData();
        }
    });
    $("#btnUpdate").click(function () {
        if (ValidateForm(true)) {
            updateData();
        }
    });
    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
    //End Events
});

function FileUploader() {
    var getImg = $("#img-viwer").attr("src")
    if (getImg == "#") {
        $("#img-viwer").hide();
        $("p.file-uploader-text").show();
    }
    else {
        $("#img-viwer").show();
        $("p.file-uploader-text").hide();
    }
}
function PhotoCheck(input) {
    if (input.files.length > 0) {
        for (var i = 0; i < input.files.length; i++) {
            var file = input.files[i];
            var fileType = file["type"];
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            if ($.inArray(fileType, validImageTypes) < 0) {
                toastr.clear();
                toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
                return false;
            }
            else return true;
        }
    }
    else return "no-file";
};
function ValidateForm(isEdit) {
    if ($("#ServiceName").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Service Name!");
        return false;
    }
    else if ($("#ServiceIconClass").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Service Class!");
        return false;
    }
    else if ($("#BriefIntroAboutService").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Brief info!");
        return false;
    }
    else if ($("#BriefIntroAboutService").val().length < 10) {
        toastr.clear();
        toastr.error("Please Enter Brief info atleast 10 Character (Maximum allowed 400 Characters.)!");
        return false;
    }
    if (!isEdit) {
        var input = document.getElementById("ServiceImagePath");
        if (input.files.length == 0) {
            toastr.clear();
            toastr.error("Please select Service Image!");
            return false;
        };
    }
    if ($("input[type=radio][name=IsActive]:checked").val() == undefined) {
        toastr.clear();
        toastr.error("Please Set Service to active or Inactive!");
        return false;
    }
    return true;
}
function resetForm() {
    $("#ServiceIconClass").val('');
    $("#ServiceName").val('');
    $("#BriefIntroAboutService").val('');
    $("input[type=radio][name=IsActive]").prop("checked", false);
    $("#img-viwer").hide();
    $("p.file-uploader-text").show();
}
function Edit(Id) {
    $.ajax({
        url: 'EditServices',
        type: "GET",
        data: {
            id: Id
        },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                    // fill data 
                    $("#PKID").val(data.PKID);
                    $("#ServiceName").val(data.ServiceName);
                    $("#ServiceIconClass").val(data.ServiceIconClass);
                    $("#BriefIntroAboutService").val(data.BriefIntroAboutService);
                    if (data.IsActive != null && data.IsActive == true) $("#IsActiveTrue").prop("checked", true);
                    else $("#IsActiveFalse").prop("checked", true);
                    if (data.ServiceImagePath != null && data.ServiceImagePath != "") {
                        $("#img-viwer").attr('src', '../Docs/CabYaariServices/' + data.ServiceImagePath)
                        $("#img-viwer").show();
                        $("p.file-uploader-text").hide();
                    }
                    else {
                        $("#img-viwer").hide();
                        $("p.file-uploader-text").show();
                    }
                    $("#AboutService").val(data.AboutService);
                    //end checkboxes
                    // set default values 
                    $("#btnSave").slideUp();
                    $("#btnUpdate").slideDown();
                    $("#dvAddUpdate").slideDown();
                    $('html, body').animate({
                        scrollTop: $("#dvAddUpdate").offset().top
                    }, 500);
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        }
    });
}
function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'ActiveDeactiveServices',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Service Status successfully Changed!", "Most Favourite Routes");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.error("Sorry Status Not Updated,Please Try it Again !", "Most Favourite Routes");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}
function saveData() {
    var formData = new FormData($("#FormCabYariServices")[0]);
    formData.append("ServiceImagePath", $("#ServiceImagePath").get(0).files[0]);
    $.ajax({
        type: 'POST',
        url: 'Services',
        data: formData,
        contentType: false,
        processData: false,
        success: function (result) {
            if (result) {
                resetForm();
                toastr.clear();
                toastr.success("Cab Yaari Service Added successfully!");
                window.setTimeout(function () { location.reload() }, 1000)
            }
            else {
                toastr.clear();
                toastr.error("Record Not added successfully please try again!");
            }
        },
        onerror: function (error) {
            toastr.clear();
            toastr.error("Somthing went wrong please try again!");
        }
    });
}
function updateData() {
    var formData = new FormData($("#FormCabYariServices")[0]);
    formData.append("ServiceImagePath", $("#ServiceImagePath").get(0).files[0]);
    $.ajax({
        url: 'EditServices',
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
            if (response > 0) {
                toastr.success("Cab Yaari Service Added successfully!", "Service");
                window.setTimeout(function () { location.reload() }, 1000)
            } else {
                toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");
            }
        },
        failure: function (response) {
        },
        error: function (response) {
            alert(response.responseText);
        }
    });


}
function setString() {
    $("td.setString").each(function () {
        var str = $(this).text().replace(/ +(?= )/g, '').match(/.{1,25}/g);
        if (str != null) {
            var result = '';
            var run = str.length > 3 ? 3 : str.length;
            for (var i = 0; i < run; i++) {
                if (i != 2) result += str[i] + '\n';
                else {
                    var finalLine = str[i].match(/.{1,20}/g)
                    result += finalLine[0] + "...";
                }
            }
            if (result != "" && result != undefined) {
                $(this).html(result);
            }
        }
    });
}
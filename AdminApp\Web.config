﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
	<configSections>
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
	</configSections>
	<appSettings>
		<add key="webpages:Version" value="*******"/>
		<add key="webpages:Enabled" value="false"/>
		<add key="ClientValidationEnabled" value="true"/>
		<add key="UnobtrusiveJavaScriptEnabled" value="true"/>
		<!--<add key="MapMyIndia_RestLicenseKey" value="7srsgfqweca3nmjfcsmmieddlygm3mh9" />-->
		<add key="OAuthAPI" value="https://outpost.mapmyindia.com/api/security/oauth/token?"/>
		<add key="MapMyIndia_CityLatLongAPI" value="http://localhost:59876/api/MMIServices/getcity?placeName="/>
		<add key="MapMyIndia_AutoSuggestAPI" value="https://atlas.mapmyindia.com/api/places/search/json?query="/>
		<add key="MapMyIndia_RestLicenseKey" value="lynngnrhhwp9xmnj4qzhccid36clntgl"/>
		<add key="GrantType" value="client_credentials"/>
		<add key="ClientId" value="fZqsZfvayHDeoMKEwvq1oa5lJvOJb0s7ByZio2GKFKR3CHrYqZAlxgSwdVXqfUdAafuZVGYkxNlZ2KTY-0Gw_g=="/>
		<add key="ClientSecret" value="9K_q_9Q2GHP3JWfb2e8giYxXMbpCGf6UHJxp19Tkw4cv8-p5wwzP8amxlGUEKg4-flfQKMyVuMwZ02xgI49Ej71luFNwWORd"/>
		<add key="RestAPIKey" value="lynngnrhhwp9xmnj4qzhccid36clntgl"/>
		<add key="MapAPIKey" value="4f8veyov8dg5y3zfsz84npsqv5nua3rd"/>
		<!-- Google Maps API Configuration -->
		<add key="GoogleMaps_APIKey" value="AIzaSyDM8oBKuplVyAEEzxpWnxaIFQApDzkrn0Q"/>
		<add key="OTPAPI" value="https://api.textlocal.in/send/"/>
		<add key="OTPAPIKey" value="Pvkx1uQ4dc4-7mwQCRHsolxusKZArop6ib7OPOzev5"/>
		<add key="Sender" value="Oneway"/>
		<add key="SMSReplaceString" value="$OTP1"/>
		<add key="SMSReplaceStringBooking" value="$BookingConfirmCustomMessage"/>
		<add key="SMSReplaceStringTripComplete" value="$CompleteCustomMessage"/>
		<add key="SMSReplaceStringAssign" value="$BookingCustomMessage"/>
		<add key="SMSReplaceStringBookingCancelByUser" value="$CustomMessage"/>
		<add key="SMSReplaceStringBookingCustomCommon" value="$CustomCommon"/>
		<add key="SMSReplaceStringBookingCancelByCompany" value="$CancelReasonCustomMessage"/>
		<add key="SmsEnable" value="true"/>
		<add key="urlSms.AuthKey" value="bd937e65947c6aca90ed26ecd15667b9" />
		<add key="urlSms.SenderId" value="OWTCPL" />
		<add key="urlSms.RouteId" value="8" />
		<add key="urlSms.BaseUrl" value="https://msg.icloudsms.com/rest/services/sendSMS/sendGroupSms" />
		<add key="urlSms.Tmid" value="140200000022" />
		<add key="urlSms.ConsentFailoverId" value="30" />
		<add key="urlSms.ContentType" value="english" />
		
		<add key="urlSms.Template.CustomerBookingConfirmed.Message" value="Your Booking is Confirmed! From: {#var#} To: {#var#} Booking Date: {#var#} | Booking Time: {#var#} Pickup Date: {#var#} | Pickup Time: {#var#} Cab: {#var#} Payment: INR{#var#} paid online. Remaining INR{#var#} to be paid to driver. Driver details will be shared shortly. For support, call us at: +91-7414000436 - ONEWAY TRIP CARTS" />
		<add key="urlSms.Template.CustomerBookingConfirmed.TemplateId" value="1707175318368219592" />

		<add key="urlSms.Template.DriverBookingConfirmed.Message" value="New Booking Alert! Name: {#var#}, From: {#var#} To: {#var#}. Fare: INR{#var#}, Paid: INR{#var#}, Booking ID: {#var#} - ONEWAY TRIP CARTS" />
		<add key="urlSms.Template.DriverBookingConfirmed.TemplateId" value="1707175360760463660" />

		<add key="urlSms.Template.CancelBooking.Message" value="Booking Cancelled: Name: {#var#}, Trip: {#var#} to {#var#}, ID: {#var#}" />
		<add key="urlSms.Template.CancelBooking.TemplateId" value="1707175360123456789" />

		<add key="urlSms.Template.CompleteBooking.Message" value="Trip Completed. Booking ID: {#var#}, Fare: INR{#var#}, Distance: {#var#} KM" />
		<add key="urlSms.Template.CompleteBooking.TemplateId" value="1707175360760111222" />

		<add key="urlSms.Template.PaymentLink.Message" value="Hi {#var#}, Thank you for booking your ride with us Trip: {#var#} to {#var#} Pickup Date: {#var#} | Time: {#var#} Cab: {#var#} Total Fare: INR{#var#} Min Booking Amount: INR{#var#} Please make the payment using the link below: {#var#} For any assistance, contact us at +91-7414000436 - ONEWAY TRIP CARTS" />
		<add key="urlSms.Template.PaymentLink.TemplateId" value="1707175318448546581" />


		<!-- PhonePe Payment Gateway Configuration -->
		<add key="PhonePe_MerchantId" value="TEST-M232TTWKCYF8S_25060"/>
		<add key="PhonePe_SaltKey" value="099eb0cd-02cf-4e2a-8aca-3e6c6aff0399"/>
		<add key="PhonePe_SaltIndex" value="1"/>
		<add key="PhonePe_BaseUrl" value="https://api-preprod.phonepe.com/apis/pg-sandbox"/>
		<add key="PhonePe_CallbackUrl" value="http://mypanel.revueleague.com/BookingManagement/PhonePeCallback"/>

		<!-- Security Configuration -->
		<add key="PaymentSecretKey" value="CabYaari_Payment_Secret_Key_2024_Secure"/>

		<!-- Partial Payment Configuration -->
		<add key="PartialPayment_Percentage" value="25"/>
		<add key="PartialPayment_MinimumAmount" value="500"/>
	</appSettings>
	<connectionStrings>
		<add name="RLTDBContext"
			 connectionString="metadata=res://*/RLTCARMODEL.csdl|res://*/RLTCARMODEL.ssdl|res://*/RLTCARMODEL.msl;
                         provider=System.Data.SqlClient;
                         provider connection string=&quot;Data Source=************;
                         Initial Catalog=CabYaari;
                         user id=cabyaari_user;
                         password=7UPXQVGB85rlpQ77rPvPGCxCV9xX66GW;
                         TrustServerCertificate=True;
                         multipleactiveresultsets=True;
                         application name=EntityFramework&quot;"
			 providerName="System.Data.EntityClient" />

		<add name="RLTDBconnection"
			 connectionString="Data Source=************;
                         Initial Catalog=CabYaari;
                         user id=cabyaari_user;
                         password=7UPXQVGB85rlpQ77rPvPGCxCV9xX66GW;
                         TrustServerCertificate=True"
			 providerName="System.Data.SqlClient" />
	</connectionStrings>
	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
	<system.web>
		<!--<httpHandlers>
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
    </httpHandlers>-->
		<compilation debug="true" targetFramework="4.7.2">
			<buildProviders>
				<add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
			</buildProviders>
			<assemblies>
				<!--<add assembly="Microsoft.Build.Framework, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>-->
				<add assembly="System.Management, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
				<add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
			</assemblies>
		</compilation>
		<httpRuntime targetFramework="4.6.1"/>
		<httpHandlers>
			<add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false"/>
		</httpHandlers>
	</system.web>
	<system.webServer>
		<handlers>
			<add name="ReportViewerWebControlHandler" verb="*" path="Reserved.ReportViewerWebControl.axd" preCondition="integratedMode" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
		</handlers>
		<validation validateIntegratedModeConfiguration="false"/>
		<modules runAllManagedModulesForAllRequests="true"/>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="EnvDTE" publicKeyToken="B03F5F7F11D50A3A" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f"/>
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed"/>
				<bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Unity.Abstractions" publicKeyToken="6d32ff45e0ccc69f" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.3.0.0" newVersion="5.3.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="QRCoder" publicKeyToken="c4ed5b9ae8358a28" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-1.6.0.0" newVersion="1.6.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Unity.Abstractions" publicKeyToken="489b6accfaf20ef0" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.11.7.0" newVersion="5.11.7.0"/>
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework"/>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
		</providers>
	</entityFramework>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701"/>
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+"/>
		</compilers>
	</system.codedom>
</configuration>
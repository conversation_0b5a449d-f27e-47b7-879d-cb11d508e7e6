﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DBLinker.Lib.Model
{
   public class M_Bookings
    {
        [Key]
        public long PKID { get; set; }
        public Nullable<int> City_From_Id { get; set; }
        public string City_From { get; set; }
        public string Booking_Id { get; set; }

        public Nullable<int> City_To_Id { get; set; }
        public string City_To { get; set; }
        public Nullable<int> Trip_Type_Id { get; set; }
        public string Trip_Type { get; set; }
        public Nullable<int> Car_Category_Id { get; set; }
        public string Car_Category { get; set; }

        public string PickUp_Address { get; set; }
        public string DropOff_Address { get; set; }
        public Nullable<System.DateTime> PickUp_Date { get; set; }
        public Nullable<System.DateTime> Booking_Date { get; set; }

        public string PickUp_Time { get; set; }
        public string Name { get; set; }
        public string Mobile_No1 { get; set; }
        public string Mobile_No2 { get; set; }
        public string Mail_Id { get; set; }
        public Nullable<int> Mode_Of_Payment_Id { get; set; }
        public Nullable<int> Booking_Status_Id { get; set; }
        public Nullable<int> Vendor_PKID { get; set; }
        public string Vendor_Name { get; set; }
        public Nullable<int> Car_PKID { get; set; }
        public string Car_Number { get; set; }
        public string Driver_Name { get; set; }
        public string Driver_Number { get; set; }

        public string Mode_Of_Payment { get; set; }
        public string Booking_Status { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Updated_By { get; set; }
        public string Booking_Remark { get; set; }

        public Nullable<decimal> Driver_Charge { get; set; }
        public Nullable<decimal> Toll_Charge { get; set; }
        public Nullable<decimal> GST { get; set; }
        public Nullable<decimal> Distance { get; set; }
        public string Duration { get; set; }

        public Nullable<decimal> Basic_Fare { get; set; }
        public Nullable<decimal> GST_Fare { get; set; }
        public Nullable<decimal> Fare { get; set; }
        public string Coupon_Code { get; set; }
        public decimal Coupon_Discount { get; set; }
        public string Invoice_No { get; set; }
        public Nullable<System.DateTime> Invoice_Date { get; set; }
        public int Is_OnlinePayment { get; set; }

        public string razorpay_payment_id { get; set; }
        public string razorpay_order_id { get; set; }
        public string razorpay_signature { get; set; }
        public string razorpay_status { get; set; }


        public string PickUpAddressLatitude { get; set; }
        public string PickUpAddressLongitude { get; set; }
        public string BookingEditRemark { get; set; }

        public Nullable<int> driver_id { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.IO;
using System.Net;
using System.Text;
using DBLinker.Lib;

namespace AdminApp.Controllers
{
    public class GoogleMapController : Controller
    {
        DBLinker.Lib.RLTDBContext entity;

        public GoogleMapController()
        {
            entity = new DBLinker.Lib.RLTDBContext();
        }

        // GET: GoogleMap
        public ActionResult Demo1()
        {
            return View();
        }

        /// <summary>
        /// Google Maps Places API AutoComplete for address suggestions
        /// </summary>
        /// <param name="placeName">The place name to search for</param>
        /// <param name="CityId">The city ID to bias the search results</param>
        /// <returns>JSON result with place suggestions</returns>
        public JsonResult AutoSuggestion(string placeName, int CityId)
        {
            string googleMapsApiKey = System.Configuration.ConfigurationManager.AppSettings["GoogleMaps_APIKey"];

            if (string.IsNullOrEmpty(googleMapsApiKey) || googleMapsApiKey == "YOUR_GOOGLE_MAPS_API_KEY_HERE")
            {
                return Json(new List<object>(), JsonRequestBehavior.AllowGet);
            }

            var cityLocation = (from a in entity.RLT_CITY
                               where a.PKID == CityId
                               select new
                               {
                                   latitude = a.latitude,
                                   longitude = a.longitude
                               }).FirstOrDefault();

            List<Object> listAutoSuggested = new List<object>();

            try
            {
                HttpWebRequest req = null;
                HttpWebResponse res = null;

                // Get city name for additional filtering
                string cityName = "";
                string stateName = "";
                var cityInfo = (from city in entity.RLT_CITY
                               join state in entity.RLT_STATE on city.State_PKID equals state.PKID into stateGroup
                               from state in stateGroup.DefaultIfEmpty()
                               where city.PKID == CityId
                               select new
                               {
                                   cityName = city.City_Name,
                                   stateName = state != null ? state.State_Name : ""
                               }).FirstOrDefault();

                if (cityInfo != null)
                {
                    cityName = cityInfo.cityName;
                    stateName = cityInfo.stateName;
                    System.Diagnostics.Debug.WriteLine($"City filtering: {cityName}, State: {stateName}");
                }

                // Use Google Places API Autocomplete with multiple restriction methods
                string url = $"https://maps.googleapis.com/maps/api/place/autocomplete/json?input={Uri.EscapeDataString(placeName)} {cityName}&key={googleMapsApiKey}";

                // Add country restriction to India
                url += "&components=country:in";

                // Add strict location restriction if city coordinates are available
                if (cityLocation != null && !string.IsNullOrEmpty(cityLocation.latitude) && !string.IsNullOrEmpty(cityLocation.longitude))
                {
                    double lat = Convert.ToDouble(cityLocation.latitude);
                    double lng = Convert.ToDouble(cityLocation.longitude);

                    // Use location bias with smaller radius and strict bounds
                    url += $"&locationbias=circle:50000@{lat},{lng}&types=establishment";
                }

                req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";

                res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();

                if (!string.IsNullOrEmpty(responseString))
                {
                    var jsonObj = JObject.Parse(responseString);

                    if (jsonObj["status"].ToString() == "OK")
                    {
                        JArray predictions = (JArray)jsonObj["predictions"];

                        foreach (JToken prediction in predictions)
                        {
                            string placeId = prediction["place_id"].ToString();
                            string description = prediction["description"].ToString();

                            // Get place details to retrieve coordinates
                            var placeDetails = GetPlaceDetails(placeId, googleMapsApiKey);

                            if (placeDetails != null)
                            {
                                // Additional filtering: check if the result is within reasonable distance from city center
                                // and contains the city name in the address
                                bool isWithinCityBounds = true;
                                bool containsCityName = true;

                                // Distance-based filtering
                                if (cityLocation != null && !string.IsNullOrEmpty(cityLocation.latitude) && !string.IsNullOrEmpty(cityLocation.longitude))
                                {
                                    double cityLat = Convert.ToDouble(cityLocation.latitude);
                                    double cityLng = Convert.ToDouble(cityLocation.longitude);
                                    double placeLat = Convert.ToDouble(placeDetails.latitude);
                                    double placeLng = Convert.ToDouble(placeDetails.longitude);

                                    // Calculate approximate distance (simple method)
                                    double latDiff = Math.Abs(cityLat - placeLat);
                                    double lngDiff = Math.Abs(cityLng - placeLng);

                                    // If the place is more than ~25km away from city center, exclude it
                                    // 0.225 degrees is approximately 25km
                                    if (latDiff > 0.225 || lngDiff > 0.225)
                                    {
                                        isWithinCityBounds = false;
                                    }
                                }

                                // City/State name filtering - check if the description contains the city name or state name
                                if (!string.IsNullOrEmpty(cityName))
                                {
                                    string descLower = description.ToLower();
                                    string cityLower = cityName.ToLower();

                                    // Check if description contains city name
                                    containsCityName = descLower.Contains(cityLower);

                                    // If city name not found but state name is available, also check for state
                                    if (!containsCityName && !string.IsNullOrEmpty(stateName))
                                    {
                                        string stateLower = stateName.ToLower();
                                        containsCityName = descLower.Contains(stateLower);
                                    }

                                    // For major cities, be more flexible - if it's a well-known location type, allow it
                                    if (!containsCityName)
                                    {
                                        // Allow results that are clearly within the city context (airports, stations, etc.)
                                        string[] allowedKeywords = { "airport", "station", "bus stand", "railway", "metro" };
                                        foreach (string keyword in allowedKeywords)
                                        {
                                            if (descLower.Contains(keyword) && descLower.Contains(cityLower.Split(' ')[0]))
                                            {
                                                containsCityName = true;
                                                break;
                                            }
                                        }
                                    }
                                }

                                // Only include results that are within city bounds AND contain the city name
                                if (isWithinCityBounds && containsCityName)
                                {
                                    var suggestionData = new
                                    {
                                        placeName = description,
                                        placeAddress = description,
                                        latitude = placeDetails.latitude,
                                        longitude = placeDetails.longitude,
                                        eLoc = placeId,
                                        score = "1.0",
                                        latlong = placeDetails.longitude + "," + placeDetails.latitude
                                    };

                                    listAutoSuggested.Add(suggestionData);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error if needed
                System.Diagnostics.Debug.WriteLine("Google Maps AutoSuggestion Error: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("Stack Trace: " + ex.StackTrace);
            }

            return Json(listAutoSuggested, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Helper method to get place details including coordinates from Google Places API
        /// </summary>
        /// <param name="placeId">The place ID from autocomplete</param>
        /// <param name="apiKey">Google Maps API key</param>
        /// <returns>Place details with coordinates</returns>
        private dynamic GetPlaceDetails(string placeId, string apiKey)
        {
            try
            {
                string url = "https://maps.googleapis.com/maps/api/place/details/json?place_id=" + placeId + "&fields=geometry&key=" + apiKey;

                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";

                HttpWebResponse res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();

                if (!string.IsNullOrEmpty(responseString))
                {
                    var jsonObj = JObject.Parse(responseString);

                    if (jsonObj["status"].ToString() == "OK")
                    {
                        var location = jsonObj["result"]["geometry"]["location"];
                        return new
                        {
                            latitude = location["lat"].ToString(),
                            longitude = location["lng"].ToString()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Google Maps Place Details Error: " + ex.Message);
            }

            return null;
        }

        /// <summary>
        /// Calculates compensated distance for time-based fare compensation
        /// If average speed < 66 km/h, compensate drivers by calculating fare based on 60 km/h standard
        /// </summary>
        /// <param name="actualDistanceKM">Actual distance from API in kilometers</param>
        /// <param name="journeyTimeSeconds">Journey time from API in seconds</param>
        /// <returns>Distance to use for billing (actual or compensated, whichever is higher)</returns>
        private int CalculateCompensatedDistance(int actualDistanceKM, int journeyTimeSeconds)
        {
            // Add 5-minute buffer to journey time as per business requirement
            int bufferedTimeSeconds = journeyTimeSeconds + (5 * 60); // Add 5 minutes
            double bufferedTimeHours = bufferedTimeSeconds / 3600.0;

            // Calculate average speed: Distance / Time
            double averageSpeed = actualDistanceKM / bufferedTimeHours;

            System.Diagnostics.Debug.WriteLine($"Journey Analysis:");
            System.Diagnostics.Debug.WriteLine($"  Actual Time: {journeyTimeSeconds / 3600.0:F2} hours");
            System.Diagnostics.Debug.WriteLine($"  Buffered Time: {bufferedTimeHours:F2} hours (+5 min buffer)");
            System.Diagnostics.Debug.WriteLine($"  Average Speed: {averageSpeed:F1} km/h");

            // Time-based compensation logic:
            // If average speed < 50 km/h, calculate compensated distance using 60 km/h standard
            if (averageSpeed < 50)
            {
                // Compensated distance = 60 km/h × buffered journey time
                int compensatedDistance = (int)Math.Round(60 * bufferedTimeHours);

                // Use the higher value between actual and compensated distance
                int billingDistance = Math.Max(actualDistanceKM, compensatedDistance);

                System.Diagnostics.Debug.WriteLine($"  Speed < 50 km/h: Applying time compensation");
                System.Diagnostics.Debug.WriteLine($"  Compensated Distance: {compensatedDistance} KM (60 km/h × {bufferedTimeHours:F2} hrs)");
                System.Diagnostics.Debug.WriteLine($"  Billing Distance: {billingDistance} KM (max of actual vs compensated)");

                return billingDistance;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"  Speed >= 50 km/h: Using actual distance (no compensation needed)");
                return actualDistanceKM;
            }
        }

        /// <summary>
        /// Google Maps Distance Matrix API for calculating distance and time between two locations
        /// </summary>
        /// <param name="pickUpAddressLatLong">Pickup location coordinates (longitude,latitude)</param>
        /// <param name="dropOffAddressLatLong">Drop-off location coordinates (longitude,latitude)</param>
        /// <param name="Trip_Type_Id">Trip type ID</param>
        /// <param name="Car_Category_Id">Car category ID</param>
        /// <returns>JSON result with distance, time, and fare details</returns>
        public JsonResult GetDistanceTimeDetailsBasedOnPickUpDropOffLocation(string pickUpAddressLatLong = null, string dropOffAddressLatLong = null, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {
            string googleMapsApiKey = System.Configuration.ConfigurationManager.AppSettings["GoogleMaps_APIKey"];

            if (string.IsNullOrEmpty(googleMapsApiKey) || googleMapsApiKey == "YOUR_GOOGLE_MAPS_API_KEY_HERE")
            {
                return Json(new { Distance = 0, Duration = "0 mins", Basic_Fare = 0, GST_Fare = 0, Fare = 0, FixRateNote = "Google Maps API key not configured" }, JsonRequestBehavior.AllowGet);
            }

            try
            {
                // Convert coordinates from longitude,latitude to latitude,longitude format for Google Maps
                string[] pickupCoords = pickUpAddressLatLong.Split(',');
                string[] dropoffCoords = dropOffAddressLatLong.Split(',');

                string pickupLatLng = pickupCoords[1] + "," + pickupCoords[0]; // Convert to lat,lng
                string dropoffLatLng = dropoffCoords[1] + "," + dropoffCoords[0]; // Convert to lat,lng

                string url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins=" + Uri.EscapeDataString(pickupLatLng) +
                           "&destinations=" + Uri.EscapeDataString(dropoffLatLng) + "&key=" + googleMapsApiKey + "&units=metric";

                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";

                HttpWebResponse res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();

                int distances = 0;
                int durationSeconds = 0; // Declare at proper scope
                string duration = "";

                if (!string.IsNullOrEmpty(responseString))
                {
                    var jsonObj = JObject.Parse(responseString);

                    if (jsonObj["status"].ToString() == "OK")
                    {
                        var element = jsonObj["rows"][0]["elements"][0];

                        if (element["status"].ToString() == "OK")
                        {
                            // Distance in meters, convert to kilometers
                            distances = Convert.ToInt32(element["distance"]["value"]) / 1000;

                            // Duration in seconds, convert to hours and minutes
                            durationSeconds = Convert.ToInt32(element["duration"]["value"]);
                            int hours = durationSeconds / 3600;
                            int mins = (durationSeconds % 3600) / 60;
                            duration = hours > 0 ? hours + " hrs " + mins + " mins" : mins + " mins";
                        }
                    }
                }

                // Apply time-based fare compensation logic (from frontend logic)
                int billingDistance = CalculateCompensatedDistance(distances, durationSeconds);

                System.Diagnostics.Debug.WriteLine($"Actual Distance: {distances} KM");
                System.Diagnostics.Debug.WriteLine($"Journey Time: {duration}");
                System.Diagnostics.Debug.WriteLine($"Billing Distance (after compensation): {billingDistance} KM");

                // Calculate fare using enhanced logic from frontend
                decimal Basic_Fare = 0, GST_Fare = 0;
                string fixRateNote = "";

                // Get car category details including base fare (if available)
                var carCategory = (from a in entity.RLT_CAR_CATEGORY
                                  where a.PKID == Car_Category_Id
                                  select a).FirstOrDefault();

                // Check for fixed fare rules first
                var fixedFareRule = (from a in entity.RLT_BOOKING_RULES
                                    where a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                                    && a.Distance_From <= billingDistance && a.Distance_To >= billingDistance
                                    select new
                                    {
                                        FixedFare = a.NewDistance
                                    }).FirstOrDefault();

                var bufferDistance = 20;
                var billingBufferedDistance = billingDistance + bufferDistance;

                if (fixedFareRule != null)
                {
                    // Use fixed fare rule
                    Basic_Fare = Convert.ToDecimal(fixedFareRule.FixedFare);
                    GST_Fare = (Basic_Fare * 5) / 100;
                    fixRateNote = $"Fixed fare rule applied: ₹{Math.Round(Basic_Fare + GST_Fare, 0)}";

                    System.Diagnostics.Debug.WriteLine($"Using Fixed Fare Rule: ₹{Basic_Fare}");
                }
                else if (carCategory != null)
                {
                    // Use per-KM pricing with base fare logic (NEW PRICING LOGIC: MAX(Base Fare, Distance Fare) + GST)
                    decimal baseFare = Convert.ToDecimal(carCategory.Base_Fare ?? 0);
                    decimal originalPerKmRate = Convert.ToDecimal(carCategory.Per_KM_fare ?? 0);

                    // Apply distance-based rate adjustment: +1 per KM if distance < 200 KM
                    decimal adjustedPerKmRate = originalPerKmRate;
                    if (billingDistance < 200)
                    {
                        adjustedPerKmRate = originalPerKmRate + 1;
                        System.Diagnostics.Debug.WriteLine($"Distance < 200 KM: Adjusted per KM rate from ₹{originalPerKmRate} to ₹{adjustedPerKmRate}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Distance >= 200 KM: Using original per KM rate ₹{originalPerKmRate}");
                    }

                    // Calculate distance fare
                    decimal distanceFare = adjustedPerKmRate * billingBufferedDistance;

                    // NEW LOGIC: Take maximum of base fare or distance fare
                    Basic_Fare = Math.Max(baseFare, distanceFare);
                    GST_Fare = (Basic_Fare * 5) / 100;

                    System.Diagnostics.Debug.WriteLine($"Fare Calculation:");
                    System.Diagnostics.Debug.WriteLine($"  Base Fare: ₹{baseFare}");
                    System.Diagnostics.Debug.WriteLine($"  Billing Distance: {billingBufferedDistance} KM");
                    System.Diagnostics.Debug.WriteLine($"  Per KM Rate: ₹{adjustedPerKmRate} (Original: ₹{originalPerKmRate})");
                    System.Diagnostics.Debug.WriteLine($"  Distance Fare: ₹{distanceFare:F0} ({billingBufferedDistance} KM × ₹{adjustedPerKmRate})");
                    System.Diagnostics.Debug.WriteLine($"  Basic Fare: ₹{Basic_Fare:F0} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare:F0})]");
                    System.Diagnostics.Debug.WriteLine($"  GST (5%): ₹{GST_Fare:F0}");
                    System.Diagnostics.Debug.WriteLine($"  Final Fare: ₹{(Basic_Fare + GST_Fare):F0}");

                    // Build comprehensive rate note with new pricing logic
                    string fareType = Basic_Fare == baseFare ? "Base Fare" : "Distance Fare";
                    fixRateNote = $"{fareType}: ₹{Basic_Fare:F0} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare:F0})]";

                    // Add distance calculation details
                    fixRateNote += $" (Distance: {billingBufferedDistance} KM ({billingDistance} KM + {bufferDistance} KM) × ₹{adjustedPerKmRate}/KM)";

                    // Add distance-based rate adjustment note
                    if (billingDistance < 200)
                    {
                        fixRateNote += " [+₹1/KM for trips < 200 KM]";
                    }

                    // Add time compensation note if billing distance differs from actual distance
                    if (billingDistance != distances)
                    {
                        fixRateNote += $" [Time compensation: billing {billingDistance} KM vs actual {distances} KM]";
                    }
                }

                // Calculate partial payment amount: 25% of basic fare OR minimum ₹500 (whichever is higher)
                decimal partialPaymentPercentage = Convert.ToDecimal(System.Configuration.ConfigurationManager.AppSettings["PartialPayment_Percentage"] ?? "25");
                decimal partialPaymentMinimum = Convert.ToDecimal(System.Configuration.ConfigurationManager.AppSettings["PartialPayment_MinimumAmount"] ?? "500");

                decimal partialPaymentAmount = Math.Max((Basic_Fare * partialPaymentPercentage / 100), partialPaymentMinimum);

                // Ensure partial payment doesn't exceed total fare
                decimal totalFare = Basic_Fare + GST_Fare;
                partialPaymentAmount = Math.Min(partialPaymentAmount, totalFare - 1); // Keep at least ₹1 for driver

                var fareData = new
                {
                    Distance = billingBufferedDistance, // Use billing distance instead of actual distance
                    Duration = duration,
                    Basic_Fare = Basic_Fare,
                    GST_Fare = GST_Fare,
                    Fare = totalFare,
                    FixRateNote = fixRateNote,
                    PartialPaymentAmount = Math.Round(partialPaymentAmount, 0) // Round to nearest rupee
                };

                return Json(fareData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Google Maps Distance Matrix Error: " + ex.Message);
                return Json(new { Distance = 0, Duration = "0 mins", Basic_Fare = 0, GST_Fare = 0, Fare = 0, FixRateNote = "Error calculating distance", PartialPaymentAmount = 0 }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// Google Maps Distance Matrix API for calculating distance and time between two cities
        /// </summary>
        /// <param name="City_From_Id">From city ID</param>
        /// <param name="City_To_Id">To city ID</param>
        /// <param name="Trip_Type_Id">Trip type ID</param>
        /// <param name="Car_Category_Id">Car category ID</param>
        /// <returns>JSON result with distance, time, and fare details</returns>
        public JsonResult GetDistanceTimeDetails(int City_From_Id = 0, int City_To_Id = 0, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {
            string googleMapsApiKey = System.Configuration.ConfigurationManager.AppSettings["GoogleMaps_APIKey"];

            if (string.IsNullOrEmpty(googleMapsApiKey) || googleMapsApiKey == "YOUR_GOOGLE_MAPS_API_KEY_HERE")
            {
                return Json(new { Distance = 0, Duration = "0 mins", Basic_Fare = 0, GST_Fare = 0, Fare = 0, FixRateNote = "Google Maps API key not configured" }, JsonRequestBehavior.AllowGet);
            }

            try
            {
                var query1 = (from a in entity.RLT_CITY
                              where a.PKID == City_From_Id
                              select new
                              {
                                  longitude = a.longitude,
                                  latitude = a.latitude
                              }).FirstOrDefault();

                var query2 = (from a in entity.RLT_CITY
                              where a.PKID == City_To_Id
                              select new
                              {
                                  longitude = a.longitude,
                                  latitude = a.latitude
                              }).FirstOrDefault();

                if (query1 == null || query2 == null)
                {
                    return Json(new { Distance = 0, Duration = "0 mins", Basic_Fare = 0, GST_Fare = 0, Fare = 0, FixRateNote = "City coordinates not found" }, JsonRequestBehavior.AllowGet);
                }

                string longitude_From = Convert.ToString(query1.longitude);
                string latitude_From = Convert.ToString(query1.latitude);
                string longitude_To = Convert.ToString(query2.longitude);
                string latitude_To = Convert.ToString(query2.latitude);

                string pickupLatLng = latitude_From + "," + longitude_From;
                string dropoffLatLng = latitude_To + "," + longitude_To;

                string url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins=" + Uri.EscapeDataString(pickupLatLng) +
                           "&destinations=" + Uri.EscapeDataString(dropoffLatLng) + "&key=" + googleMapsApiKey + "&units=metric";

                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";

                HttpWebResponse res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();

                int distances = 0;
                string duration = "";

                if (!string.IsNullOrEmpty(responseString))
                {
                    var jsonObj = JObject.Parse(responseString);

                    if (jsonObj["status"].ToString() == "OK")
                    {
                        var element = jsonObj["rows"][0]["elements"][0];

                        if (element["status"].ToString() == "OK")
                        {
                            // Distance in meters, convert to kilometers
                            distances = Convert.ToInt32(element["distance"]["value"]) / 1000;

                            // Duration in seconds, convert to hours and minutes
                            int durationSeconds = Convert.ToInt32(element["duration"]["value"]);
                            int hours = durationSeconds / 3600;
                            int mins = (durationSeconds % 3600) / 60;
                            duration = hours > 0 ? hours + " hrs " + mins + " mins" : mins + " mins";

                            // Apply time-based fare compensation logic
                            distances = CalculateCompensatedDistance(distances, durationSeconds);
                        }
                    }
                }

                // Calculate fare using enhanced logic from frontend
                decimal Basic_Fare = 0, GST_Fare = 0;
                string fixRateNote = "";

                // Get car category details
                var carCategory = (from a in entity.RLT_CAR_CATEGORY
                                  where a.PKID == Car_Category_Id
                                  select a).FirstOrDefault();

                // Check for fixed fare rules first
                var fixedFareRule = (from a in entity.RLT_BOOKING_RULES
                                    where a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                                    && a.Distance_From <= distances && a.Distance_To >= distances
                                    select new
                                    {
                                        FixedFare = a.NewDistance
                                    }).FirstOrDefault();

                if (fixedFareRule != null)
                {
                    // Use fixed fare rule
                    Basic_Fare = Convert.ToDecimal(fixedFareRule.FixedFare);
                    GST_Fare = (Basic_Fare * 5) / 100;
                    fixRateNote = $"Fixed fare rule applied: ₹{Math.Round(Basic_Fare + GST_Fare, 0)}";
                }
                else if (carCategory != null)
                {
                    // Use per-KM pricing with base fare logic
                    decimal baseFare = Convert.ToDecimal(carCategory.Base_Fare ?? 0);
                    decimal originalPerKmRate = Convert.ToDecimal(carCategory.Per_KM_fare ?? 0);

                    // Apply distance-based rate adjustment: +1 per KM if distance < 200 KM
                    decimal adjustedPerKmRate = originalPerKmRate;
                    if (distances < 200)
                    {
                        adjustedPerKmRate = originalPerKmRate + 1;
                    }

                    // 20 Buffer KM
                    var bufferDistance = 20;
                    var distanceBuffered = distances + bufferDistance;

                    // Calculate distance fare
                    decimal distanceFare = adjustedPerKmRate * distanceBuffered;

                    // NEW LOGIC: Take maximum of base fare or distance fare
                    Basic_Fare = Math.Max(baseFare, distanceFare);
                    GST_Fare = (Basic_Fare * 5) / 100;

                    // Build comprehensive rate note
                    string fareType = Basic_Fare == baseFare ? "Base Fare" : "Distance Fare";
                    fixRateNote = $"{fareType}: ₹{Basic_Fare:F0} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare:F0})]";
                    fixRateNote += $" (Distance: {distanceBuffered} KM ({distances} KM + {bufferDistance} Buffer KM) × ₹{adjustedPerKmRate}/KM)";

                    if (distances < 200)
                    {
                        fixRateNote += " [+₹1/KM for trips < 200 KM]";
                    }
                }

                // Calculate partial payment amount: 25% of basic fare OR minimum ₹500 (whichever is higher)
                decimal partialPaymentPercentage = Convert.ToDecimal(System.Configuration.ConfigurationManager.AppSettings["PartialPayment_Percentage"] ?? "25");
                decimal partialPaymentMinimum = Convert.ToDecimal(System.Configuration.ConfigurationManager.AppSettings["PartialPayment_MinimumAmount"] ?? "500");

                decimal partialPaymentAmount = Math.Max((Basic_Fare * partialPaymentPercentage / 100), partialPaymentMinimum);

                // Ensure partial payment doesn't exceed total fare
                decimal totalFare = Basic_Fare + GST_Fare;
                partialPaymentAmount = Math.Min(partialPaymentAmount, totalFare - 1); // Keep at least ₹1 for driver

                var fareData = new
                {
                    Distance = distances,
                    Duration = duration,
                    Basic_Fare = Basic_Fare,
                    GST_Fare = GST_Fare,
                    Fare = totalFare,
                    FixRateNote = fixRateNote,
                    PartialPaymentAmount = Math.Round(partialPaymentAmount, 0) // Round to nearest rupee
                };

                return Json(fareData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Google Maps Distance Matrix Error: " + ex.Message);
                return Json(new { Distance = 0, Duration = "0 mins", Basic_Fare = 0, GST_Fare = 0, Fare = 0, FixRateNote = "Error calculating distance" }, JsonRequestBehavior.AllowGet);
            }
        }
    }
}
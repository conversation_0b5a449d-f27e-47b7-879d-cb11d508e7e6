﻿@model DBLinker.Lib.Model.M_Driver_Enquiries

@{
    ViewBag.Title = "BecomeDriverRequest";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Become Driver Request for @Model.Driver_Name</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="#">Become Driver Request</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>


                </ol>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Driver Request Details</header>
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Drive Name</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Phone_1, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Driver Phone</label>
                            </div>
                        </div>

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Mail, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Driver Email</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Driver_Address, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Driver Address</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                @Html.TextBoxFor(x => x.IsWhatsAppNumber, new { @class = "mdl-textfield__input", Style = "color:black;" })
                                <label class="mdl-textfield__label">IsWhatsAppNumber</label>
                            </div>
                        </div>
                       

                    </div>
                



                </div>
            </div>
        </div>

</div>
    </div>


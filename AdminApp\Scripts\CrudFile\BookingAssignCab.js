﻿$(document).ready(function () {

    $("#Booking").parent().parent().find("a").next().slideToggle(500);
    $("#Booking").parent().parent().toggleClass('toggled');
    $("#Booking").find("a").addClass("selectedMenu");

    $("#btnSave").click(function () {
        if (IsValidate()) {
            $.ajax({
                url: 'AssignCab',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

  

    $("#Booking_Status_Id").change(function () {
        if ($("#Booking_Status_Id").val() == 2) {
            $("#rowCarAssign").slideDown();
            $("#rowRemark").slideUp();
        }
        else if ($("#Booking_Status_Id").val() == 4 || $("#Booking_Status_Id").val() == 5 || $("#Booking_Status_Id").val() == 6) {
            $("#rowRemark").slideDown();
            $("#rowCarAssign").slideUp();
        }
        else {
            $("#rowRemark").slideUp();
            $("#rowCarAssign").slideUp();
        }
    });

    $("#Vendor_PKID").change(function () {
        BindCar(0);
    });

});



function IsValidate() {  
    if ($("#Booking_Status_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select Booking Status!");
        return false;
    }

    if ($("#Booking_Status_Id").val() == 2) {
        if ($("#Vendor_PKID").val() == "") {
            toastr.clear();
            toastr.error("Please select Vendor!");
            return false;
        }
        if ($("#Car_PKID").val() == "") {
            toastr.clear();
            toastr.error("Please select Car!");
            return false;
        }
    }
    if ($("#Booking_Status_Id").val() == 4 || $("#Booking_Status_Id").val() == 5 || $("#Booking_Status_Id").val() == 6) {
        if ($("#Booking_Remark").val() == "") {
            toastr.clear();
            toastr.error("Please Enter remark!");
            return false;
        }
       
    }


    
    
    return true;
}


function BindCar(selectId) {
    $("#Car_PKID").empty();
    $.ajax({
        type: 'POST',
        url: '../VendorManagement/GetCarByVendor', // Calling json method  
        dataType: 'json',
        data: { id: $("#Vendor_PKID").val() },
        success: function (Car) {
            $("#Car_PKID").append('<option value=>-- Select Car --</option>');
            $.each(Car, function (i, Car) {
                $("#Car_PKID").append('<option value="' + Car.Value + '">' +
                    Car.Text + '</option>');
            });
            if (selectId != 0)
                $("#Car_PKID").val(selectId);

        }
    });
    return false;
}




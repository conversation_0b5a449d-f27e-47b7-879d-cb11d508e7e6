﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Net.Http;
using System.Net.Http.Headers;

namespace AdminApp.Controllers
{
    public class MapMyIndiaController : Controller
    {
        DBLinker.Lib.RLTDBContext entity;

        public MapMyIndiaController()
        {
            entity = new DBLinker.Lib.RLTDBContext();
        }
        // GET: MapMyIndia
        public JsonResult GetCityDetails(string CityName = "", string StateName = "")
        {
            string MapMyIndia_Token = "bearer " + GetAcessToken();
            //string MapMyIndia_Token =  GetAcessToken();

            HttpWebRequest req = null;
            HttpWebResponse res = null;
            //string url = System.Configuration.ConfigurationManager.AppSettings["MapMyIndia_CityLatLongAPI"]+CityName.Trim()+ "&token="+ MapMyIndia_Token;
            string url = "https://atlas.mapmyindia.com/api/places/search/json?query=" + CityName + "&pod=CITY";

            req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "GET";
            req.ContentType = "application/json; charset=utf-8";
            req.Headers.Add("Authorization", MapMyIndia_Token);
            ASCIIEncoding encoder = new ASCIIEncoding();
            res = (HttpWebResponse)req.GetResponse();
            Stream responseStream = res.GetResponseStream();
            var streamReader = new StreamReader(responseStream);

            string responseString = streamReader.ReadToEnd();
            string latitude1 = "", longitude1 = "", eLoc1 = "", score1 = "";

            if (responseString != "")
            {
                var jsonObj = JObject.Parse(responseString);
                JArray suggestedLocations = (JArray)jsonObj["suggestedLocations"];
                foreach (JToken signInName in suggestedLocations)
                {
                    if (signInName["placeName"].ToString().ToLower() == CityName.ToLower() && (string)signInName["placeAddress"] == StateName)
                    {
                        latitude1 = (string)signInName["latitude"];
                        longitude1 = (string)signInName["longitude"];
                        eLoc1 = (string)signInName["eLoc"];
                        score1 = (string)signInName["score"];
                    }
                }
            }

            var CityData = new
            {
                latitude = latitude1,
                longitude = longitude1,
                eLoc = eLoc1,
                score = score1,
            };



            if (CityData != null)
                return Json(CityData, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult GetDistanceTimeDetails(int City_From_Id = 0, int City_To_Id = 0, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {

            string MapMyIndia_RestLicenseKey = System.Configuration.ConfigurationManager.AppSettings["MapMyIndia_RestLicenseKey"];


            var query1 = (from a in entity.RLT_CITY
                          where a.PKID == City_From_Id
                          select new
                          {
                              longitude = a.longitude,
                              latitude = a.latitude
                          }).FirstOrDefault();

            var query2 = (from a in entity.RLT_CITY
                          where a.PKID == City_To_Id
                          select new
                          {
                              longitude = a.longitude,
                              latitude = a.latitude
                          }).FirstOrDefault();
            string longitude_From = "", latitude_From = "", longitude_To = "", latitude_To = "";
            longitude_From = Convert.ToString(query1.longitude);
            latitude_From = Convert.ToString(query1.latitude);
            longitude_To = Convert.ToString(query2.longitude);
            latitude_To = Convert.ToString(query2.latitude);

            string address = longitude_From + "," + latitude_From + ";" + longitude_To + "," + latitude_To;


            HttpWebRequest req = null;
            HttpWebResponse res = null;
            string url = "https://apis.mapmyindia.com/advancedmaps/v1/" + MapMyIndia_RestLicenseKey + "/distance_matrix/driving/" + address + "";

            req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "GET";
            req.ContentType = "application/json; charset=utf-8";
            ASCIIEncoding encoder = new ASCIIEncoding();
            res = (HttpWebResponse)req.GetResponse();
            Stream responseStream = res.GetResponseStream();
            var streamReader = new StreamReader(responseStream);
            string responseString = streamReader.ReadToEnd();
            int distances = 0;
            string duration = "";
            if (responseString != "")
            {
                var jsonObj = JObject.Parse(responseString);
                JArray distancesArr = (JArray)jsonObj.SelectToken("results.distances[0]");
                distances = Convert.ToInt32(distancesArr[1]) / 1000;
                JArray durationsArr = (JArray)jsonObj.SelectToken("results.durations[0]");
                int secs = Convert.ToInt32(durationsArr[1]);
                int hours = secs / 3600;
                int mins = (secs % 3600) / 60;
                duration = hours + " hrs " + mins + " mins ";
            }
            int Per_KM_fare = 0;
            decimal Basic_Fare = 0, GST_Fare;
            string fixRateNote = "";
            var query = (from a in entity.RLT_CAR_CATEGORY
                         where a.PKID == Car_Category_Id
                         select new
                         {
                             Per_KM_fare = a.Per_KM_fare
                         }).FirstOrDefault();

            var queryNew = (from a in entity.RLT_BOOKING_RULES
                            where a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                            && a.Distance_From <= distances && a.Distance_To >= distances
                            select new
                            {
                                NewFare = a.NewDistance
                            }).FirstOrDefault();

            if (queryNew != null)
            {
                Basic_Fare = Convert.ToDecimal(queryNew.NewFare);
                GST_Fare = (Basic_Fare * 5) / 100;
                fixRateNote = "1 KM to 100 KM we charge fix 1000 rs";
            }
            else
            {
                Per_KM_fare = Convert.ToInt32(query.Per_KM_fare);
                Basic_Fare = Per_KM_fare * distances;
                GST_Fare = (Basic_Fare * 5) / 100;
            }

            var FareChart = new
            {
                Distance = distances.ToString("#.##"),
                Basic_Fare = Basic_Fare.ToString("#.##"),
                GST_Fare = GST_Fare.ToString("#.##"),
                Fare = (Basic_Fare + GST_Fare).ToString("#.##"),
                Duration = duration,
                FixRateNote = fixRateNote
            };



            if (FareChart != null)
                return Json(FareChart, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult GetDistanceTimeDetailsBasedOnPickUpDropOffLocation(string pickUpAddressLatLong = null, string dropOffAddressLatLong = null, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {

            string MapMyIndia_RestLicenseKey = System.Configuration.ConfigurationManager.AppSettings["MapMyIndia_RestLicenseKey"];

            string address = pickUpAddressLatLong + ";" + dropOffAddressLatLong;


            HttpWebRequest req = null;
            HttpWebResponse res = null;
            string url = "https://apis.mapmyindia.com/advancedmaps/v1/" + MapMyIndia_RestLicenseKey + "/distance_matrix/driving/" + address + "";

            req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "GET";
            req.ContentType = "application/json; charset=utf-8";
            ASCIIEncoding encoder = new ASCIIEncoding();
            res = (HttpWebResponse)req.GetResponse();
            Stream responseStream = res.GetResponseStream();
            var streamReader = new StreamReader(responseStream);
            string responseString = streamReader.ReadToEnd();
            int distances = 0;
            string duration = "";
            if (responseString != "")
            {
                var jsonObj = JObject.Parse(responseString);
                JArray distancesArr = (JArray)jsonObj.SelectToken("results.distances[0]");
                distances = Convert.ToInt32(distancesArr[1]) / 1000;
                JArray durationsArr = (JArray)jsonObj.SelectToken("results.durations[0]");
                int secs = Convert.ToInt32(durationsArr[1]);
                int hours = secs / 3600;
                int mins = (secs % 3600) / 60;
                duration = hours + " hrs " + mins + " mins ";
            }
            int Per_KM_fare = 0;
            decimal Basic_Fare = 0, GST_Fare;
            string fixRateNote = "";
            var query = (from a in entity.RLT_CAR_CATEGORY
                         where a.PKID == Car_Category_Id
                         select new
                         {
                             Per_KM_fare = a.Per_KM_fare
                         }).FirstOrDefault();

            var queryNew = (from a in entity.RLT_BOOKING_RULES
                            where a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                            && a.Distance_From <= distances && a.Distance_To >= distances
                            select new
                            {
                                NewFare = a.NewDistance
                            }).FirstOrDefault();

            if (queryNew != null)
            {
                Basic_Fare = Convert.ToDecimal(queryNew.NewFare);
                GST_Fare = (Basic_Fare * 5) / 100;
                fixRateNote = "1 KM to 100 KM we charge fix 1000 rs";
            }
            else
            {
                Per_KM_fare = Convert.ToInt32(query.Per_KM_fare);
                Basic_Fare = Per_KM_fare * distances;
                GST_Fare = (Basic_Fare * 5) / 100;
            }

            var FareChart = new
            {
                Distance = distances.ToString("#.##"),
                Basic_Fare = Basic_Fare.ToString("#.##"),
                GST_Fare = GST_Fare.ToString("#.##"),
                Fare = (Basic_Fare + GST_Fare).ToString("#.##"),
                Duration = duration,
                FixRateNote = fixRateNote
            };



            if (FareChart != null)
                return Json(FareChart, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }


        public string GetAcessToken()
        {
            string oAuthAPI = System.Configuration.ConfigurationManager.AppSettings["OAuthAPI"];
            string grantTpye = System.Configuration.ConfigurationManager.AppSettings["GrantType"];
            string clientId = System.Configuration.ConfigurationManager.AppSettings["ClientId"];
            string clientSecret = System.Configuration.ConfigurationManager.AppSettings["ClientSecret"];

            string Token = "";
            HttpWebRequest req = null;
            HttpWebResponse res = null;
            string url = oAuthAPI + "grant_type=" + grantTpye + "&client_id=" + clientId + "&client_secret=" + clientSecret;

            req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "POST";
            req.ContentType = "application/json; charset=utf-8";
            ASCIIEncoding encoder = new ASCIIEncoding();
            res = (HttpWebResponse)req.GetResponse();
            Stream responseStream = res.GetResponseStream();
            var streamReader = new StreamReader(responseStream);
            string responseString = streamReader.ReadToEnd();
            if (responseString != "")
            {
                var jsonObj = JObject.Parse(responseString);
                Token = (string)jsonObj.SelectToken("access_token");
            }
            return Token;
        }

        // Test endpoint to check city data
        public JsonResult GetCityInfo(int CityId)
        {
            var cityInfo = (from a in entity.RLT_CITY
                            where a.PKID == CityId
                            select new
                            {
                                PKID = a.PKID,
                                City_Name = a.City_Name,
                                eLoc = a.eLoc,
                                latitude = a.latitude,
                                longitude = a.longitude,
                                Is_Active = a.Is_Active
                            }).FirstOrDefault();

            return Json(cityInfo, JsonRequestBehavior.AllowGet);
        }

        public JsonResult AutoSuggestion(string placeName, int CityId)
        {
            string autoSuggestionAPI = System.Configuration.ConfigurationManager.AppSettings["MapMyIndia_AutoSuggestAPI"];
            Dictionary<string, string> addressDictionary = new Dictionary<string, string>();
            var cityLocation = (from a in entity.RLT_CITY
                        where a.PKID == CityId
                        select new
                        {
                            latitude = a.latitude,
                            longitude = a.longitude
                        }).FirstOrDefault();

            string MapMyIndia_Token = "bearer " + GetAcessToken();
            string responseString = "";
            HttpWebRequest req = null;
            HttpWebResponse res = null;

            string url = "https://atlas.mapmyindia.com/api/places/search/json?query=" + placeName;

            // Add location parameter using latitude and longitude from the selected city
            if (cityLocation != null && !string.IsNullOrEmpty(cityLocation.latitude) && !string.IsNullOrEmpty(cityLocation.longitude))
            {
                url += "&location=" + cityLocation.latitude + "," + cityLocation.longitude;
            }

            req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "GET";
            req.ContentType = "application/json; charset=utf-8";
            req.Headers.Add("Authorization", MapMyIndia_Token);
            ASCIIEncoding encoder = new ASCIIEncoding();
            res = (HttpWebResponse)req.GetResponse();
            Stream responseStream = res.GetResponseStream();
            var streamReader = new StreamReader(responseStream);

            responseString = streamReader.ReadToEnd();

            List<Object> listAutoSuggested = new List<object>();

            string latlong = "", latitude1 = "", longitude1 = "", eLoc1 = "", score1 = "", placeName1 = "", placeAddress1 = "";

            if (responseString != "" && responseString != "{}")
            {
                var jsonObj = JObject.Parse(responseString);
                JArray suggestedLocations = (JArray)jsonObj["suggestedLocations"];
                foreach (JToken signInName in suggestedLocations)
                {
                    placeName1 = (string)signInName["placeName"];
                    placeAddress1 = (string)signInName["placeAddress"];
                    latitude1 = (string)signInName["latitude"];
                    longitude1 = (string)signInName["longitude"];
                    eLoc1 = (string)signInName["eLoc"];
                    score1 = (string)signInName["score"];

                    var CityData = new
                    {
                        placeName = placeName,
                        placeAddress = placeAddress1,
                        latitude = latitude1,
                        longitude = longitude1,
                        eLoc = eLoc1,
                        score = score1,
                        latlong = longitude1 + "," + latitude1
                    };

                    listAutoSuggested.Add(CityData);
                }
            }

            if (listAutoSuggested != null)
                return Json(listAutoSuggested, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }
    }
}
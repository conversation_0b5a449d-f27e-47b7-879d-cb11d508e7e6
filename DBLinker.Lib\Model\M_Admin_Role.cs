﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Admin_Role
    {
        public int RoleId { get; set; }
        public string Role { get; set; }
        public string Role_Description { get; set; }
        public string CreatedBy { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<bool> IsActive { get; set; }

    }
}

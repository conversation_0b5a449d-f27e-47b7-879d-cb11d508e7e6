﻿using Common.Lib.Model;
using DBLinker.Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace AdminApp.Filters
{
    public class ExceptionHandlerAttribute : FilterAttribute, IExceptionFilter
    {
 
        public void OnException(ExceptionContext filterContext)
        {
            System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace(filterContext.Exception, true);
            //string ipAddresss = filterContext.Request
            int loginUserId = Common.Lib.Impl.CommonMethods.SB_TryParseInt32(Convert.ToString(filterContext.HttpContext.Session["UserId"]));

            StringBuilder loValidationMessage = new StringBuilder();
            if (filterContext.Exception is System.Data.Entity.Validation.DbEntityValidationException)
            {
                var dbEx = (System.Data.Entity.Validation.DbEntityValidationException)filterContext.Exception;
                foreach (var loEntityValidationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var loValidationErrors in loEntityValidationErrors.ValidationErrors)
                    {
                        loValidationMessage.AppendLine(string.Format("{0}:{1}", loEntityValidationErrors.Entry.Entity.ToString(), loValidationErrors.ErrorMessage));
                    }
                }
            }
            string strErrorMessage = filterContext.Exception.Message;
            if (!string.IsNullOrEmpty(Convert.ToString(loValidationMessage)))
            {
                strErrorMessage = "Entity Validation Error ::>> " + Convert.ToString(loValidationMessage) + " Exception Message :: >>" + filterContext.Exception.Message;
            }
            ExceptionLogger logger = new ExceptionLogger()
            {
                StackTrace = st.ToString(),
                ExceptionMessage = strErrorMessage,
                LineNumber = st.GetFrame(0).GetFileLineNumber(),
                MethodName = Convert.ToString(filterContext.RouteData.Values["action"]),
                ControllerName = Convert.ToString(filterContext.RouteData.Values["controller"]),
                LoginUserId = loginUserId,
                LogTime = DateTime.UtcNow
            };


            using (RLTDBContext db = new RLTDBContext())
            {
                //Add Exception To Tables
               // db.ExceptionLoggers.Add(logger);
               // db.SaveChanges();
            }

            filterContext.ExceptionHandled = true;
        }
        //Ended


    
       
    }
}
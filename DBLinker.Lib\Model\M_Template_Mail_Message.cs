﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Template_Mail_Message
    {

        public int PKID { get; set; }
        public Nullable<int> SendToType { get; set; } 
        public Nullable<long> SendToId { get; set; }
        public string Mail_SMS_Type { get; set; }
        public Nullable<int> TemplateId { get; set; }
        public string Template_Name { get; set; }
        public string MobileNo { get; set; }
        public string MailId { get; set; }
        public string Subject { get; set; }
        public string Message_Body { get; set; }

    }
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
 public class M_Document_Name
    {
        public int PKID { get; set; }
        
        [Remote("Check_Doc_Name", "Common",AdditionalFields = "Doc_For", ErrorMessage = "Document Name already exists!")]
        public string Doc_Name { get; set; }
        public string Doc_For { get; set; }
        public bool Is_Doc_Req { get; set; }
        public Nullable<bool> Is_Doc_Expiry_Date { get; set; }
        public bool Is_Active { get; set; }

        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }

    }
}

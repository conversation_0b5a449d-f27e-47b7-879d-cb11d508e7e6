﻿@model DBLinker.Lib.Model.M_Booking

@{
    ViewBag.Title = "Booking Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

    <head>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">

        <script src="//code.jquery.com/jquery-1.10.2.js"></script>

        <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>

        <link href="~/Content/assets/TabWizard/normalize.css" rel="stylesheet" />
        <link href="~/Content/assets/TabWizard/main.css" rel="stylesheet" />
        <link href="~/Content/assets/TabWizard/jquery.steps.css" rel="stylesheet" />
        <script src="~/Content/assets/TabWizard/modernizr-2.6.2.min.js"></script>
        <script src="~/Content/assets/TabWizard/jquery.steps.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
        <style>
            .ui-autocomplete {
                min-height: 400px;
                overflow-y: auto;
            }

            
        </style>

    </head>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Add Booking</li>
                </ol>
            </div>
        </div>


        <script>
            $(function () {
                $("#wizard").steps({
                    headerTag: "h2",
                    bodyTag: "section",
                    transitionEffect: "slideLeft"
                });
            });
        </script>

        @using (Html.BeginForm())
        {

            @Html.AntiForgeryToken()
            <div class="row">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div id="wizard">
                            <h2>Search Cab</h2>
                            <section style="position:inherit !important">

                                <div class="card-body row">

                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CityList != null)
                                            {

                                                @Html.DropDownListFor(x => x.City_From_Id, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>City From</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CityList != null)
                                            {

                                                @Html.DropDownListFor(x => x.City_To_Id, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>City To</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.TripTypeList != null)
                                            {

                                                @Html.DropDownListFor(x => x.Trip_Type_Id, new SelectList(ViewBag.TripTypeList, "PKID", "Trip_Type"), "-- Select Trip Type --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>Trip Type</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.CarCategory != null)
                                            {

                                                @Html.DropDownListFor(x => x.Car_Category_Id, new SelectList(ViewBag.CarCategory, "PKID", "Car_Category"), "-- Select Car Category --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                            }
                                            <label class="mdl-textfield__label required"><span>CAR Category</span> </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-5 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.PickUp_Address, new { @class = "mdl-textfield__input" })
                                            @Html.HiddenFor(x=>x.PickUpAddressLatitude)
                                          
                                            <label class="mdl-textfield__label required"><span>Pick Up Address</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-5 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.DropOff_Address, new { @class = "mdl-textfield__input" })
                                            @Html.HiddenFor(x => x.PickUpAddressLongitude)
                                            <label class="mdl-textfield__label required"><span>Drop Off Address</span></label>
                                        </div>
                                    </div>

                                    <div class="col-lg-2 p-t-20">
                                        <button type="button" style="position: absolute;bottom:20px;left:0px;" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSearchCab">Calculate</button>

                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                            @Html.TextBoxFor(x => x.Duration, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                            <label class="mdl-textfield__label"> Duration</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                            @Html.TextBoxFor(x => x.Distance, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                            <label class="mdl-textfield__label"> Distance (KM)</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                            @Html.TextBoxFor(x => x.Basic_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                            <label class="mdl-textfield__label"> Basic Fare</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                            @Html.TextBoxFor(x => x.GST_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                            <label class="mdl-textfield__label"> GST Amount(5%)</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                            @Html.TextBoxFor(x => x.Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:green;font-weight:bold;" })
                                            <label class="mdl-textfield__label"> Fare</label>

                                            @Html.HiddenFor(x => x.Coupon_Discount, new { @id = "Coupon_Discount" })
                                            @Html.HiddenFor(x => x.GST, new { @id = "GST" })
                                            <input type="hidden" name="hd_OldBasic_Fare" id="hd_OldBasic_Fare" />
                                            <input type="hidden" name="hd_OldGST_Fare" id="hd_OldGST_Fare" />
                                            <input type="hidden" name="hd_OldFare" id="hd_OldFare" />
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                            @Html.TextBoxFor(x => x.Coupon_Code, new { @class = "mdl-textfield__input", Style = "color:green;font-weight:bold;" })
                                            <label class="mdl-textfield__label"> Coupon Code</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 p-t-20">
                                        <button type="button" style="position: absolute;bottom:20px;left:0px;" class="btn btn-info" id="btnApplyDiscount">Apply Coupon</button>
                                        <button type="button" style="position: absolute;bottom:20px;left:0px;display:none;" class="btn btn-info" id="btnRemoveDiscount">Remove Coupon</button>

                                    </div>
                                    <div class="col-lg-4 p-t-20">
                                        <label id="Coupon_Code_Applied" style="font-weight:bold;color:green;position: absolute; bottom: 20px;font-size:13px;"></label> &nbsp;&nbsp;
                                        <label id="FixRateNote" style="font-weight:bold;color:red;position: absolute; bottom: 40px;font-size:13px;"></label>


                                    </div>
                                </div>

                            </section>
                            <h2>Booking Details</h2>
                            <section>
                                <div class="card-body row">

                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.completepickupaddress, new { @class = "mdl-textfield__input" })
                                          
                                            <label class="mdl-textfield__label required"><span>Pick Up Complete Address</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.completedropoffpaddress, new { @class = "mdl-textfield__input" })
                                           
                                            <label class="mdl-textfield__label required"><span>Drop Off Complete Address</span></label>
                                        </div>
                                    </div>


                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.PickUp_Date, new { @class = "mdl-textfield__input", type = "date" })
                                            <label class="mdl-textfield__label required"> <span>Pick Up Date</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.PickUp_Time, new { @class = "mdl-textfield__input", @id = "time" })
                                            <label class="mdl-textfield__label required"><span>Pick Up Time</span> </label>
                                        </div>
                                    </div>

                                </div>
                            </section>
                            <h2>Personal Details</h2>
                            <section>
                                <div class="card-body row">
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.Name, new { @class = "mdl-textfield__input", @onblur = "isTextValid(this);", @maxlength = "50" })
                                            <label class="mdl-textfield__label required"><span>Name</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.Mail_Id, new { @class = "mdl-textfield__input", @onblur = "isEmail(this);", @maxlength = "50" })
                                            <label class="mdl-textfield__label required"><span>Mail Id</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.Mobile_No1, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                            <label class="mdl-textfield__label required"> <span>Mobile No 1</span></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @Html.TextBoxFor(x => x.Mobile_No2, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                            <label class="mdl-textfield__label">Mobile No 2</label>
                                        </div>
                                    </div>
                                </div>
                            </section>
                            <h2>Payment Details</h2>
                            <section>

                                <div class="card-body row">
                                    <div class="col-lg-6 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            @if (ViewBag.PaymentMethodList != null)
                                            {

                                                @Html.DropDownListFor(x => x.Mode_Of_Payment_Id, new SelectList(ViewBag.PaymentMethodList, "PKID", "Payment_Method_Name"), "-- Select Payment Method --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                            }

                                            <label class="mdl-textfield__label">Payment Mode</label>
                                            <small class="text-muted" id="paymentMethodInfo" style="display:none;">
                                                <i class="fa fa-info-circle"></i> Online payment will be processed securely
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 p-t-20">
                                    </div>
                                    <div class="col-lg-4 p-t-20" style="text-align:center;">
                                        @Html.HiddenFor(x => x.Is_OnlinePayment, new { Value = "0" })
                                        @Html.HiddenFor(x => x.PaymentOption, new { Value = "2" })
                                        @Html.HiddenFor(x => x.PartialPaymentAmount, new { Value = "0" })

                                        <!-- Partial Payment Options -->
                                        <div id="partialPaymentSection" style="display:none; margin-bottom: 15px; text-align: left;">
                                            <div style="margin-bottom: 10px;">
                                                <label style="display: flex; align-items: center; font-size: 14px; color: #333;">
                                                    <input type="checkbox" id="chkPartialPayment" style="margin-right: 8px; transform: scale(1.2);" />
                                                    <span>Enable Partial Payment</span>
                                                </label>
                                                <small class="text-muted" style="margin-left: 24px; font-size: 12px;">
                                                    <i class="fa fa-info-circle"></i> Customer pays partial amount online, remaining amount to driver
                                                </small>
                                            </div>
                                            <div id="partialAmountSection" style="display:none; margin-left: 24px;">
                                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label" style="width: 200px;">
                                                    <input type="number" class="mdl-textfield__input" id="txtPartialAmount" min="1" step="0.01" />
                                                    <label class="mdl-textfield__label">Partial Amount (₹)</label>
                                                </div>
                                                <small class="text-muted" style="display: block; margin-top: 5px; font-size: 11px;">
                                                    <i class="fa fa-info-circle"></i> Suggested: 25% of basic fare OR minimum ₹500 (whichever is higher)
                                                </small>
                                                <small class="text-muted" style="display: block; margin-top: 2px; font-size: 10px; color: #666;">
                                                    Amount customer will pay online, remaining amount to be paid to driver
                                                </small>
                                            </div>
                                        </div>

                                        <button type="button" style="position: absolute;bottom:20px;left:0px;display:none;" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSubmitWithPayment">Generate Payment Link</button>

                                    </div>

                                </div>

                            </section>
                        </div>
                    </div>
                </div>
            </div>
        }

    </div>
</div>


<script src="~/Scripts/CrudFile/BookingNew.js"></script>

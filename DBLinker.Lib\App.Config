﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
    <!--<add name="RLTDBContext" connectionString="metadata=res://*/RLTCARMODEL.csdl|res://*/RLTCARMODEL.ssdl|res://*/RLTCARMODEL.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=*************,1232;initial catalog=rltcarrental;persist security info=True;user id=rlt_web;password=**************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="RLTDBContext" connectionString="metadata=res://*/RLTCARMODEL.csdl|res://*/RLTCARMODEL.ssdl|res://*/RLTCARMODEL.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-0IKCJLS\SQLEXPRESS;initial catalog=cabyaari;persist security info=True;user id=rlt_web;password=**************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <add name="RLTDBContext" connectionString="metadata=res://*/RLTCARMODEL.csdl|res://*/RLTCARMODEL.ssdl|res://*/RLTCARMODEL.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=CabYaari;user id=cabyaari_user;password=********************************;trustservercertificate=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
</configuration>
﻿@model DBLinker.Lib.RLT_Services

@{
    ViewBag.Title = "PrivateDayManager";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/css/jquery.dataTables.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/select/1.2.2/css/select.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.2/js/dataTables.select.min.js"></script>
<script src="//cdn.tinymce.com/4/tinymce.min.js"></script>
<link href="~/Content/toastr.min.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<script>

    tinymce.init({
        selector: "#AboutService",
        height: 300,
        plugins: [
"advlist autolink autosave link image lists charmap print preview hr anchor pagebreak spellchecker",
"searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking",
"table contextmenu directionality emoticons template textcolor paste textcolor colorpicker textpattern"
        ],

        toolbar1: "bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | styleselect formatselect fontselect fontsizeselect",
        toolbar2: "cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | undo redo | link unlink anchor image media code | insertdatetime preview | forecolor backcolor",
        toolbar3: "table | hr removeformat | subscript superscript | charmap emoticons | print fullscreen | ltr rtl | spellchecker | visualchars visualblocks nonbreaking template pagebreak restoredraft",

        menubar: false,
        toolbar_items_size: 'small',

        templates: [{
            title: 'Test template 1',
            content: 'Test 1'
        }, {
            title: 'Test template 2',
            content: 'Test 2'
        }],
        content_css: [
'/Content/style.css'
        ]
    });

</script>

<section class="content">
    .
    <div class="row">
        <div class="col-md-12">
            <!--breadcrumbs start -->
            <ul class="breadcrumb">
                <li><a href="#"><i class="fa fa-user"></i> Admin</a></li>
                <li><a href="#"><i class="fa fa-flag"></i> Services Manager</a></li>

            </ul>
            <!--breadcrumbs end -->
        </div>
    </div>
    @using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()

        <input type="hidden" name="hdnOperation" id="hdnOperation" />
        <input type="hidden" name="hdnStatus" id="hdnStatus" />
        @Html.HiddenFor(x => x.IsActive)
        @Html.HiddenFor(x => x.ID)
        <div class="row" id="dvAddUpdate">
            <div class="col-md-12">
                <div class="col-lg-12">
                    <section class="panel">
                        <header class="panel-heading">
                            Add Update Services
                        </header>
                        <div class="panel-body">

                            <div class="form-group">
                                <div class="col-sm-2">
                                   Service Name
                                </div>
                                <div class="col-lg-4">
                                   

                                     @Html.DropDownListFor(x => x.ServiceName, new List<SelectListItem>
    {
        new SelectListItem{ Text="--Select Service--", Value = "" },
        new SelectListItem{ Text="Train Reservation", Value = "Train Reservation" },
        new SelectListItem{ Text="Airline Services", Value = "Airline Services" },
        new SelectListItem{ Text="International Food", Value = "International Food"},
         new SelectListItem{ Text="Tour Operator", Value = "Tour Operator" },
        new SelectListItem{ Text="Car and Coach Service", Value = "Car and Coach Service" },
        new SelectListItem{ Text="Travel Insurance", Value = "Travel Insurance"},
         new SelectListItem{ Text="Hotel Booking", Value = "Hotel Booking" },
        new SelectListItem{ Text="Event Management", Value = "Event Management" },
      
    }, new { @class = "form-control" })
                                   
                                </div>
                                <div class="col-sm-2">
                                    Service Font Icon
                                </div>
                                <div class="col-lg-4">
                                    @Html.TextBoxFor(x => x.ServiceIconClass, new { @class = "form-control", @placeholder = "font awesome icon like fa fa-train fa-5x  !" })
                                </div>
                            </div>

                            <br />
                            <br />

                            <div class="form-group">
                                <div class="col-sm-2">
                                    About Service
                                </div>
                                <div class="col-lg-10">
                                    @Html.TextBoxFor(x => x.AboutService, new { @class = "form-control", @placeholder = "enter service description !" })


                                </div>

                            </div>

                        </div>
                        <footer class="panel-footer">
                            <center>
                                <button type="button" class="btn btn-info" id="btnSave">Save</button>
                                <button type="button" class="btn btn-info" id="btnUpdate">Update</button>
                                <button type="reset" class="btn btn-danger" id="btnReset">cancel</button>
                            </center>
                        </footer>
                    </section>
                </div>

            </div>
        </div>

    }

    <section class="panel tasks-widget">
        <header class="panel-heading">
            Services Manager
            <button type="button" id="btnAdd" class="btn btn-info" style="float:right;margin-top: -5px;">Add Update Services</button>
        </header>
        <div class="panel-body">

            <div class="task-content">

                <table id="example" class="display" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Services Name</th>
                            <th>Icon</th>
                            <th>About Services</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th></th>
                            <th>Services Name</th>
                            <th>Icon</th>
                            <th>About Services</th>
                            <th>Status</th>
                            <th></th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @if (ViewBag.serviceGrid != null)
                        {
                            foreach (var item in ViewBag.serviceGrid)
                            {

                                <tr>
                                    <input type="hidden" id="<EMAIL>" value="@item.AboutService" />
                                    <input type="hidden" id="<EMAIL>" value="@item.ServiceName" />
                                    <input type="hidden" id="<EMAIL>" value="@item.ServiceIconClass" />
                                    <input type="hidden" id="<EMAIL>" value="@item.IsActive" />

                                    <td></td>
                                    <td>@item.ServiceName</td>
                                    <td><i class="@item.ServiceIconClass"></i> </td>
                                    <td>@item.AboutService</td>
                                    <td>@item.IsActive</td>

                                    <td>
                                        <button class="btn btn-default btn-xs" onclick="ActivateDeactivate(@item.ID)" type="button"><i class="fa fa-check"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Edit(@item.ID)" type="button"><i class="fa fa-pencil"></i></button>
                                        <button class="btn btn-default btn-xs" onclick="Delete(@item.ID)" type="button"><i class="fa fa-times"></i></button>
                                    </td>
                                </tr>
                            }

                        }

                    </tbody>
                </table>
            </div>


        </div>
    </section>

</section>
<script src="~/Scripts/CrudFile/ServieCrudJs.js"></script>





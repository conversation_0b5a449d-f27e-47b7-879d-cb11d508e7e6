﻿$(document).ready(function () {

    $("#FAQManager").parent().parent().find("a").next().slideToggle(500);
    $("#FAQManager").parent().parent().toggleClass('toggled');
    $("#FAQManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();
    $('#example').DataTable({
        columnDefs: [{
            orderable: false,
            className: 'select-checkbox',
            targets: 0
        }],
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        order: [[1, 'asc']]
    });


    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'FAQManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            $.ajax({
                url: 'FAQManager',
                type: "POST",
                traditional: true,
                data: $("form").serialize(),
                dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    tinyMCE.get('FAQDetails').setContent($("#FAQDetails_" + Id).val());

    $("#IsActive").val(true);

    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}


function ActivateDeactivate(Id) {
    $("#PKID").val('');
    tinyMCE.get('FAQDetails').setContent($("#FAQDetails_" + Id).val());
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'FAQManager',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    tinyMCE.get('FAQDetails').setContent('');

}

function IsValidate() {
    if (tinyMCE.get('FAQDetails').getContent() == "") {
        toastr.clear();
        toastr.error("Please enter FAQ details !");
        return false;
    }


    $("#FAQDetails").val(tinyMCE.get('FAQDetails').getContent());
    return true;
}
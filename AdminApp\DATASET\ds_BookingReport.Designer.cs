﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace AdminApp.DATASET {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("ds_BookingReport")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class ds_BookingReport : global::System.Data.DataSet {
        
        private dt_BookingDataTable tabledt_Booking;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public ds_BookingReport() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected ds_BookingReport(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["dt_Booking"] != null)) {
                    base.Tables.Add(new dt_BookingDataTable(ds.Tables["dt_Booking"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public dt_BookingDataTable dt_Booking {
            get {
                return this.tabledt_Booking;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public override global::System.Data.DataSet Clone() {
            ds_BookingReport cln = ((ds_BookingReport)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["dt_Booking"] != null)) {
                    base.Tables.Add(new dt_BookingDataTable(ds.Tables["dt_Booking"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars(bool initTable) {
            this.tabledt_Booking = ((dt_BookingDataTable)(base.Tables["dt_Booking"]));
            if ((initTable == true)) {
                if ((this.tabledt_Booking != null)) {
                    this.tabledt_Booking.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void InitClass() {
            this.DataSetName = "ds_BookingReport";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/ds_BookingReport.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tabledt_Booking = new dt_BookingDataTable();
            base.Tables.Add(this.tabledt_Booking);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private bool ShouldSerializedt_Booking() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            ds_BookingReport ds = new ds_BookingReport();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public delegate void dt_BookingRowChangeEventHandler(object sender, dt_BookingRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class dt_BookingDataTable : global::System.Data.TypedTableBase<dt_BookingRow> {
            
            private global::System.Data.DataColumn columnBooking_Id;
            
            private global::System.Data.DataColumn columnCity_From;
            
            private global::System.Data.DataColumn columnCity_To;
            
            private global::System.Data.DataColumn columnTrip_Type;
            
            private global::System.Data.DataColumn columnCar_Category;
            
            private global::System.Data.DataColumn columnFare;
            
            private global::System.Data.DataColumn columnGST_Fare;
            
            private global::System.Data.DataColumn columnBooking_Date;
            
            private global::System.Data.DataColumn columnPickUp_Address;
            
            private global::System.Data.DataColumn columnDropOff_Address;
            
            private global::System.Data.DataColumn columnPickUp_Date;
            
            private global::System.Data.DataColumn columnPickUp_Time;
            
            private global::System.Data.DataColumn columnName;
            
            private global::System.Data.DataColumn columnMobile_No1;
            
            private global::System.Data.DataColumn columnMobile_No2;
            
            private global::System.Data.DataColumn columnMail_Id;
            
            private global::System.Data.DataColumn columnPayment_Method_Name;
            
            private global::System.Data.DataColumn columnVendor_Company_Name;
            
            private global::System.Data.DataColumn columnCar_Number;
            
            private global::System.Data.DataColumn columnBOOKING_STATUS;
            
            private global::System.Data.DataColumn columnBooking_Remark;
            
            private global::System.Data.DataColumn columnDriver_Name;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingDataTable() {
                this.TableName = "dt_Booking";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal dt_BookingDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected dt_BookingDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Booking_IdColumn {
                get {
                    return this.columnBooking_Id;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn City_FromColumn {
                get {
                    return this.columnCity_From;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn City_ToColumn {
                get {
                    return this.columnCity_To;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Trip_TypeColumn {
                get {
                    return this.columnTrip_Type;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Car_CategoryColumn {
                get {
                    return this.columnCar_Category;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn FareColumn {
                get {
                    return this.columnFare;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn GST_FareColumn {
                get {
                    return this.columnGST_Fare;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Booking_DateColumn {
                get {
                    return this.columnBooking_Date;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn PickUp_AddressColumn {
                get {
                    return this.columnPickUp_Address;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn DropOff_AddressColumn {
                get {
                    return this.columnDropOff_Address;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn PickUp_DateColumn {
                get {
                    return this.columnPickUp_Date;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn PickUp_TimeColumn {
                get {
                    return this.columnPickUp_Time;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn NameColumn {
                get {
                    return this.columnName;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Mobile_No1Column {
                get {
                    return this.columnMobile_No1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Mobile_No2Column {
                get {
                    return this.columnMobile_No2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Mail_IdColumn {
                get {
                    return this.columnMail_Id;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Payment_Method_NameColumn {
                get {
                    return this.columnPayment_Method_Name;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Vendor_Company_NameColumn {
                get {
                    return this.columnVendor_Company_Name;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Car_NumberColumn {
                get {
                    return this.columnCar_Number;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn BOOKING_STATUSColumn {
                get {
                    return this.columnBOOKING_STATUS;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Booking_RemarkColumn {
                get {
                    return this.columnBooking_Remark;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Driver_NameColumn {
                get {
                    return this.columnDriver_Name;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingRow this[int index] {
                get {
                    return ((dt_BookingRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_BookingRowChangeEventHandler dt_BookingRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_BookingRowChangeEventHandler dt_BookingRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_BookingRowChangeEventHandler dt_BookingRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event dt_BookingRowChangeEventHandler dt_BookingRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void Adddt_BookingRow(dt_BookingRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingRow Adddt_BookingRow(
                        string Booking_Id, 
                        string City_From, 
                        string City_To, 
                        string Trip_Type, 
                        string Car_Category, 
                        string Fare, 
                        string GST_Fare, 
                        string Booking_Date, 
                        string PickUp_Address, 
                        string DropOff_Address, 
                        string PickUp_Date, 
                        string PickUp_Time, 
                        string Name, 
                        string Mobile_No1, 
                        string Mobile_No2, 
                        string Mail_Id, 
                        string Payment_Method_Name, 
                        string Vendor_Company_Name, 
                        string Car_Number, 
                        string BOOKING_STATUS, 
                        string Booking_Remark, 
                        string Driver_Name) {
                dt_BookingRow rowdt_BookingRow = ((dt_BookingRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        Booking_Id,
                        City_From,
                        City_To,
                        Trip_Type,
                        Car_Category,
                        Fare,
                        GST_Fare,
                        Booking_Date,
                        PickUp_Address,
                        DropOff_Address,
                        PickUp_Date,
                        PickUp_Time,
                        Name,
                        Mobile_No1,
                        Mobile_No2,
                        Mail_Id,
                        Payment_Method_Name,
                        Vendor_Company_Name,
                        Car_Number,
                        BOOKING_STATUS,
                        Booking_Remark,
                        Driver_Name};
                rowdt_BookingRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowdt_BookingRow);
                return rowdt_BookingRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public override global::System.Data.DataTable Clone() {
                dt_BookingDataTable cln = ((dt_BookingDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new dt_BookingDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal void InitVars() {
                this.columnBooking_Id = base.Columns["Booking_Id"];
                this.columnCity_From = base.Columns["City_From"];
                this.columnCity_To = base.Columns["City_To"];
                this.columnTrip_Type = base.Columns["Trip_Type"];
                this.columnCar_Category = base.Columns["Car_Category"];
                this.columnFare = base.Columns["Fare"];
                this.columnGST_Fare = base.Columns["GST_Fare"];
                this.columnBooking_Date = base.Columns["Booking_Date"];
                this.columnPickUp_Address = base.Columns["PickUp_Address"];
                this.columnDropOff_Address = base.Columns["DropOff_Address"];
                this.columnPickUp_Date = base.Columns["PickUp_Date"];
                this.columnPickUp_Time = base.Columns["PickUp_Time"];
                this.columnName = base.Columns["Name"];
                this.columnMobile_No1 = base.Columns["Mobile_No1"];
                this.columnMobile_No2 = base.Columns["Mobile_No2"];
                this.columnMail_Id = base.Columns["Mail_Id"];
                this.columnPayment_Method_Name = base.Columns["Payment_Method_Name"];
                this.columnVendor_Company_Name = base.Columns["Vendor_Company_Name"];
                this.columnCar_Number = base.Columns["Car_Number"];
                this.columnBOOKING_STATUS = base.Columns["BOOKING_STATUS"];
                this.columnBooking_Remark = base.Columns["Booking_Remark"];
                this.columnDriver_Name = base.Columns["Driver_Name"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            private void InitClass() {
                this.columnBooking_Id = new global::System.Data.DataColumn("Booking_Id", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBooking_Id);
                this.columnCity_From = new global::System.Data.DataColumn("City_From", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCity_From);
                this.columnCity_To = new global::System.Data.DataColumn("City_To", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCity_To);
                this.columnTrip_Type = new global::System.Data.DataColumn("Trip_Type", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTrip_Type);
                this.columnCar_Category = new global::System.Data.DataColumn("Car_Category", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCar_Category);
                this.columnFare = new global::System.Data.DataColumn("Fare", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFare);
                this.columnGST_Fare = new global::System.Data.DataColumn("GST_Fare", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGST_Fare);
                this.columnBooking_Date = new global::System.Data.DataColumn("Booking_Date", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBooking_Date);
                this.columnPickUp_Address = new global::System.Data.DataColumn("PickUp_Address", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPickUp_Address);
                this.columnDropOff_Address = new global::System.Data.DataColumn("DropOff_Address", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDropOff_Address);
                this.columnPickUp_Date = new global::System.Data.DataColumn("PickUp_Date", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPickUp_Date);
                this.columnPickUp_Time = new global::System.Data.DataColumn("PickUp_Time", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPickUp_Time);
                this.columnName = new global::System.Data.DataColumn("Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnName);
                this.columnMobile_No1 = new global::System.Data.DataColumn("Mobile_No1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMobile_No1);
                this.columnMobile_No2 = new global::System.Data.DataColumn("Mobile_No2", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMobile_No2);
                this.columnMail_Id = new global::System.Data.DataColumn("Mail_Id", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMail_Id);
                this.columnPayment_Method_Name = new global::System.Data.DataColumn("Payment_Method_Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPayment_Method_Name);
                this.columnVendor_Company_Name = new global::System.Data.DataColumn("Vendor_Company_Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnVendor_Company_Name);
                this.columnCar_Number = new global::System.Data.DataColumn("Car_Number", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCar_Number);
                this.columnBOOKING_STATUS = new global::System.Data.DataColumn("BOOKING_STATUS", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBOOKING_STATUS);
                this.columnBooking_Remark = new global::System.Data.DataColumn("Booking_Remark", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBooking_Remark);
                this.columnDriver_Name = new global::System.Data.DataColumn("Driver_Name", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDriver_Name);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingRow Newdt_BookingRow() {
                return ((dt_BookingRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new dt_BookingRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Type GetRowType() {
                return typeof(dt_BookingRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.dt_BookingRowChanged != null)) {
                    this.dt_BookingRowChanged(this, new dt_BookingRowChangeEvent(((dt_BookingRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.dt_BookingRowChanging != null)) {
                    this.dt_BookingRowChanging(this, new dt_BookingRowChangeEvent(((dt_BookingRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.dt_BookingRowDeleted != null)) {
                    this.dt_BookingRowDeleted(this, new dt_BookingRowChangeEvent(((dt_BookingRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.dt_BookingRowDeleting != null)) {
                    this.dt_BookingRowDeleting(this, new dt_BookingRowChangeEvent(((dt_BookingRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void Removedt_BookingRow(dt_BookingRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                ds_BookingReport ds = new ds_BookingReport();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "dt_BookingDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class dt_BookingRow : global::System.Data.DataRow {
            
            private dt_BookingDataTable tabledt_Booking;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal dt_BookingRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tabledt_Booking = ((dt_BookingDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Booking_Id {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Booking_IdColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Booking_Id\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Booking_IdColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string City_From {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.City_FromColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'City_From\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.City_FromColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string City_To {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.City_ToColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'City_To\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.City_ToColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Trip_Type {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Trip_TypeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Trip_Type\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Trip_TypeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Car_Category {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Car_CategoryColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Car_Category\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Car_CategoryColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Fare {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.FareColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Fare\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.FareColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string GST_Fare {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.GST_FareColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'GST_Fare\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.GST_FareColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Booking_Date {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Booking_DateColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Booking_Date\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Booking_DateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string PickUp_Address {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.PickUp_AddressColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PickUp_Address\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.PickUp_AddressColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string DropOff_Address {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.DropOff_AddressColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'DropOff_Address\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.DropOff_AddressColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string PickUp_Date {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.PickUp_DateColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PickUp_Date\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.PickUp_DateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string PickUp_Time {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.PickUp_TimeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PickUp_Time\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.PickUp_TimeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Name\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Mobile_No1 {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Mobile_No1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Mobile_No1\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Mobile_No1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Mobile_No2 {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Mobile_No2Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Mobile_No2\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Mobile_No2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Mail_Id {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Mail_IdColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Mail_Id\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Mail_IdColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Payment_Method_Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Payment_Method_NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Payment_Method_Name\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Payment_Method_NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Vendor_Company_Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Vendor_Company_NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Vendor_Company_Name\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Vendor_Company_NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Car_Number {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Car_NumberColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Car_Number\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Car_NumberColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string BOOKING_STATUS {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.BOOKING_STATUSColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'BOOKING_STATUS\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.BOOKING_STATUSColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Booking_Remark {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Booking_RemarkColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Booking_Remark\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Booking_RemarkColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Driver_Name {
                get {
                    try {
                        return ((string)(this[this.tabledt_Booking.Driver_NameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Driver_Name\' in table \'dt_Booking\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tabledt_Booking.Driver_NameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsBooking_IdNull() {
                return this.IsNull(this.tabledt_Booking.Booking_IdColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetBooking_IdNull() {
                this[this.tabledt_Booking.Booking_IdColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCity_FromNull() {
                return this.IsNull(this.tabledt_Booking.City_FromColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCity_FromNull() {
                this[this.tabledt_Booking.City_FromColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCity_ToNull() {
                return this.IsNull(this.tabledt_Booking.City_ToColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCity_ToNull() {
                this[this.tabledt_Booking.City_ToColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsTrip_TypeNull() {
                return this.IsNull(this.tabledt_Booking.Trip_TypeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetTrip_TypeNull() {
                this[this.tabledt_Booking.Trip_TypeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCar_CategoryNull() {
                return this.IsNull(this.tabledt_Booking.Car_CategoryColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCar_CategoryNull() {
                this[this.tabledt_Booking.Car_CategoryColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsFareNull() {
                return this.IsNull(this.tabledt_Booking.FareColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetFareNull() {
                this[this.tabledt_Booking.FareColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsGST_FareNull() {
                return this.IsNull(this.tabledt_Booking.GST_FareColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetGST_FareNull() {
                this[this.tabledt_Booking.GST_FareColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsBooking_DateNull() {
                return this.IsNull(this.tabledt_Booking.Booking_DateColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetBooking_DateNull() {
                this[this.tabledt_Booking.Booking_DateColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPickUp_AddressNull() {
                return this.IsNull(this.tabledt_Booking.PickUp_AddressColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPickUp_AddressNull() {
                this[this.tabledt_Booking.PickUp_AddressColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDropOff_AddressNull() {
                return this.IsNull(this.tabledt_Booking.DropOff_AddressColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDropOff_AddressNull() {
                this[this.tabledt_Booking.DropOff_AddressColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPickUp_DateNull() {
                return this.IsNull(this.tabledt_Booking.PickUp_DateColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPickUp_DateNull() {
                this[this.tabledt_Booking.PickUp_DateColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPickUp_TimeNull() {
                return this.IsNull(this.tabledt_Booking.PickUp_TimeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPickUp_TimeNull() {
                this[this.tabledt_Booking.PickUp_TimeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNameNull() {
                return this.IsNull(this.tabledt_Booking.NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNameNull() {
                this[this.tabledt_Booking.NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsMobile_No1Null() {
                return this.IsNull(this.tabledt_Booking.Mobile_No1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetMobile_No1Null() {
                this[this.tabledt_Booking.Mobile_No1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsMobile_No2Null() {
                return this.IsNull(this.tabledt_Booking.Mobile_No2Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetMobile_No2Null() {
                this[this.tabledt_Booking.Mobile_No2Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsMail_IdNull() {
                return this.IsNull(this.tabledt_Booking.Mail_IdColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetMail_IdNull() {
                this[this.tabledt_Booking.Mail_IdColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPayment_Method_NameNull() {
                return this.IsNull(this.tabledt_Booking.Payment_Method_NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPayment_Method_NameNull() {
                this[this.tabledt_Booking.Payment_Method_NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsVendor_Company_NameNull() {
                return this.IsNull(this.tabledt_Booking.Vendor_Company_NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetVendor_Company_NameNull() {
                this[this.tabledt_Booking.Vendor_Company_NameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCar_NumberNull() {
                return this.IsNull(this.tabledt_Booking.Car_NumberColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCar_NumberNull() {
                this[this.tabledt_Booking.Car_NumberColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsBOOKING_STATUSNull() {
                return this.IsNull(this.tabledt_Booking.BOOKING_STATUSColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetBOOKING_STATUSNull() {
                this[this.tabledt_Booking.BOOKING_STATUSColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsBooking_RemarkNull() {
                return this.IsNull(this.tabledt_Booking.Booking_RemarkColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetBooking_RemarkNull() {
                this[this.tabledt_Booking.Booking_RemarkColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDriver_NameNull() {
                return this.IsNull(this.tabledt_Booking.Driver_NameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDriver_NameNull() {
                this[this.tabledt_Booking.Driver_NameColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public class dt_BookingRowChangeEvent : global::System.EventArgs {
            
            private dt_BookingRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingRowChangeEvent(dt_BookingRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public dt_BookingRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591
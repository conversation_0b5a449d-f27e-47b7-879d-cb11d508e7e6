﻿using AdminApp.Filters;
using AdminApp.Mapper;
using AjaxControlToolkit.HTMLEditor.ToolbarButton;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;

namespace AdminApp.Controllers
{
    public class LoginController : Controller
    {
        RLTDBContext entity;
        IGenericRepository<RLT_ADMIN_USER> userRepository;
        ITwoFactorAuth auth;
        public LoginController(IGenericRepository<RLT_ADMIN_USER> _userRepository, ITwoFactorAuth _auth)
        {
            userRepository = _userRepository;
            entity = new RLTDBContext();
            auth = _auth;

        }
        // GET: Login
        public ActionResult Index()
        {
            var adminLogin = new RLT_ADMIN_USER();
            return View(adminLogin);
        }

        [HttpPost]
        public ActionResult Index(RLT_ADMIN_USER user, string returnUrl)
        {
            if (ModelState.IsValid)
            {
                user.UserPWD = QueryStringEncoding.EncryptString(user.UserPWD, "UserPass");
                var IsValidUser = userRepository.GetAll().Where(x => (x.UserName == user.UserName && x.UserPWD == user.UserPWD && x.IsActive == true)).FirstOrDefault();
                // var IsValidUser = userRepository.GetAll().Where(x => x.IsActive == true).FirstOrDefault();
                if (IsValidUser != null)
                {
                    CustomPrincipalSerializeModel model = new CustomPrincipalSerializeModel();
                    model.userId = IsValidUser.PKID;
                    model.UserFirstName = IsValidUser.UserFirstName;
                    model.UserLastName = IsValidUser.UserLastName;
                    model.UserName = IsValidUser.UserName;
                    model.Is2TfaAuthentication = IsValidUser.Is2TfaAuthentication;
                    model.UserEmailID = IsValidUser.UserEmailID;

                    string[] staticRole = new string[1];
                    staticRole[0] = "Admin";
                    model.roles = staticRole;
                    
                    model.roleId = IsValidUser.RoleId;
                    model.userPhoto = IsValidUser.UserPhoto;
                    CreateRoleWiseMenu(IsValidUser.RoleId);
                    string userData = JsonConvert.SerializeObject(model);
                    FormsAuthenticationTicket authTicket = new FormsAuthenticationTicket(
                               1,
                               IsValidUser.UserEmailID,
                               DateTime.Now,
                               DateTime.Now.AddMinutes(10),
                               false,
                               userData);

                    string encTicket = FormsAuthentication.Encrypt(authTicket);
                    HttpCookie faCookie = new HttpCookie(FormsAuthentication.FormsCookieName, encTicket);

                    Response.Cookies.Add(faCookie);
                    if (model.Is2TfaAuthentication == true) return RedirectToAction("VerifyTwoFactorAuthentication", "Login");
                    else return (returnUrl != null) ? Redirect(Server.UrlDecode(returnUrl)) : Redirect("~/Admin/Dashboard");

                }
                ModelState.AddModelError("", "Incorrect username and/or password");
            }

            return View(user);
        }

        [CustomAuthorize]
        public ActionResult VerifyTwoFactorAuthentication()
        {
            return View();
        }

        [HttpPost]
        public ActionResult VerifyTwoFactorAuthentication(string PassCode)
        {
            string UserName = Convert.ToString(((CustomPrincipal)(User)).UserName);
            string UniqueKey = GetUniqueKey(UserName);
            bool isValid = auth.Verify2FA(PassCode, UniqueKey);

            return Json(isValid, JsonRequestBehavior.AllowGet);
        }
        [NonAction]
        public string GetUniqueKey(string userName)
        {

            string UniqueKey = "";

            string key = "S@2a88();{}#~.,";

            var userAuth = userRepository.GetAll().Where(x => (x.UserName == userName && x.IsActive == true)).FirstOrDefault();

            UniqueKey = (userName + userAuth.UserEmailID + key);

            return UniqueKey;

        }

        public ActionResult AcessDenied()
        {

            return View();
        }

        public void CreateRoleWiseMenu(int? role_Id = 0)
        {

            var MenuList = (from a in entity.RLT_MENU_MASTER
                            join b in entity.RLT_ROLE_MENU_MAPPING on
                              a.PKID equals b.Menu_Id
                            where b.Role_Id == role_Id && b.Is_View == true
                            orderby a.OrderNumber
                            select new M_Role_Menu_Mapping
                            {
                                Menu_Id = a.PKID,
                                MenuName = a.MenuName,
                                MenuIcon = a.MenuIcon,
                                MenuURL = a.MenuURL,
                                PageId = a.PageId,
                                Is_View = b.Is_View,
                                Is_Add = b.Is_Add,
                                Is_Edit = b.Is_Edit,
                                Is_Delete = b.Is_Delete,
                                ParentMenuId = a.ParentMenuId,
                                ActiveMenuClass = a.ActiveMenuClass
                            }).ToList();

            Session["MenuList"] = MenuList;
        }

    }
}
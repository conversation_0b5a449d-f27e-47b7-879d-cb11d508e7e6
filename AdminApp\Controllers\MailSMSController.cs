﻿using System;
using System.Collections.Generic;
using System.Data.Entity.SqlServer;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using AdminApp.Mapper;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;

namespace AdminApp.Controllers
{
    public class MailSMSController : Controller
    {
        DBLinker.Lib.RLTDBContext entity;

        public MailSMSController()
        {
            entity = new DBLinker.Lib.RLTDBContext();
        }

        #region Template Manager

        public ActionResult TemplateManager()
        {
            var TemplateList = from a in entity.RLT_Template_Master select a; 
            ViewBag.TemplateList = TemplateList;
            return View();
        }

        [HttpPost, ValidateInput(false)]
        public JsonResult AddTemplateManager(M_Template Template)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {

                var vdtlsList = (from a in entity.RLT_Template_Master where a.PKID == Template.PKID select a);
                foreach (RLT_Template_Master vdtls in vdtlsList)
                {
                    vdtls.Mail_SMS_Type = Template.Mail_SMS_Type;
                    vdtls.Template_Name = Template.Template_Name;
                    vdtls.Subject = Template.Subject;
                    vdtls.Message_Body = Template.Message_Body;
                }
                entity.SaveChanges();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                var vdtlsList = (from a in entity.RLT_Template_Master where a.PKID == Template.PKID select a);
                foreach (RLT_Template_Master vdtls in vdtlsList)
                {
                    vdtls.IsActive = Template.IsActive;
                }
                entity.SaveChanges();
                Id = 3;
            }
            else
            {

                RLT_Template_Master vdtls = new RLT_Template_Master();
                vdtls.Mail_SMS_Type = Template.Mail_SMS_Type;
                vdtls.Template_Name = Template.Template_Name;
                vdtls.Subject = Template.Subject;
                vdtls.Message_Body = Template.Message_Body;
                vdtls.IsActive = true;
                entity.RLT_Template_Master.Add(vdtls);
                entity.SaveChanges();

                Id = 1;
            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion



        #region Send Mail Message Manager

        public ActionResult SendMailMessageManager()
        {
            var TemplateMailMessageList = (from a in entity.RLT_Template_Mail_Message
                                           join b in entity.RLT_Template_Master on a.TemplateId equals b.PKID
                                 select new M_Template_Mail_Message
                                 {
                                     PKID = a.PKID,
                                     SendToType = a.SendToType,
                                     SendToId = a.SendToId,
                                     Mail_SMS_Type = a.Mail_SMS_Type,
                                     Template_Name = b.Template_Name,
                                     MobileNo = a.MobileNo,
                                     MailId = a.MailId,
                                     Subject = a.Subject

        }).ToList();

            ViewBag.TemplateMailMessageList = TemplateMailMessageList;
            return View();
        }

        [HttpPost, ValidateInput(false)]
        public JsonResult AddSendMailMessageManager(M_Template_Mail_Message Template)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("D"))
            {
                
                var x = (from y in entity.RLT_Template_Mail_Message
                         where y.PKID == Template.PKID
                         select y).FirstOrDefault();
                entity.RLT_Template_Mail_Message.Remove(x);
                entity.SaveChanges();
                Id = 3;
            }
            else
            {

                RLT_Template_Mail_Message vdtls = new RLT_Template_Mail_Message();
                vdtls.SendToType = Template.SendToType;
                vdtls.SendToId = Template.SendToId;
                vdtls.Mail_SMS_Type = Template.Mail_SMS_Type;
                vdtls.TemplateId = Template.TemplateId;
                vdtls.MobileNo = Template.MobileNo;
                vdtls.MailId = Template.MailId;
                vdtls.Subject = Template.Subject;
                vdtls.Message_Body = Template.Message_Body;
                entity.RLT_Template_Mail_Message.Add(vdtls);
                entity.SaveChanges();

                Id = 1;
            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }


        public JsonResult GetSendToId(int id = 0)
        {
            if (id == 1) // Vendor
            {
                var DataList =from a in entity.RLT_Vendor_Details orderby a.Vendor_Owner_Name select a  ;
                var Data = DataList.Select(m => new SelectListItem()
                {
                    Text = m.Vendor_Owner_Name,
                    Value = m.PKID.ToString(),
                });
                return Json(Data, JsonRequestBehavior.AllowGet);
            }
            if (id == 2) // Car Owner
            {
                var DataList = from a in entity.RLT_CAR_OWNER_DETAILS orderby a.Car_Owner_Name select a;
                var Data = DataList.Select(m => new SelectListItem()
                {
                    Text = m.Car_Owner_Name,
                    Value = m.PKID.ToString(),
                });
                return Json(Data, JsonRequestBehavior.AllowGet);
            }
            if (id == 3) // Driver
            {
                var DataList = from a in entity.RLT_CAR_DRIVER_DETAILS orderby a.Driver_Name select a;
                var Data = DataList.Select(m => new SelectListItem()
                {
                    Text = m.Driver_Name,
                    Value = m.PKID.ToString(),
                });
                return Json(Data, JsonRequestBehavior.AllowGet);
            }
            else //Employee
            {
                var DataList = from a in entity.RLT_Admin_User_Info orderby a.Employee_Name select a;
                var Data = DataList.Select(m => new SelectListItem()
                {
                    Text = m.Employee_Name,
                    Value = m.PKID.ToString(),
                });
                return Json(Data, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult GetTemplate(string id = "")
        {
            var DataList = entity.RLT_Template_Master.Where(m => m.Mail_SMS_Type == id).ToList();
            var Data = DataList.Select(m => new SelectListItem()
            {
                Text = m.Template_Name,
                Value = m.PKID.ToString(),
            });
            return Json(Data, JsonRequestBehavior.AllowGet);
        }

        public JsonResult GetTemplateDetails(string Mail_SMS_Type = "", int SendToType = 0, int SendToId = 0, int TemplateId = 0)
        {
            string UserMailId = "", UserMobile = "",UserName="";
            if (SendToType == 1) // Vendor
            {
                var Data = (from a in entity.RLT_Vendor_Details
                                 where a.PKID == SendToId
                                 select new
                                 { a.Vendor_EmailID, a.Vendor_Phone1, a.Vendor_Owner_Name }).SingleOrDefault();

                UserMailId = Data.Vendor_EmailID;
                UserMobile = Data.Vendor_Phone1;
                UserName = Data.Vendor_Owner_Name;
            }
           else if (SendToType == 2) // Car Owner
            {
                var Data = (from a in entity.RLT_CAR_OWNER_DETAILS
                            where a.PKID == SendToId
                            select new
                            { a.Car_Owner_Email, a.Car_Owner_Phone1, a.Car_Owner_Name }).SingleOrDefault();

                UserMailId = Data.Car_Owner_Email;
                UserMobile = Data.Car_Owner_Phone1;
                UserName = Data.Car_Owner_Name;
            }
            if (SendToType == 3) // Driver
            {

                var Data = (from a in entity.RLT_CAR_DRIVER_DETAILS
                            where a.PKID == SendToId
                            select new
                            { a.Driver_Email, a.Drive_Phone_1, a.Driver_Name }).SingleOrDefault();

                UserMailId = Data.Driver_Email;
                UserMobile = Data.Drive_Phone_1;
                UserName = Data.Driver_Name;
            }
            else if (SendToType == 4) //Employee
            {
                var DataList = from a in entity.RLT_Admin_User_Info orderby a.Employee_Name select a;
                var Data = (from a in entity.RLT_Admin_User_Info
                            where a.PKID == SendToId
                            select new
                            { a.Email, a.Phone_1, a.Employee_Name }).SingleOrDefault();

                UserMailId = Data.Email;
                UserMobile = Data.Phone_1;
                UserName = Data.Employee_Name;
            }






           
            var Template_Mail_Message = (from a in entity.RLT_Template_Master
                             where a.PKID==TemplateId
                             select new M_Template_Mail_Message
                             {
                                 Subject= a.Subject,
                                 Message_Body = a.Message_Body,
                                 MailId= UserMailId,
                                 MobileNo= UserMobile
                             }).SingleOrDefault();


            if (Template_Mail_Message != null)
            {
                Template_Mail_Message.Subject = SubjectStringReplace(Template_Mail_Message.Subject, UserName);
                Template_Mail_Message.Message_Body = BodyStringReplace(Template_Mail_Message.Message_Body, UserName);
                return Json(Template_Mail_Message, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }

        public string SubjectStringReplace(string Subject = "",string UserName="")
        {
            Subject= Subject.Replace("$Vendor$", UserName);
            Subject = Subject.Replace("$CarOwner$", UserName);
            Subject = Subject.Replace("$Driver$", UserName);
            Subject = Subject.Replace("$Employee$", UserName);
            return Subject;
        }
        public string BodyStringReplace(string Body = "", string UserName = "")
        {
            Body= Body.Replace("$Vendor$", UserName);
            Body= Body.Replace("$CarOwner$", UserName);
            Body= Body.Replace("$Driver$", UserName);
            Body= Body.Replace("$Employee$", UserName);
            return Body;
        }
        #endregion
    }
}
﻿using AdminApp.Filters;
using AdminApp.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;

namespace AdminApp
{
    public class MvcApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            UnityConfig.RegisterComponents();
        }
        protected void Application_PostAuthenticateRequest(Object sender, EventArgs e)
        {

            HttpCookie authCookie = Request.Cookies[FormsAuthentication.FormsCookieName];
            if (authCookie != null)
            {

                FormsAuthenticationTicket authTicket = FormsAuthentication.Decrypt(authCookie.Value);

                CustomPrincipalSerializeModel serializeModel = JsonConvert.DeserializeObject<CustomPrincipalSerializeModel>(authTicket.UserData);
                CustomPrincipal newUser = new CustomPrincipal(authTicket.Name);
                newUser.userId = serializeModel.userId;
                newUser.UserName = serializeModel.UserName;
                newUser.UserEmailID = serializeModel.UserEmailID;
                newUser.Is2TfaAuthentication = serializeModel.Is2TfaAuthentication;
                newUser.UserFirstName = serializeModel.UserFirstName;
                newUser.UserLastName = serializeModel.UserLastName;
                newUser.roles = serializeModel.roles;
                newUser.roleId = serializeModel.roleId;
                newUser.userPhoto = serializeModel.userPhoto;
                HttpContext.Current.User = newUser;
            }

        }

        
    }
}

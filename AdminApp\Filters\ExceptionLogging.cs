﻿using Common.Lib.Model;
using DBLinker.Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace AdminApp.Filters
{
    public class ExceptionLogging
    {

        public static string GetExceptionsData(Exception filterContext, System.Web.Mvc.Controller controller)
        {
            string str = "";
            int loginUserId =Common.Lib.Impl.CommonMethods.SB_TryParseInt32(Convert.ToString((HttpContext.Current.Session["UserId"] ?? "")));

            //StackTrace st = new StackTrace(ex, true);

            ////Get the first stack frame
            //StackFrame frame = st.GetFrame(0);

            ////Get the file name
            //string fileName = frame.GetFileName();

            ////Get the method name
            //string methodName = frame.GetMethod().Name;

            ////Get the line number from the stack frame
            //int line = frame.GetFileLineNumber();

            ////Get the column number
            //int col = frame.GetFileColumnNumber();

            //string controllerName = this.ControllerContext.RouteData.Values["controller"].ToString();
            //string controllerName = this.ControllerContext.RouteData.Values["action"].ToString();

            System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace(filterContext, true);
            System.Diagnostics.StackFrame sf = st.GetFrame(0);
            string filename = sf.GetFileName();
            string methodname = sf.GetMethod().Name;

            //int line_no = 0;
            //int.TryParse(sf.GetFileLineNumber().ToString(), out line_no);

            ExceptionLogger logger = new ExceptionLogger()
            {
                StackTrace = filterContext.StackTrace.ToString(),
                ExceptionMessage = filterContext.Message,
                LineNumber = st.GetFrame(0).GetFileLineNumber(),
                //MethodName = controller.ControllerContext.RouteData.Values["action"].ToString(),
                //ControllerName = controller.ControllerContext.RouteData.Values["controller"].ToString(),
                MethodName = methodname,
                ControllerName = filename,
                LoginUserId = loginUserId,
                LogTime = DateTime.UtcNow
            };

            using (RLTDBContext db = new RLTDBContext())
            {

               // db.RLT_LOG.Add(logger);
              //  db.ExceptionLoggers.Add(logger);


               // db.SaveChanges();
            }
            return str;
        }


    }
}
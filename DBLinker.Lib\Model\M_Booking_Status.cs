﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace DBLinker.Lib.Model
{
  public  class M_Booking_Status
    {       

      
        public int PKID { get; set; }
        [Remote("Check_Booking_Status", "Common", ErrorMessage = "Booking Status already exists!")]
        public string BOOKING_STATUS { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<int> Created_By { get; set; }
        public Nullable<int> Modified_By { get; set; }
        public Nullable<System.DateTime> Modified_Date { get; set; }

    }
}

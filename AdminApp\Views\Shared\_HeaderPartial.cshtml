﻿@using AdminApp.Filters;
@{ 

}
<!-- main header -->
<div class="page-header navbar navbar-fixed-top">
    <div class="page-header-inner ">
        <!-- logo start -->
        <div class="page-logo">
            <a href="~/Admin/Dashboard">
                <img alt="" src="~/Content/img/RLT TOURS.png" style="width:150px;height:50px">

            </a>
        </div>
        <!-- logo end -->
        <ul class="nav navbar-nav navbar-left in">
            <li><a href="#" class="menu-toggler sidebar-toggler"><i class="icon-menu"></i></a></li>
        </ul>
        <form class="search-form-opened" action="#" method="GET">
            <div class="input-group">
                <input type="text" id="searchBarField" class="form-control" placeholder="Search..." name="searchBarField">
                <span class="input-group-btn search-btn">
                    <a href="#" class="btn submit">
                        <i class="icon-magnifier"></i>
                    </a>
                </span>
            </div>
        </form>
        <!-- start mobile menu -->
        <a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse">
            <span></span>
        </a>
        <!-- end mobile menu -->
        <!-- start header menu -->
        <div class="top-menu">
            <ul class="nav navbar-nav pull-right">
                @if (Session["MenuList"] == null)
                {
                    Response.Redirect("~/Login/Index?returnUrl=" + Server.UrlEncode(Request.Url.AbsoluteUri));
                }
                else 
                {
                <li class="dropdown dropdown-user">
                    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <img alt="" class="img-circle " src="../@(((AdminApp.Filters.CustomPrincipal)(User)).userPhoto != null ? Convert.ToString(((AdminApp.Filters.CustomPrincipal)(User)).userPhoto) : "Docs/No_image_available.png" )"  onerror="this.onerror=null;this.src='../Docs/No_image_available.png';" />
                        <span class="username username-hide-on-mobile"> @Convert.ToString(((AdminApp.Filters.CustomPrincipal)(User)).UserName) </span>
                        <i class="fa fa-angle-down"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-default animated jello">
                        <li>
                            <a href="#">
                                <i class="icon-user"></i> Profile
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="icon-settings"></i> Settings
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="icon-directions"></i> Help
                            </a>
                        </li>
                        <li class="divider"> </li>
                        <li>
                            <a href="~/Admin/TwoFactorAuth">
                                <i class="icon-lock"></i> 2FA
                                @{

                                    if (Convert.ToBoolean(((AdminApp.Filters.CustomPrincipal)(User)).Is2TfaAuthentication) == false)
                                    {
                                        <span class="label label-sm label-danger"> (InActive) </span>
                                    }
                                    else
                                    {

                                        <span class="label label-sm label-success"> (Active) </span>
                                    }
                                }

                            </a>

                        </li>
                        <li>
                            <a href="~/Admin/LogOut">
                                <i class="icon-logout"></i> Log Out
                            </a>
                        </li>
                    </ul>
                </li>
                <!-- end manage user dropdown -->
                }
            </ul>
        </div>
    </div>
</div>
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />
<div class="searchableLinksContainer">
    <div class="searchableLinks">

    </div>
</div>
<!-- main header end -->

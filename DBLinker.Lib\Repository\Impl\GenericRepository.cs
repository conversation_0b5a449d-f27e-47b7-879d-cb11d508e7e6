﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DBLinker.Lib.Repository.Impl
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {

        RLTDBContext _entities;
        public GenericRepository()
        {
            _entities = new RLTDBContext();
        }

        public virtual IQueryable<T> GetAll()
        {

            IQueryable<T> query = _entities.Set<T>();
            return query;
        }

        public IQueryable<T> FindBy(System.Linq.Expressions.Expression<Func<T, bool>> predicate)
        {

            IQueryable<T> query = _entities.Set<T>().Where(predicate);
            return query;
        }

        public virtual void Add(T entity)
        {
            _entities.Set<T>().Add(entity);
        }

        public virtual void Delete(T entity)
        {
            _entities.Set<T>().Remove(entity);
        }

        public virtual void Edit(T entity)
        {
            _entities.Entry(entity).State = System.Data.Entity.EntityState.Modified;
        }

        public virtual void Save()
        {
            using (System.Data.Entity.DbContextTransaction dbTran = _entities.Database.BeginTransaction())
            {
                try
                {
                    _entities.SaveChanges();
                    //commit transaction
                    dbTran.Commit();
                }
                catch (Exception ex)
                {
                    //Rollback transaction if exception occurs
                    dbTran.Rollback();
                }

            }
        }

        public void Dispose()
        {
            _entities.Dispose();
        }



    }
}

﻿$(document).ready(function () {
    $("#CarManager").parent().parent().find("a").next().slideToggle(500);
    $("#CarManager").parent().parent().toggleClass('toggled');
    $("#CarManager").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();

    $("#btnAdd").click(function () {
        $("#Is_Active").val(true); //by default set car detail true when adding new
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp(); 
    });

    $("#btnSave").click(function () {
        if (IsValidate()) { 
            var formData = new FormData($("#idFormCarAdd")[0]);
            $.ajax({
                url: 'AddCar',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Infromations Successfully Saved!", "Car Details Add");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Saved,please try it again !", "Error");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) { 
            var formData = new FormData($("#idFormCarAdd")[0]);
            $.ajax({
                url: 'CarEdit',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Car Infromations Successfully Updated!", "Car Details Update!");
                        document.location.reload();
                    }
                    else {
                        toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error"); 
                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            }); 
        }
    });

    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });

    $("#Vendor_PKID").change(function () {
        BindCarOwner(0);
    });
});

function Edit(Id) {
    $.ajax({
        url: 'CarEdit',
        type: "GET",
        data: { pkid: Id },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                // fill data 
                $("#PKID").val(data.PKID);
                $("#Is_Active").val((data.Is_Active != null ? data.Is_Active : false));
                $("#Vendor_PKID").val(data.Vendor_PKID);
                BindCarOwner(data.Car_Owner_PKID);
                $("#Car_Number").val(data.Car_Number);
                $("#Car_Purchase_Year").val(data.Car_Purchase_Year);
                $("#Car_Manufacturing_Year").val(data.Car_Manufacturing_Year);
                $("#Car_Company_PKID").val(data.Car_Company_PKID);
                $("#Car_Model_PKID").val(data.Car_Model_PKID);
                $("#Car_Registered_City_PKID").val(data.Car_Registered_City_PKID);
                if (data.Car_Fuel_Type_Status == "CNG") $("#Car_Fuel_Type_Status1").prop("checked", true);
                if (data.Car_Fuel_Type_Status == "Petrol") $("#Car_Fuel_Type_Status2").prop("checked", true);
                if (data.Car_Fuel_Type_Status == "Diesel") $("#Car_Fuel_Type_Status3").prop("checked", true);
                // set default values
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);
                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
            }
            else {
                toastr.error("Sorry Data Not Found ,Please Try it Again !" , "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !"  , "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" , "Error");
        }
    });




}


function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);

    $("#Vendor_PKID").val($("#Vendor_PKID_" + Id).val());
    BindCarOwner($("#Car_Owner_PKID_" + Id).val());

    $("#Car_Number").val($("#Car_Number_" + Id).val());
    $("#Car_Purchase_Year").val($("#Car_Purchase_Year_" + Id).val());
    $("#Car_Company_PKID").val($("#Car_Company_PKID_" + Id).val());
    $("#Car_Model_PKID").val($("#Car_Model_PKID_" + Id).val());

    $("#Car_Registered_City_PKID").val($("#Car_Registered_City_PKID_" + Id).val());


    if ($("#Car_Fuel_Type_Status_" + Id).val() == "CNG")
        $("#Car_Fuel_Type_Status1").prop("checked", true);
    else if ($("#Car_Fuel_Type_Status_" + Id).val() == "Petrol")
        $("#Car_Fuel_Type_Status2").prop("checked", true);
    else
        $("#Car_Fuel_Type_Status3").prop("checked", true);


    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {
        $.ajax({
            url: 'AddCar',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            contentType: false,
            enctype: 'multipart/form-data',
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Car Infromations Successfully Updated!", "Car Details Delete !");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    if (Id > 0) {
        $.ajax({
            url: 'CarActivateDeactivate',
            type: "POST",
            data: { pkid: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Car Status Successfully Updated!", "Car Status Update !");
                    document.location.reload();
                }
                else {
                    toastr.error("Sorry Data Not Updated,Please Try it Again !", "Error");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}

function resetForm() {
    $("#Vendor_PKID").val('');
    $("#Car_Owner_PKID").val('');
    $("#Car_Number").val('');
    $("#Car_Purchase_Year").val('');
    $("#Car_Company_PKID").val('');
    $("#Car_Model_PKID").val('');
    $("#Car_Registered_City_PKID").val('');
    $("#Car_Manufacturing_Year").val('');

}

function IsValidate() {
    if ($("#Vendor_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Vendor!", "Required!");
        return false;
    }
    if ($("#Car_Owner_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Owner!", "Required!");
        return false;
    }
    if ($("#Car_Number").val() == "") {
        toastr.clear();
        toastr.error("Please enter Car Number!", "Required!");
        return false;
    }
    /*Repeat Validation*/
    if ($("#Car_Number").val() != "") {
        if ($("#Car_Number-error").html() !== undefined) {
            toastr.clear();
            toastr.error("Car Number already exists!", "Required!");
            return false;
        }
    }
    if ($("#Car_Purchase_Year").val() == "") {
        toastr.clear();
        toastr.error("Please enter Purchase Year!", "Required!");
        return false;
    }
    if ($("#Car_Company_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please selrct Car Company!", "Required!");
        return false;
    }
    if ($("#Car_Model_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Model!", "Required!");
        return false;
    }
    if ($("#Car_Registered_City_PKID").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Registered City!", "Required!");
        return false;
    }
    if ($("#Car_Manufacturing_Year").val() == "") {
        toastr.clear();
        toastr.error("Please enter Car Manufacturing Year!", "Required!");
        return false;
    }

    return true;
}


function popupSubWindow(mylink) {
    if (!window.focus) return true; var href;
    if (typeof (mylink) == 'string') href = mylink;
    else href = mylink.href; window.open(href, 'HSMS', 'left=300,top=80,width=850,height=500,toolbar=1,scrollbars=yes'); return false;
}


function BindCarOwner(selectId) {
    $("#Car_Owner_PKID").empty();
    $.ajax({
        type: 'POST',
        url: 'GetOwner', // Calling json method  
        dataType: 'json',
        data: { id: $("#Vendor_PKID").val() },
        success: function (CarOwner) {
            $("#Car_Owner_PKID").append('<option value=>-- Select Car Owner --</option>');
            $.each(CarOwner, function (i, CarOwner) {
                $("#Car_Owner_PKID").append('<option value="' + CarOwner.Value + '">' +
                    CarOwner.Text + '</option>');
            });
            if (selectId != 0)
                $("#Car_Owner_PKID").val(selectId);

        }
    });
    return false;
}



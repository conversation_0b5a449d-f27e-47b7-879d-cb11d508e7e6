﻿@model  IEnumerable<DBLinker.Lib.RLT_MENU_MASTER>
@{
    ViewBag.Title = "Menu List";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<link href="~/Content/assets/css/custom.css" rel="stylesheet" />
<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Cab <PERSON><PERSON></div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="index.html">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Menu List</li>
                </ol>
            </div>
        </div>
        <!-- start widget -->

        <div class="row" id="dvAddUpdate">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Menu Details</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                            data-mdl-for="panel-button">
                            <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                            <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                            <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                        </ul>
                    </div>
                    @using (Html.BeginForm("MenuList", "UserManagement", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormUserManagementMenuList" }))
                    {
                        @Html.AntiForgeryToken()
                        @Html.Hidden("PKID")
                        <div class="card-body">
                            <div class="row">

                                <div class="col-lg-12 p-t-20 row">
                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="MenuName" name="MenuName" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Menu Name</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="MenuIcon" name="MenuIcon" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Menu Icon Class</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="MenuURL" name="MenuURL" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Menu Url</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="PageId" name="PageId" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Page Id</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ParentMenuId" name="ParentMenuId" type="number" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Parent Menu Id</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ControllerName" name="ControllerName" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Controller Name</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ActionName" name="ActionName" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Action Name</label>
                                        </div>
                                    </div>

                                    <div class="col-lg-4 p-t-20">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded" data-upgraded=",MaterialTextfield">
                                            <div class="mdl-textfield__input">
                                                <div style="float:left;">
                                                    <input id="IsActiveTrue" name="IsActive" type="radio" value="true">
                                                    <label for="radio1"><span><span></span></span>Active</label>
                                                </div>
                                                <div style="float:left;">
                                                    <input id="IsActiveFalse" name="IsActive" type="radio" value="false">
                                                    <label for="radio2"><span><span></span></span>Deactive</label>
                                                </div>

                                                <div style="clear:both;"></div>

                                            </div>

                                            <label class="mdl-textfield__label"><span>Set Menu Active/Deactive</span> </label>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="OrderNumber" name="OrderNumber" type="number" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Menu Order Number</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-5 p-t-20 ">
                                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                            <input style="font-weight:bold;" class="mdl-textfield__input" id="ActiveMenuClass" name="ActiveMenuClass" type="text" value="" data-msgName="#">
                                            <label class="mdl-textfield__label">Active Menu Class</label>
                                        </div>
                                    </div>

                                </div>


                            </div>


                            <div class="col-lg-12 p-t-20 text-center">
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                                <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnUpdate">Update</button>
                                <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>


        @*//div list pannel for menu`s *@
        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Menu List</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    <a href="#" id="btnAdd" class="btn btn-info">
                                        Add New <i class="fa fa-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group pull-right">
                                    <a class="btn deepPink-bgcolor  btn-outline dropdown-toggle" data-toggle="dropdown">
                                        Tools
                                        <i class="fa fa-angle-down"></i>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-print"></i> Print
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-pdf-o"></i> Save as PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:;">
                                                <i class="fa fa-file-excel-o"></i> Export to Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <td>Icon</td>
                                        <th>Menu<br />Name</th>
                                        <th>Action<br />Name</th>
                                        <th>Controller<br />Name</th>
                                        <th>Date<br />Created</th>
                                        <th>Order<br />Number</th>
                                        <th>Status</th>
                                        <th style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null)
                                    {
                                        foreach (var item in Model)
                                        {
                                            <tr class="odd gradeX">
                                                <td>

                                                    @Html.Raw(item.MenuIcon != null ? "<i class='" + item.MenuIcon + "'></i>" : "<i class='fa fa-file-text-o'></i>")
                                                </td>
                                                <td>
                                                    <a href="@item.MenuURL" target="_blank" title="@item.MenuURL">@item.MenuName</a>
                                                </td>
                                                <td>@(item.ActionName != null ? item.ActionName : "n/a")</td>
                                                <td>@(item.ControllerName != null ? item.ControllerName : "n/a")</td>
                                                <td>
                                                    @(item.CreatedDate != null
                                                ? Html.Raw(Convert.ToDateTime(item.CreatedDate).ToString("dddd") + "<br />"
                                                + Convert.ToDateTime(item.CreatedDate).ToString("dd MMMM yyyy"))
                                                : Html.Raw("n/a"))
                                                </td>
                                                <td>@item.OrderNumber</td>
                                                <td>
                                                    @Html.Raw(item.IsActive != null && item.IsActive != false
                                                   ? "<span class='label label-sm label-success'> Active </span>"
                                                   : "<span class='label label-sm label-danger'> InActive </span>")
                                                </td>
                                                <td class="valigntop">
                                                    <div class="btn-group">
                                                        <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                            Actions
                                                            <i class="fa fa-angle-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" role="menu">
                                                            <li>
                                                                <a onclick="ActivateDeactivate(@item.PKID)" title="Enable/Disable">
                                                                    <i class="material-icons">check</i> Enable/Disable
                                                                </a>
                                                            </li>

                                                            <li>
                                                                <a onclick="Edit(@item.PKID)" title="Edit">
                                                                    <i class="material-icons">mode_edit</i> Edit
                                                                </a>
                                                            </li> 
                                                            @*<li>
                                                                    <a href="../VendorManagement/VendorManagerView?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.ID), "Vendor")" title="View Most favorite routes Info">
                                                                        <i class="material-icons">remove_red_eye</i>
                                                                        View
                                                                    </a>
                                                                </li>*@
                                                        </ul>
                                                    </div>
                                                </td>

                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @*//End div list pannel for vendor`s *@
    </div>
</div>
<script src="~/Scripts/CrudFile/UserManagement-MenuList.js"></script>

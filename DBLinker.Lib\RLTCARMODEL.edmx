﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="RLTCARMODEL.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="__EFMigrationsHistory">
          <Key>
            <PropertyRef Name="MigrationId" />
          </Key>
          <Property Name="MigrationId" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductVersion" Type="nvarchar" MaxLength="32" Nullable="false" />
        </EntityType>
        <EntityType Name="C__EFMigrationsHistory">
          <Key>
            <PropertyRef Name="MigrationId" />
          </Key>
          <Property Name="MigrationId" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductVersion" Type="nvarchar" MaxLength="32" Nullable="false" />
        </EntityType>
        <EntityType Name="Products">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CreatedBy" Type="nvarchar(max)" />
          <Property Name="Created" Type="datetime" Nullable="false" />
          <Property Name="LastModifiedBy" Type="nvarchar(max)" />
          <Property Name="LastModified" Type="datetime" />
          <Property Name="Name" Type="nvarchar(max)" />
          <Property Name="Barcode" Type="nvarchar(max)" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="Rate" Type="decimal" Precision="18" Scale="6" Nullable="false" />
        </EntityType>
        <EntityType Name="RLT_AboutUs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="AboutUsDetails" Type="varchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ADMIN_ROLE">
          <Key>
            <PropertyRef Name="RoleId" />
          </Key>
          <Property Name="RoleId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Role" Type="nvarchar" MaxLength="50" />
          <Property Name="Role_Description" Type="nvarchar" MaxLength="500" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ADMIN_USER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RoleId" Type="int" />
          <Property Name="UserName" Type="nvarchar" MaxLength="50" />
          <Property Name="UserPWD" Type="nvarchar" MaxLength="50" />
          <Property Name="UserPhoto" Type="nvarchar" MaxLength="500" />
          <Property Name="UserFirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="UserMiddleName" Type="varchar" MaxLength="50" />
          <Property Name="UserLastName" Type="nvarchar" MaxLength="50" />
          <Property Name="UserEmailID" Type="nvarchar" MaxLength="100" />
          <Property Name="UserMobileNo" Type="nvarchar" MaxLength="20" />
          <Property Name="VendorId" Type="int" />
          <Property Name="CarOwnerId" Type="int" />
          <Property Name="EmployeeId" Type="int" />
          <Property Name="AadharId" Type="nvarchar" MaxLength="50" />
          <Property Name="DepartmentId" Type="int" />
          <Property Name="Address" Type="nvarchar" MaxLength="500" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="IsAdmin" Type="int" />
          <Property Name="IsDeleted" Type="bit" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="Is2TfaAuthentication" Type="bit" />
          <Property Name="ID" Type="uniqueidentifier" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="UpdatedBy" Type="int" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Admin_User_Info">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Employee_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Photo" Type="nvarchar" MaxLength="500" />
          <Property Name="DOB" Type="datetime" />
          <Property Name="Phone_1" Type="nvarchar" MaxLength="20" />
          <Property Name="Phone_2" Type="nvarchar" MaxLength="20" />
          <Property Name="Phone_1_IsWhatsup" Type="nvarchar" MaxLength="10" />
          <Property Name="Email" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="nvarchar" MaxLength="50" />
          <Property Name="Marrital_Status" Type="nvarchar" MaxLength="50" />
          <Property Name="Spouses_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Father_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Mother_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Current_Permanent_Same" Type="bit" />
          <Property Name="Current_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Permanent_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Permanent_Address1" Type="nvarchar" MaxLength="250" />
          <Property Name="Permanent_Address2" Type="nvarchar" MaxLength="250" />
          <Property Name="Permanent_Address_State" Type="int" />
          <Property Name="Permanent_Address_City" Type="int" />
          <Property Name="Current_Address1" Type="nvarchar" MaxLength="250" />
          <Property Name="Current_Address2" Type="nvarchar" MaxLength="250" />
          <Property Name="Current_Address_State" Type="int" />
          <Property Name="Current_Address_City" Type="int" />
          <Property Name="C10th_School" Type="nvarchar" MaxLength="50" />
          <Property Name="C10th_PassingYear" Type="nvarchar" MaxLength="50" />
          <Property Name="C10th_Percentage" Type="nvarchar" MaxLength="10" />
          <Property Name="C12th_School" Type="nvarchar" MaxLength="50" />
          <Property Name="C12th_PassingYear" Type="nvarchar" MaxLength="50" />
          <Property Name="C12th_Percentage" Type="nvarchar" MaxLength="10" />
          <Property Name="Degree_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Degree_College" Type="nvarchar" MaxLength="50" />
          <Property Name="Degree_PassingYear" Type="nvarchar" MaxLength="50" />
          <Property Name="Degree_Percentage" Type="nvarchar" MaxLength="10" />
          <Property Name="Master_Degree_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Master_Degree_College" Type="nvarchar" MaxLength="50" />
          <Property Name="Master_Degree_PassingYear" Type="nvarchar" MaxLength="50" />
          <Property Name="Master_Degree_Percentage" Type="nvarchar" MaxLength="10" />
          <Property Name="Employee_ID" Type="nvarchar" MaxLength="50" />
          <Property Name="Department" Type="nvarchar" MaxLength="50" />
          <Property Name="Designation" Type="nvarchar" MaxLength="50" />
          <Property Name="Supervisor" Type="nvarchar" MaxLength="50" />
          <Property Name="Work_City" Type="int" />
          <Property Name="Work_Phone" Type="nvarchar" MaxLength="20" />
          <Property Name="Work_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="Work_StartDate" Type="datetime" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
          <Property Name="UserId" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BANK_NAMES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Bank_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Booking_Id" Type="nvarchar" MaxLength="20" />
          <Property Name="City_From_Id" Type="int" />
          <Property Name="City_To_Id" Type="int" />
          <Property Name="Trip_Type_Id" Type="int" />
          <Property Name="Car_Category_Id" Type="int" />
          <Property Name="Duration" Type="nvarchar" MaxLength="50" />
          <Property Name="Distance" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Basic_Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Driver_Charge" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Toll_Charge" Type="decimal" Precision="18" Scale="2" />
          <Property Name="GST" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="GST_Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Coupon_Code" Type="nvarchar" MaxLength="50" />
          <Property Name="Coupon_Discount" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Booking_Date" Type="datetime" />
          <Property Name="PickUp_Address" Type="nvarchar" MaxLength="200" />
          <Property Name="DropOff_Address" Type="nvarchar" MaxLength="200" />
          <Property Name="PickUp_Date" Type="datetime" />
          <Property Name="PickUp_Time" Type="nvarchar" MaxLength="50" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Mobile_No1" Type="nvarchar" MaxLength="50" />
          <Property Name="Mobile_No2" Type="nvarchar" MaxLength="50" />
          <Property Name="Mail_Id" Type="nvarchar" MaxLength="50" />
          <Property Name="Mode_Of_Payment_Id" Type="int" />
          <Property Name="Vendor_Id" Type="int" />
          <Property Name="Car_Id" Type="int" />
          <Property Name="Booking_Status_Id" Type="int" />
          <Property Name="Booking_Remark" Type="nvarchar" MaxLength="500" />
          <Property Name="Invoice_No" Type="nvarchar" MaxLength="20" />
          <Property Name="Invoice_Date" Type="datetime" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Updated_By" Type="int" />
          <Property Name="razorpay_payment_id" Type="nvarchar" MaxLength="50" />
          <Property Name="razorpay_order_id" Type="nvarchar" MaxLength="50" />
          <Property Name="razorpay_signature" Type="nvarchar" MaxLength="500" />
          <Property Name="razorpay_status" Type="nvarchar" MaxLength="50" />
          <Property Name="PickUpAddressLatitude" Type="nvarchar" MaxLength="100" />
          <Property Name="PickUpAddressLongitude" Type="nvarchar" MaxLength="100" />
          <Property Name="BookingEditRemark" Type="nvarchar" MaxLength="300" />
          <Property Name="driver_id" Type="int" />
          <Property Name="completepickupaddress" Type="nvarchar" MaxLength="400" />
          <Property Name="completedropoffpaddress" Type="nvarchar" MaxLength="400" />
          <Property Name="IsFullOnlinePayment" Type="bit" />
          <Property Name="CashAmountToPayDriver" Type="decimal" Precision="18" Scale="0" />
          <Property Name="TollCharge" Type="decimal" Precision="18" Scale="0" />
          <Property Name="PaymentOption" Type="int" />
          <Property Name="DropOffDate" Type="datetime" />
          <Property Name="DropOffTime" Type="nvarchar" MaxLength="10" />
          <Property Name="PaymentDate" Type="datetime" />
          <Property Name="BookedBy" Type="nvarchar" MaxLength="900" />
          <Property Name="IsAdminBooked" Type="bit" />
          <Property Name="PaymentType" Type="nvarchar" MaxLength="10" />
          <Property Name="PartialPaymentAmount" Type="decimal" Precision="18" Scale="2" />
          <Property Name="RemainingAmountForDriver" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Booking_Created_By" Type="nvarchar" MaxLength="450" />
          <Property Name="payment_link" Type="nvarchar" MaxLength="500" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_FARE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="City_From" Type="int" />
          <Property Name="City_To" Type="int" />
          <Property Name="Trip_Type_Id" Type="int" />
          <Property Name="Car_Category_Id" Type="int" />
          <Property Name="Basic_Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Driver_Charge" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Toll_Charge" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Total_Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="GST" Type="decimal" Precision="18" Scale="2" />
          <Property Name="GST_Amount" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Final_Fare" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Remark" Type="nvarchar" MaxLength="500" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Updated_By" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_GST">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GST" Type="decimal" Precision="18" Scale="2" />
          <Property Name="SGST" Type="decimal" Precision="18" Scale="2" />
          <Property Name="CGST" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Updated_By" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_MODE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Booking_Mode_Name" Type="varchar" MaxLength="20" />
          <Property Name="Counrty_Abbr" Type="varchar" MaxLength="5" />
          <Property Name="Is_Active" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_PAYMENT_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Passenger_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Passenger_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="Passenger_Phone" Type="nvarchar" MaxLength="15" />
          <Property Name="Passenger_Alt_Phone" Type="nvarchar" MaxLength="15" />
          <Property Name="Passenger_Special_Request_Comment" Type="nvarchar" MaxLength="150" />
          <Property Name="Passenger_Trip_Purpose" Type="nvarchar" MaxLength="15" />
          <Property Name="Passenger_Trip_Type_PKID" Type="int" />
          <Property Name="Passenger_Pickup_City_PKID" Type="int" />
          <Property Name="Passenger_Pickup_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Passenger_Dropoff_City_PKID" Type="int" />
          <Property Name="Passenger_Dropoff_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Booking_Login_PKID" Type="bigint" />
          <Property Name="Base_Fare" Type="float" />
          <Property Name="Driver_Allowance" Type="float" />
          <Property Name="Service_Tax" Type="float" />
          <Property Name="Discount" Type="float" />
          <Property Name="Discount_Coupon" Type="nvarchar" MaxLength="50" />
          <Property Name="Total_Booking_Amount" Type="float" />
          <Property Name="Payment_Mode_PKID" Type="int" />
          <Property Name="Payment_Amount_Recieved" Type="float" />
          <Property Name="Payment_Need_to_Collect" Type="float" />
          <Property Name="Payment_Issue_Comment" Type="nvarchar" MaxLength="250" />
          <Property Name="Payment_TransactionId" Type="nvarchar" MaxLength="50" />
          <Property Name="Payment_Transaction_Status" Type="varchar" MaxLength="20" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_RULES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Trip_Type_Id" Type="int" />
          <Property Name="Car_Category_Id" Type="int" />
          <Property Name="Distance_From" Type="int" />
          <Property Name="Distance_To" Type="int" />
          <Property Name="NewDistance" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Updated_By" Type="int" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_STATUS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="BOOKING_STATUS" Type="nvarchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Modified_By" Type="int" />
          <Property Name="Modified_Date" Type="datetime" />
        </EntityType>
        <EntityType Name="RLT_BOOKINGS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Booking_Id" Type="nvarchar" MaxLength="30" />
          <Property Name="City_From_Id" Type="nvarchar" MaxLength="5" />
          <Property Name="City_To_Id" Type="nvarchar" MaxLength="5" />
          <Property Name="PickUp_Address" Type="nvarchar" MaxLength="200" />
          <Property Name="DropOff_Address" Type="nvarchar" MaxLength="200" />
          <Property Name="PickUpLatLong" Type="nvarchar" MaxLength="200" />
          <Property Name="DropOffLatLong" Type="nvarchar" MaxLength="200" />
          <Property Name="TravelerName" Type="nvarchar" MaxLength="200" />
          <Property Name="TravelerEmail" Type="nvarchar" MaxLength="30" />
          <Property Name="TravelerPhone" Type="nvarchar" MaxLength="30" />
          <Property Name="BookingSessionID" Type="nvarchar" MaxLength="30" />
          <Property Name="PaymentOption" Type="nvarchar" MaxLength="30" />
          <Property Name="PaymentMode" Type="nvarchar" MaxLength="30" />
          <Property Name="IsWhatsAppNumber" Type="bit" />
          <Property Name="CashToPayDriver" Type="nvarchar" MaxLength="5" />
          <Property Name="DriverNightCharge" Type="nvarchar" MaxLength="5" />
          <Property Name="Trip_Type_Id" Type="nvarchar" MaxLength="5" />
          <Property Name="Car_Category_Id" Type="nvarchar" MaxLength="5" />
          <Property Name="Duration" Type="nvarchar" MaxLength="50" />
          <Property Name="Distance" Type="nvarchar" MaxLength="5" />
          <Property Name="Basic_Fare" Type="nvarchar" MaxLength="5" />
          <Property Name="Driver_Charge" Type="nvarchar" MaxLength="5" />
          <Property Name="Toll_Charge" Type="nvarchar" MaxLength="10" />
          <Property Name="GST" Type="nvarchar" MaxLength="5" />
          <Property Name="Fare" Type="nvarchar" MaxLength="10" />
          <Property Name="GST_Fare" Type="nvarchar" MaxLength="10" />
          <Property Name="Coupon_Code" Type="nvarchar" MaxLength="50" />
          <Property Name="Coupon_Discount" Type="nvarchar" MaxLength="30" />
          <Property Name="Booking_Date" Type="datetime" />
          <Property Name="PickUp_Date" Type="datetime" />
          <Property Name="PickUp_Time" Type="nvarchar" MaxLength="50" />
          <Property Name="Vendor_Id" Type="nvarchar" MaxLength="10" />
          <Property Name="Car_Id" Type="nvarchar" MaxLength="10" />
          <Property Name="Booking_Status_Id" Type="nvarchar" MaxLength="10" />
          <Property Name="Booking_Remark" Type="nvarchar" MaxLength="500" />
          <Property Name="Invoice_No" Type="nvarchar" MaxLength="20" />
          <Property Name="Invoice_Date" Type="datetime" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="nvarchar" MaxLength="10" />
          <Property Name="Updated_By" Type="nvarchar" MaxLength="10" />
          <Property Name="razorpay_payment_id" Type="nvarchar" MaxLength="50" />
          <Property Name="razorpay_order_id" Type="nvarchar" MaxLength="50" />
          <Property Name="razorpay_signature" Type="nvarchar" MaxLength="500" />
          <Property Name="razorpay_status" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="RLT_CAR_CATEGORY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Car_Category_Name" Type="nvarchar" MaxLength="30" />
          <Property Name="Car_Category_Abbr" Type="nvarchar" MaxLength="60" />
          <Property Name="Per_KM_fare" Type="float" />
          <Property Name="Capacity" Type="int" />
          <Property Name="Features" Type="nvarchar" MaxLength="500" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Car_Categroy_Image" Type="nvarchar" MaxLength="150" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
          <Property Name="Base_Fare" Type="decimal" Precision="10" Scale="2" />
        </EntityType>
        <EntityType Name="RLT_CAR_CHARGES_FECILITIES_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Car_Category_PKID" Type="int" />
          <Property Name="Car_Charges_PKM" Type="float" />
          <Property Name="Car_Waiting_Charges_PM" Type="float" />
          <Property Name="Car_Driver_Charges_PD" Type="float" />
          <Property Name="Is_Contain_AC" Type="bit" />
          <Property Name="Total_Number_Seats" Type="nchar" MaxLength="10" />
          <Property Name="Is_Smoking_Allow" Type="bit" />
          <Property Name="Is_Luggage_Allow" Type="bit" />
          <Property Name="Is_Pet_Allow" Type="bit" />
          <Property Name="Inclusive_Charges_Comment" Type="nvarchar" MaxLength="100" />
          <Property Name="Extras_Charges_Comment" Type="nvarchar" MaxLength="100" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_COMPANY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Company_Name" Type="nvarchar" MaxLength="30" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Vendor_PKID" Type="int" />
          <Property Name="Car_Owner_PKID" Type="int" />
          <Property Name="Car_Number" Type="nvarchar" MaxLength="20" />
          <Property Name="Car_Purchase_Year" Type="nvarchar" MaxLength="10" />
          <Property Name="Car_Manufacturing_Year" Type="nvarchar" MaxLength="10" />
          <Property Name="Car_Company_PKID" Type="int" />
          <Property Name="Car_Model_PKID" Type="int" />
          <Property Name="Car_Registered_Document" Type="nvarchar" MaxLength="300" />
          <Property Name="Car_Registered_City_PKID" Type="int" />
          <Property Name="Car_Fuel_Type_Status" Type="nvarchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Update_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_DOCS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Car_PKID" Type="int" Nullable="false" />
          <Property Name="Car_Doc_Id" Type="int" Nullable="false" />
          <Property Name="Car_Doc_EndDate" Type="datetime" />
          <Property Name="Document_Path" Type="nvarchar" MaxLength="150" />
          <Property Name="Is_Verified" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Last_Modified_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_DRIVER_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Vendor_PKID" Type="int" />
          <Property Name="Car_Owner_PKID" Type="int" />
          <Property Name="Car_PKID" Type="bigint" />
          <Property Name="Driver_Name" Type="nvarchar" MaxLength="75" />
          <Property Name="Driver_Photo" Type="nvarchar" MaxLength="150" />
          <Property Name="Driver_DOB" Type="datetime" />
          <Property Name="Driver_FName" Type="nvarchar" MaxLength="20" />
          <Property Name="Driver_MName" Type="nvarchar" MaxLength="20" />
          <Property Name="Driver_LName" Type="nvarchar" MaxLength="20" />
          <Property Name="Drive_Phone_1" Type="nvarchar" MaxLength="15" />
          <Property Name="Drive_Phone_2" Type="nchar" MaxLength="10" />
          <Property Name="Phone_1_IsWhatsup" Type="nvarchar" MaxLength="10" />
          <Property Name="Driver_Email" Type="nvarchar" MaxLength="100" />
          <Property Name="Driver_DL" Type="nvarchar" MaxLength="150" />
          <Property Name="Driver_Aadhaar" Type="nvarchar" MaxLength="150" />
          <Property Name="Driver_Father_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Driver_Mother_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Driver_Marrital_Status" Type="varchar" MaxLength="10" />
          <Property Name="Driver_Gender" Type="varchar" MaxLength="10" />
          <Property Name="Driver_Current_Permanent_Same" Type="bit" />
          <Property Name="Driver_Current_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Permanent_Address" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Permanent_Address1" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Permanent_Address2" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Permanent_Address_State" Type="int" />
          <Property Name="Driver_Permanent_Address_City" Type="int" />
          <Property Name="Driver_Current_Address1" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Current_Address2" Type="nvarchar" MaxLength="250" />
          <Property Name="Driver_Current_Address_State" Type="int" />
          <Property Name="Driver_Current_Address_City" Type="int" />
          <Property Name="Driver_Smoking_Status" Type="varchar" MaxLength="25" />
          <Property Name="Driver_Drinking_Status" Type="varchar" MaxLength="25" />
          <Property Name="Driver_Eating_Type" Type="varchar" MaxLength="25" />
          <Property Name="Driver_Religion" Type="nvarchar" MaxLength="20" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
          <Property Name="Will_Owner_Drive_Car_Status" Type="varchar" MaxLength="10" />
          <Property Name="Driver_Pwd" Type="nvarchar" MaxLength="100" />
          <Property Name="OTPUpdatedDateTime" Type="datetime" />
          <Property Name="OTPNumber" Type="int" />
          <Property Name="UserID" Type="nvarchar" MaxLength="50" />
          <Property Name="Special_Remarking" Type="nvarchar" MaxLength="200" />
        </EntityType>
        <EntityType Name="RLT_CAR_DRIVER_DOCS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CarDriver_PKID" Type="int" Nullable="false" />
          <Property Name="CarDriver_Doc_Id" Type="int" Nullable="false" />
          <Property Name="CarDriver_Doc_EndDate" Type="datetime" />
          <Property Name="Document_Path" Type="nvarchar" MaxLength="150" />
          <Property Name="Is_Verified" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Last_Modified_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_FUEL_TYPES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="CAR_FUEL_TYPE" Type="nvarchar" MaxLength="10" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_MODEL">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Car_Model_Name" Type="nvarchar" MaxLength="20" />
          <Property Name="Car_Category_PKID" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Car_Fuel_Type_Status" Type="int" />
          <Property Name="Car_Company_PKID" Type="int" />
          <Property Name="Car_Segment_PKID" Type="int" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_OWNER_BANK_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Car_Owner_PKID" Type="int" />
          <Property Name="Bank_Name_PKID" Type="int" />
          <Property Name="Bank_IFSC_Code" Type="nvarchar" MaxLength="15" />
          <Property Name="Car_Owner_Bank_Acc_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_Bank_Acc_Number" Type="nvarchar" MaxLength="15" />
          <Property Name="Bank_Acc_Branch_Address" Type="nvarchar" MaxLength="150" />
          <Property Name="Car_Owner_Bank_Registered_Phone" Type="nvarchar" MaxLength="15" />
          <Property Name="Car_Owner_Bank_Cancelled_Chk_Photo" Type="nvarchar" MaxLength="150" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Creatde_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
          <Property Name="Updater_Comment" Type="nvarchar" MaxLength="150" />
        </EntityType>
        <EntityType Name="RLT_CAR_OWNER_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Vendor_PKID" Type="int" />
          <Property Name="Car_Owner_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_Photo" Type="nvarchar" MaxLength="500" />
          <Property Name="Car_Owner_Phone1" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_Phone2" Type="nvarchar" MaxLength="50" />
          <Property Name="Phone1_IsWhatsup" Type="nvarchar" MaxLength="10" />
          <Property Name="Car_Owner_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_DL" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_Aadhaar" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Owner_Address" Type="nvarchar" MaxLength="500" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Update_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CAR_SEGMENT">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Car_Segment" Type="nvarchar" MaxLength="100" />
          <Property Name="Car_Segment_Description" Type="nvarchar" MaxLength="500" />
          <Property Name="Per_KM_fare" Type="float" />
          <Property Name="Capacity" Type="int" />
          <Property Name="features" Type="nvarchar" MaxLength="500" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CITY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="City_Name" Type="varchar" MaxLength="30" />
          <Property Name="City_Abbr" Type="varchar" MaxLength="5" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="State_PKID" Type="int" />
          <Property Name="latitude" Type="nvarchar" MaxLength="50" />
          <Property Name="longitude" Type="nvarchar" MaxLength="50" />
          <Property Name="eLoc" Type="nvarchar" MaxLength="50" />
          <Property Name="orderIndex" Type="int" />
          <Property Name="score" Type="nvarchar" MaxLength="50" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CLASS_MASTER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Class_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Class_Charges_PKMs" Type="float" />
          <Property Name="Waiting_Charges" Type="int" />
          <Property Name="Driver_Night_Charges" Type="int" />
          <Property Name="Other_Charges" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Company_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Company_Name" Type="nvarchar" MaxLength="500" />
          <Property Name="Company_Address" Type="nvarchar" MaxLength="500" />
          <Property Name="State_GSTIN" Type="nvarchar" MaxLength="50" />
          <Property Name="SAC_Code" Type="nvarchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_CONTACT_ENQUIRIES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="30" />
          <Property Name="PhoneNumber" Type="nvarchar" MaxLength="15" />
          <Property Name="EnquiryMessage" Type="nvarchar" MaxLength="300" />
          <Property Name="EMailID" Type="nvarchar" MaxLength="30" />
          <Property Name="RequestDate" Type="datetime" />
          <Property Name="Remark" Type="nvarchar(max)" />
          <Property Name="IsWhatsAppNumber" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ContactInformation">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Address" Type="nvarchar" MaxLength="350" />
          <Property Name="SalesTelNo" Type="nvarchar" MaxLength="170" />
          <Property Name="BusinessEmailNo" Type="nvarchar" MaxLength="200" />
          <Property Name="WorkingHours" Type="nvarchar" MaxLength="250" />
          <Property Name="GPSCordinationLatitude" Type="nvarchar" MaxLength="50" />
          <Property Name="GPSCordinationLongitude" Type="nvarchar" MaxLength="50" />
          <Property Name="isActive" Type="bit" />
          <Property Name="CreateDate" Type="datetime" />
          <Property Name="SalesEmailNo" Type="nvarchar" MaxLength="200" />
          <Property Name="BusinessTelNo" Type="nvarchar" MaxLength="170" />
          <Property Name="GoogleMapLink" Type="nvarchar(max)" />
          <Property Name="FBLink" Type="nvarchar" MaxLength="200" />
          <Property Name="TwitterLink" Type="nvarchar" MaxLength="200" />
          <Property Name="InstagramLink" Type="nvarchar" MaxLength="200" />
          <Property Name="PinsterLink" Type="nvarchar" MaxLength="200" />
          <Property Name="GooglePlusLink" Type="nvarchar" MaxLength="200" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_COUNTRY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Country_Name" Type="varchar" MaxLength="20" />
          <Property Name="Counrty_Abbr" Type="varchar" MaxLength="5" />
          <Property Name="Is_Active" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Department_Name">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DepartmentName" Type="nvarchar" MaxLength="50" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_DISCOUNT_COUPON">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Discount_Coupon" Type="nvarchar" MaxLength="50" />
          <Property Name="Discount" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DiscountImage" Type="nvarchar" MaxLength="250" />
          <Property Name="Coupon_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Coupon_Remark" Type="nvarchar" MaxLength="500" />
          <Property Name="Max_Discount" Type="int" />
          <Property Name="Fare_When_Applied" Type="int" />
          <Property Name="Coupon_Last_Date" Type="datetime" />
          <Property Name="CouponMaxTimeAllowance" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Updated_By" Type="int" />
        </EntityType>
        <EntityType Name="RLT_DOCUMNETS_NAME">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Doc_For" Type="nvarchar" MaxLength="50" />
          <Property Name="Doc_Name" Type="nvarchar" MaxLength="30" />
          <Property Name="Is_Doc_Req" Type="bit" />
          <Property Name="Is_Doc_Expiry_Date" Type="bit" />
          <Property Name="Is_Active" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_DRIVER_APPROVE_STATUS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Status" Type="nvarchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Created_By" Type="int" />
          <Property Name="Modified_Date" Type="datetime" />
          <Property Name="Modified_By" Type="int" />
        </EntityType>
        <EntityType Name="RLT_DRIVER_ENQUIRIES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Driver_Name" Type="nvarchar" MaxLength="150" />
          <Property Name="Driver_Phone_1" Type="nvarchar" MaxLength="15" />
          <Property Name="Driver_Car_No" Type="nvarchar" MaxLength="20" />
          <Property Name="Driver_Mail" Type="nvarchar" MaxLength="50" />
          <Property Name="Driver_Address" Type="nvarchar" MaxLength="500" />
          <Property Name="RequestDate" Type="datetime" />
          <Property Name="Driver_Approving_Status" Type="int" />
          <Property Name="Remark" Type="nvarchar(max)" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Created_BY" Type="int" />
          <Property Name="Updated_BY" Type="int" />
          <Property Name="IsWhatsAppNumber" Type="bit" />
        </EntityType>
        <EntityType Name="RLT_ENTITY_GENERALS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Entity_Action_Code" Type="int" />
          <Property Name="Entity_Flow_Code" Type="int" />
          <Property Name="Entity_Flow_Run_By" Type="int" Nullable="false" />
          <Property Name="Action_Date" Type="datetime" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_FAQ_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="FAQDetails" Type="nvarchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_LOCATION_CODE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ZIP_Code" Type="varchar" MaxLength="10" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Creatded_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="City_PKID" Type="int" />
          <Property Name="Location_Name" Type="nvarchar" MaxLength="150" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_LOG">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="Date" />
            <PropertyRef Name="Thread" />
            <PropertyRef Name="Level" />
            <PropertyRef Name="Logger" />
            <PropertyRef Name="Message" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="Thread" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="Level" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Logger" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="Message" Type="varchar" MaxLength="4000" Nullable="false" />
          <Property Name="Exception" Type="varchar" MaxLength="2000" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_MENU_MASTER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MenuName" Type="nvarchar" MaxLength="50" />
          <Property Name="MenuIcon" Type="nvarchar" MaxLength="50" />
          <Property Name="MenuURL" Type="nvarchar" MaxLength="150" />
          <Property Name="PageId" Type="nvarchar" MaxLength="150" />
          <Property Name="ParentMenuId" Type="int" Nullable="false" />
          <Property Name="IsAdminMenu" Type="int" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="50" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ActiveMenuClass" Type="nvarchar" MaxLength="50" />
          <Property Name="OrderNumber" Type="float" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="UpdatedBy" Type="int" />
          <Property Name="ActionName" Type="nvarchar" MaxLength="150" />
          <Property Name="ControllerName" Type="nvarchar" MaxLength="150" />
        </EntityType>
        <EntityType Name="RLT_NextId">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Type" Type="nvarchar" MaxLength="50" />
          <Property Name="Count" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_PAYMENT_METHOD">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Payment_Method_Name" Type="varchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
          <Property Name="PhonePePaymentMethod" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="RLT_PrivacyPolicy">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PrivacyPolicy" Type="varchar(max)" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ROLE_MENU_MAPPING">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Role_Id" Type="int" Nullable="false" />
          <Property Name="Menu_Id" Type="int" Nullable="false" />
          <Property Name="Is_Add" Type="bit" Nullable="false" />
          <Property Name="Is_Edit" Type="bit" Nullable="false" />
          <Property Name="Is_Delete" Type="bit" Nullable="false" />
          <Property Name="Is_View" Type="bit" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="75" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ROUTE_PLAN">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="From_City_PKID" Type="int" />
          <Property Name="To_City_PKID" Type="int" />
          <Property Name="Car_PKID" Type="int" />
          <Property Name="Fecilities_PKID" Type="int" />
          <Property Name="Car_Class_PKID" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Charges_Master_PKID" Type="int" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_ROUTES_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="Route_Distance_KM" Type="int" />
          <Property Name="Total_Paid_Tolls_In_Route" Type="int" />
          <Property Name="Pickup_City_PKID" Type="int" />
          <Property Name="Dropoff_City_PKID" Type="int" />
          <Property Name="Car_Category_PKID" Type="int" />
          <Property Name="Car_Trip_Type_PKID" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Services">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ServiceName" Type="nvarchar" MaxLength="50" />
          <Property Name="ServiceIconClass" Type="nvarchar" MaxLength="50" />
          <Property Name="ServiceImagePath" Type="nvarchar" MaxLength="50" />
          <Property Name="BriefIntroAboutService" Type="nvarchar" MaxLength="200" />
          <Property Name="AboutService" Type="nvarchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_STATE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="State_Name" Type="varchar" MaxLength="40" />
          <Property Name="State_Abbr" Type="varchar" MaxLength="5" />
          <Property Name="Counrty_PKID" Type="int" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_SUBSCRIBERS">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SubsciberMailID" Type="nvarchar" MaxLength="50" />
          <Property Name="IsValid" Type="bit" />
          <Property Name="SubscriptionDate" Type="datetime" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Template_Mail_Message">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SendToType" Type="int" />
          <Property Name="SendToId" Type="bigint" />
          <Property Name="Mail_SMS_Type" Type="nvarchar" MaxLength="10" />
          <Property Name="TemplateId" Type="int" />
          <Property Name="MobileNo" Type="nvarchar" MaxLength="50" />
          <Property Name="MailId" Type="nvarchar" MaxLength="50" />
          <Property Name="Subject" Type="nvarchar" MaxLength="500" />
          <Property Name="Message_Body" Type="varchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Template_Master">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Mail_SMS_Type" Type="nvarchar" MaxLength="50" />
          <Property Name="Template_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Subject" Type="nvarchar" MaxLength="500" />
          <Property Name="Message_Body" Type="varchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_TermsConditions">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TermsConditions" Type="varchar(max)" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_TRIP_TYPES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Trip_Type" Type="nvarchar" MaxLength="50" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_USER_LOGIN_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PK_GUID" Type="uniqueidentifier" />
          <Property Name="RLT_User_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="RLT_User_Phone" Type="nvarchar" MaxLength="15" />
          <Property Name="RLT_User_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="RLT_User_Login_Signup_Method" Type="nvarchar" MaxLength="20" />
          <Property Name="RLT_User_Aggreed" Type="nchar" MaxLength="10" />
          <Property Name="RLT_User_Pwd" Type="nvarchar" MaxLength="20" />
          <Property Name="RLT_User_Gender" Type="varchar" MaxLength="10" />
          <Property Name="RLT_User_DOB" Type="datetime" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Last_Login_Date_Time" Type="datetime" />
          <Property Name="RLT_User_2FA_Auth_Status" Type="bit" />
          <Property Name="OTPNumber" Type="int" />
          <Property Name="OTPUpdatedDateTime" Type="datetime" />
          <Property Name="UserID" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="RLT_UserFeedBack">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="EmailID" Type="nvarchar" MaxLength="50" />
          <Property Name="PhoneNo" Type="nvarchar" MaxLength="50" />
          <Property Name="Country" Type="nvarchar" MaxLength="50" />
          <Property Name="Photo" Type="nvarchar" MaxLength="150" />
          <Property Name="Message" Type="nvarchar(max)" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreateDate" Type="datetime" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Bank_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Bank_Name_PKID" Type="int" />
          <Property Name="IFSC_CODE" Type="nvarchar" MaxLength="15" />
          <Property Name="Account_Holder_Name" Type="nvarchar" MaxLength="60" />
          <Property Name="Account_Number" Type="varchar" MaxLength="20" />
          <Property Name="Vendor_Ref_PKID" Type="int" Nullable="false" />
          <Property Name="Bank_Status" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="LastModified_Date" Type="datetime" />
          <Property Name="Lastmodified_By" Type="int" Nullable="false" />
          <Property Name="Is_active" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_VENDOR_CAR_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Car_Company_Ref" Type="int" Nullable="false" />
          <Property Name="Car_Segment_Ref" Type="int" Nullable="false" />
          <Property Name="Car_Fuel_Type_Ref" Type="int" Nullable="false" />
          <Property Name="Car_Number" Type="nvarchar" MaxLength="50" />
          <Property Name="Car_Registration_Date" Type="datetime" />
          <Property Name="Driver_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Driver_Phone1" Type="nvarchar" MaxLength="15" />
          <Property Name="Driver_Phone2" Type="nvarchar" MaxLength="15" />
          <Property Name="Vendor_Ref_ID" Type="int" Nullable="false" />
          <Property Name="Is_Active" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Last_Modified_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Vendor_Company_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Vendor_Owner_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Vendor_Photo" Type="nvarchar" MaxLength="500" />
          <Property Name="Vendor_EmailID" Type="nvarchar" MaxLength="30" />
          <Property Name="Vendor_Phone1" Type="nvarchar" MaxLength="15" />
          <Property Name="Vendor_Phone2" Type="nvarchar" MaxLength="15" />
          <Property Name="Phone1_IsWhatsup" Type="nvarchar" MaxLength="10" />
          <Property Name="Vendor_Address" Type="nvarchar" MaxLength="200" />
          <Property Name="Vendor_Member_Id" Type="nvarchar" MaxLength="15" />
          <Property Name="Vendor_LoginID" Type="nvarchar" MaxLength="15" />
          <Property Name="Vendor_Pwd" Type="nvarchar" MaxLength="15" />
          <Property Name="Is_Active" Type="bit" />
          <Property Name="Is_2FA_Activated" Type="bit" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Updated_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Docs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Vendor_Doc_Id" Type="int" Nullable="false" />
          <Property Name="Vendor_Ref_PKID" Type="int" Nullable="false" />
          <Property Name="Document_Path" Type="nvarchar" MaxLength="150" />
          <Property Name="Is_Verified" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Last_Modified_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="Comments" Type="nvarchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLTBookingAuditTrails">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLTConfigurationAuditTrails">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RequestDataChange" Type="nvarchar(max)" />
          <Property Name="ResourceActionCode" Type="nvarchar" MaxLength="30" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLTMostFavoriteRoutes">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PickUpCityID" Type="int" />
          <Property Name="DropOffCityID" Type="int" />
          <Property Name="CarCategoryID" Type="int" />
          <Property Name="TripType" Type="int" />
          <Property Name="Fare" Type="decimal" Precision="18" Scale="0" />
          <Property Name="GSTFare" Type="decimal" Precision="18" Scale="0" />
          <Property Name="Distance" Type="decimal" Precision="18" Scale="0" />
          <Property Name="TravelDuration" Type="nvarchar" MaxLength="30" />
          <Property Name="Facilities" Type="nvarchar" MaxLength="100" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
          <Property Name="CityImage" Type="nvarchar" MaxLength="200" />
        </EntityType>
        <EntityType Name="RLTResources">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ResourceCode" Type="nvarchar" MaxLength="30" />
          <Property Name="ResourceCodeText" Type="nvarchar" MaxLength="150" />
          <Property Name="ResourceStatus" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RLTResourceStaus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="StatusName" Type="nvarchar" MaxLength="30" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RoleClaims">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RoleId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ClaimType" Type="nvarchar(max)" />
          <Property Name="ClaimValue" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="Roles">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedName" Type="nvarchar" MaxLength="256" />
          <Property Name="ConcurrencyStamp" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="TempBookings">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="User_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Mobile_Number" Type="nvarchar" MaxLength="20" />
          <Property Name="PickUp_Address" Type="nvarchar" MaxLength="150" />
          <Property Name="DropOff_Address" Type="nvarchar" MaxLength="150" />
          <Property Name="Travel_Date" Type="datetime" />
          <Property Name="Travel_Time" Type="nvarchar" MaxLength="10" />
          <Property Name="Is_Active" Type="bit" Nullable="false" />
          <Property Name="Booking_Date" Type="datetime" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="UserClaims">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ClaimType" Type="nvarchar(max)" />
          <Property Name="ClaimValue" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="UserDiscountAvaileds">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserId" Type="nvarchar" MaxLength="900" />
          <Property Name="DiscountCouponId" Type="int" />
          <Property Name="AvailedDate" Type="datetime" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="UpdatedDate" Type="datetime" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="UpdatedBy" Type="int" />
        </EntityType>
        <EntityType Name="UserLogins">
          <Key>
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="ProviderKey" />
          </Key>
          <Property Name="LoginProvider" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ProviderKey" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ProviderDisplayName" Type="nvarchar(max)" />
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
        </EntityType>
        <EntityType Name="UserRoles">
          <Key>
            <PropertyRef Name="Roles_Id" />
            <PropertyRef Name="Users_Id" />
          </Key>
          <Property Name="Roles_Id" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Users_Id" Type="nvarchar" MaxLength="450" Nullable="false" />
        </EntityType>
        <EntityType Name="Users">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="UserName" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedUserName" Type="nvarchar" MaxLength="256" />
          <Property Name="Email" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedEmail" Type="nvarchar" MaxLength="256" />
          <Property Name="EmailConfirmed" Type="bit" Nullable="false" />
          <Property Name="PasswordHash" Type="nvarchar(max)" />
          <Property Name="SecurityStamp" Type="nvarchar(max)" />
          <Property Name="ConcurrencyStamp" Type="nvarchar(max)" />
          <Property Name="PhoneNumber" Type="nvarchar(max)" />
          <Property Name="PhoneNumberConfirmed" Type="bit" Nullable="false" />
          <Property Name="TwoFactorEnabled" Type="bit" Nullable="false" />
          <Property Name="LockoutEnd" Type="datetimeoffset" Precision="7" />
          <Property Name="LockoutEnabled" Type="bit" Nullable="false" />
          <Property Name="AccessFailedCount" Type="int" Nullable="false" />
          <Property Name="FirstName" Type="nvarchar(max)" />
          <Property Name="LastName" Type="nvarchar(max)" />
          <Property Name="UserType" Type="int" Nullable="false" />
          <Property Name="CurrentOTPNumber" Type="nvarchar(max)" />
          <Property Name="OTPExpireTimeInMinute" Type="int" Nullable="false" />
          <Property Name="OTPGeneratedDate" Type="datetime" />
          <Property Name="UserProfilePicture" Type="nvarchar" MaxLength="300" />
        </EntityType>
        <EntityType Name="UserTokens">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="LoginProvider" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Value" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="Vendor_Car_Driver_Docs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Doc_Name" Type="int" Nullable="false" />
          <Property Name="Vendor_Car_Ref" Type="int" Nullable="false" />
          <Property Name="Document_Path" Type="nvarchar" MaxLength="150" />
          <Property Name="Is_Verified" Type="bit" Nullable="false" />
          <Property Name="Created_Date" Type="datetime" />
          <Property Name="Last_Modified_Date" Type="datetime" />
          <Property Name="Last_Modified_By" Type="int" Nullable="false" />
          <Property Name="Comments" Type="nvarchar" MaxLength="250" />
          <Property Name="CreatedBy" Type="int" />
        </EntityType>
        <EntityType Name="RefreshToken">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Token" Type="nvarchar(max)" />
          <Property Name="Expires" Type="datetime2" Precision="7" Nullable="false" />
          <Property Name="Created" Type="datetime2" Precision="7" Nullable="false" />
          <Property Name="CreatedByIp" Type="nvarchar(max)" />
          <Property Name="Revoked" Type="datetime2" Precision="7" />
          <Property Name="RevokedByIp" Type="nvarchar(max)" />
          <Property Name="ReplacedByToken" Type="nvarchar(max)" />
          <Property Name="ApplicationUserId" Type="nvarchar" MaxLength="450" />
        </EntityType>
        <EntityType Name="Role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedName" Type="nvarchar" MaxLength="256" />
          <Property Name="ConcurrencyStamp" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="RoleClaims1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RoleId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ClaimType" Type="nvarchar(max)" />
          <Property Name="ClaimValue" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="User">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="UserName" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedUserName" Type="nvarchar" MaxLength="256" />
          <Property Name="Email" Type="nvarchar" MaxLength="256" />
          <Property Name="NormalizedEmail" Type="nvarchar" MaxLength="256" />
          <Property Name="EmailConfirmed" Type="bit" Nullable="false" />
          <Property Name="PasswordHash" Type="nvarchar(max)" />
          <Property Name="SecurityStamp" Type="nvarchar(max)" />
          <Property Name="ConcurrencyStamp" Type="nvarchar(max)" />
          <Property Name="PhoneNumber" Type="nvarchar(max)" />
          <Property Name="PhoneNumberConfirmed" Type="bit" Nullable="false" />
          <Property Name="TwoFactorEnabled" Type="bit" Nullable="false" />
          <Property Name="LockoutEnd" Type="datetimeoffset" Precision="7" />
          <Property Name="LockoutEnabled" Type="bit" Nullable="false" />
          <Property Name="AccessFailedCount" Type="int" Nullable="false" />
          <Property Name="FirstName" Type="nvarchar(max)" />
          <Property Name="LastName" Type="nvarchar(max)" />
          <Property Name="UserType" Type="int" Nullable="false" />
          <Property Name="UserProfilePicture" Type="nvarchar(max)" />
          <Property Name="CurrentOTPNumber" Type="nvarchar(max)" />
          <Property Name="OTPExpireTimeInMinute" Type="int" Nullable="false" />
          <Property Name="OTPGeneratedDate" Type="datetime2" Precision="7" />
        </EntityType>
        <EntityType Name="UserClaims1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ClaimType" Type="nvarchar(max)" />
          <Property Name="ClaimValue" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="UserLogins1">
          <Key>
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="ProviderKey" />
          </Key>
          <Property Name="LoginProvider" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ProviderKey" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="ProviderDisplayName" Type="nvarchar(max)" />
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
        </EntityType>
        <EntityType Name="UserRoles1">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="RoleId" />
          </Key>
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="RoleId" Type="nvarchar" MaxLength="450" Nullable="false" />
        </EntityType>
        <EntityType Name="UserTokens1">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="UserId" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="LoginProvider" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="450" Nullable="false" />
          <Property Name="Value" Type="nvarchar(max)" />
        </EntityType>
        <Association Name="FK_RefreshToken_User_ApplicationUserId">
          <End Role="User" Type="Self.User" Multiplicity="0..1" />
          <End Role="RefreshToken" Type="Self.RefreshToken" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RefreshToken">
              <PropertyRef Name="ApplicationUserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Dropoff_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Pickup_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD">
          <End Role="RLT_PAYMENT_METHOD" Type="Self.RLT_PAYMENT_METHOD" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_PAYMENT_METHOD">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Payment_Mode_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES">
          <End Role="RLT_TRIP_TYPES" Type="Self.RLT_TRIP_TYPES" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_TRIP_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Trip_Type_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS">
          <End Role="RLT_USER_LOGIN_DETAILS" Type="Self.RLT_USER_LOGIN_DETAILS" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_USER_LOGIN_DETAILS">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Booking_Login_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_CAR_CHARGES_FECILITIES_DETAILS" Type="Self.RLT_CAR_CHARGES_FECILITIES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_CHARGES_FECILITIES_DETAILS">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_COMPANY">
          <End Role="RLT_CAR_COMPANY" Type="Self.RLT_CAR_COMPANY" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_COMPANY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Company_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES">
          <End Role="RLT_CAR_FUEL_TYPES" Type="Self.RLT_CAR_FUEL_TYPES" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_FUEL_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Fuel_Type_Status" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES">
          <End Role="RLT_BANK_NAMES" Type="Self.RLT_BANK_NAMES" Multiplicity="0..1" />
          <End Role="RLT_CAR_OWNER_BANK_DETAILS" Type="Self.RLT_CAR_OWNER_BANK_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_BANK_NAMES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_OWNER_BANK_DETAILS">
              <PropertyRef Name="Bank_Name_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CITY_RLT_STATE">
          <End Role="RLT_STATE" Type="Self.RLT_STATE" Multiplicity="0..1" />
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_STATE">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CITY">
              <PropertyRef Name="State_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_LOCATION_CODE_RLT_CITY">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_LOCATION_CODE" Type="Self.RLT_LOCATION_CODE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_LOCATION_CODE">
              <PropertyRef Name="City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Dropoff_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Pickup_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES">
          <End Role="RLT_TRIP_TYPES" Type="Self.RLT_TRIP_TYPES" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_TRIP_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Car_Trip_Type_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_STATE_RLT_COUNTRY">
          <End Role="RLT_COUNTRY" Type="Self.RLT_COUNTRY" Multiplicity="0..1" />
          <End Role="RLT_STATE" Type="Self.RLT_STATE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_COUNTRY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_STATE">
              <PropertyRef Name="Counrty_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Segment_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY">
          <End Role="RLT_CAR_COMPANY" Type="Self.RLT_CAR_COMPANY" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_COMPANY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Company_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES">
          <End Role="RLT_CAR_FUEL_TYPES" Type="Self.RLT_CAR_FUEL_TYPES" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_FUEL_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Fuel_Type_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details">
          <End Role="RLT_Vendor_Details" Type="Self.RLT_Vendor_Details" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_Vendor_Details">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Vendor_Ref_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME">
          <End Role="RLT_DOCUMNETS_NAME" Type="Self.RLT_DOCUMNETS_NAME" Multiplicity="1" />
          <End Role="RLT_Vendor_Docs" Type="Self.RLT_Vendor_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_DOCUMNETS_NAME">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_Vendor_Docs">
              <PropertyRef Name="Vendor_Doc_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_Vendor_Docs_RLT_Vendor_Details">
          <End Role="RLT_Vendor_Details" Type="Self.RLT_Vendor_Details" Multiplicity="1" />
          <End Role="RLT_Vendor_Docs" Type="Self.RLT_Vendor_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_Vendor_Details">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_Vendor_Docs">
              <PropertyRef Name="Vendor_Ref_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RoleClaims_Role_RoleId">
          <End Role="Roles" Type="Self.Roles" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="RoleClaims" Type="Self.RoleClaims" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Roles">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RoleClaims">
              <PropertyRef Name="RoleId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RoleClaims_Role_RoleId1">
          <End Role="Role" Type="Self.Role" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="RoleClaims1" Type="Self.RoleClaims1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RoleClaims1">
              <PropertyRef Name="RoleId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserClaims_User_UserId">
          <End Role="Users" Type="Self.Users" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserClaims" Type="Self.UserClaims" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserClaims">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserClaims_User_UserId1">
          <End Role="User" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserClaims1" Type="Self.UserClaims1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserClaims1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserLogins_User_UserId">
          <End Role="Users" Type="Self.Users" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserLogins" Type="Self.UserLogins" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserLogins">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserLogins_User_UserId1">
          <End Role="User" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserLogins1" Type="Self.UserLogins1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserLogins1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_Role">
          <End Role="Roles" Type="Self.Roles" Multiplicity="1" />
          <End Role="UserRoles" Type="Self.UserRoles" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Roles">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles">
              <PropertyRef Name="Roles_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_Role_RoleId">
          <End Role="Role" Type="Self.Role" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserRoles1" Type="Self.UserRoles1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles1">
              <PropertyRef Name="RoleId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_Roles">
          <End Role="Roles" Type="Self.Roles" Multiplicity="1" />
          <End Role="UserRoles" Type="Self.UserRoles" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Roles">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles">
              <PropertyRef Name="Roles_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_User">
          <End Role="Users" Type="Self.Users" Multiplicity="1" />
          <End Role="UserRoles" Type="Self.UserRoles" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles">
              <PropertyRef Name="Users_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_User_UserId">
          <End Role="User" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserRoles1" Type="Self.UserRoles1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserRoles_Users">
          <End Role="Users" Type="Self.Users" Multiplicity="1" />
          <End Role="UserRoles" Type="Self.UserRoles" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserRoles">
              <PropertyRef Name="Users_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserTokens_User_UserId">
          <End Role="Users" Type="Self.Users" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserTokens" Type="Self.UserTokens" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserTokens">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserTokens_User_UserId1">
          <End Role="User" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserTokens1" Type="Self.UserTokens1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserTokens1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME">
          <End Role="RLT_DOCUMNETS_NAME" Type="Self.RLT_DOCUMNETS_NAME" Multiplicity="1" />
          <End Role="Vendor_Car_Driver_Docs" Type="Self.Vendor_Car_Driver_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_DOCUMNETS_NAME">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="Vendor_Car_Driver_Docs">
              <PropertyRef Name="Doc_Name" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS">
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="1" />
          <End Role="Vendor_Car_Driver_Docs" Type="Self.Vendor_Car_Driver_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="Vendor_Car_Driver_Docs">
              <PropertyRef Name="Vendor_Car_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Function Name="Proc_GetUserDtl" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserId" Type="varchar" Mode="In" />
        </Function>
        <Function Name="SP_GET_REPORT_DATA" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Flag" Type="int" Mode="In" />
          <Parameter Name="CityFrom" Type="int" Mode="In" />
          <Parameter Name="CityTo" Type="int" Mode="In" />
          <Parameter Name="FromDate" Type="datetime" Mode="In" />
          <Parameter Name="ToDate" Type="datetime" Mode="In" />
          <Parameter Name="BookingStatusId" Type="int" Mode="In" />
          <Parameter Name="BookingId" Type="nvarchar" Mode="In" />
          <Parameter Name="CustomerName" Type="nvarchar" Mode="In" />
          <Parameter Name="CustomerMobile" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Booking_Create" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Booking_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUpCity" Type="nvarchar" Mode="In" />
          <Parameter Name="DropOffCity" Type="nvarchar" Mode="In" />
          <Parameter Name="TripType" Type="nvarchar" Mode="In" />
          <Parameter Name="CarCategory" Type="nvarchar" Mode="In" />
          <Parameter Name="Duration" Type="nvarchar" Mode="In" />
          <Parameter Name="Distance" Type="decimal" Mode="In" />
          <Parameter Name="BasicFare" Type="decimal" Mode="In" />
          <Parameter Name="DriverCharge" Type="decimal" Mode="In" />
          <Parameter Name="GST" Type="decimal" Mode="In" />
          <Parameter Name="Fare" Type="decimal" Mode="In" />
          <Parameter Name="GSTFare" Type="decimal" Mode="In" />
          <Parameter Name="CouponCode" Type="nvarchar" Mode="In" />
          <Parameter Name="CouponDiscount" Type="decimal" Mode="In" />
          <Parameter Name="PickUpAddress" Type="nvarchar" Mode="In" />
          <Parameter Name="DropOffAddress" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUpDate" Type="date" Mode="In" />
          <Parameter Name="PickUpTime" Type="nvarchar" Mode="In" />
          <Parameter Name="TravelerName" Type="nvarchar" Mode="In" />
          <Parameter Name="PhoneNumber" Type="nvarchar" Mode="In" />
          <Parameter Name="MailId" Type="nvarchar" Mode="In" />
          <Parameter Name="PaymentMode" Type="int" Mode="In" />
          <Parameter Name="BookingCreatedBy" Type="nvarchar" Mode="In" />
          <Parameter Name="RazorpayPaymentID" Type="nvarchar" Mode="In" />
          <Parameter Name="RazorpayOrderID" Type="nvarchar" Mode="In" />
          <Parameter Name="RazorpaySignature" Type="nvarchar" Mode="In" />
          <Parameter Name="RazorpayStatus" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUpAddressLongLat" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUpAddressLongitude" Type="nvarchar" Mode="In" />
          <Parameter Name="CashAmountToPayDriver" Type="decimal" Mode="In" />
          <Parameter Name="PaymentOption" Type="int" Mode="In" />
          <Parameter Name="TollCharge" Type="decimal" Mode="In" />
          <Parameter Name="result" Type="nvarchar" Mode="InOut" />
        </Function>
        <Function Name="usp_Booking_Execute" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Booking_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="City_From_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="City_To_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUp_Address" Type="nvarchar" Mode="In" />
          <Parameter Name="DropOff_Address" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUpLatLong" Type="nvarchar" Mode="In" />
          <Parameter Name="DropOffLatLong" Type="nvarchar" Mode="In" />
          <Parameter Name="TravelerName" Type="nvarchar" Mode="In" />
          <Parameter Name="TravelerEmail" Type="nvarchar" Mode="In" />
          <Parameter Name="TravelerPhone" Type="nvarchar" Mode="In" />
          <Parameter Name="BookingSessionID" Type="nvarchar" Mode="In" />
          <Parameter Name="PaymentOption" Type="nvarchar" Mode="In" />
          <Parameter Name="PaymentMode" Type="nvarchar" Mode="In" />
          <Parameter Name="IsWhatsAppNumber" Type="bit" Mode="In" />
          <Parameter Name="CashToPayDriver" Type="nvarchar" Mode="In" />
          <Parameter Name="DriverNightCharge" Type="nvarchar" Mode="In" />
          <Parameter Name="Trip_Type_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="Car_Category_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="Distance" Type="nvarchar" Mode="In" />
          <Parameter Name="Basic_Fare" Type="nvarchar" Mode="In" />
          <Parameter Name="Toll_Charge" Type="nvarchar" Mode="In" />
          <Parameter Name="GST" Type="nvarchar" Mode="In" />
          <Parameter Name="Fare" Type="nvarchar" Mode="In" />
          <Parameter Name="GST_Fare" Type="nvarchar" Mode="In" />
          <Parameter Name="Coupon_Code" Type="nvarchar" Mode="In" />
          <Parameter Name="Coupon_Discount" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUp_Date" Type="nvarchar" Mode="In" />
          <Parameter Name="PickUp_Time" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Contact_Enquiry_Execute" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Name" Type="nvarchar" Mode="In" />
          <Parameter Name="PhoneNumber" Type="nvarchar" Mode="In" />
          <Parameter Name="EmailID" Type="nvarchar" Mode="In" />
          <Parameter Name="IsWhatsAppNumbe" Type="bit" Mode="In" />
          <Parameter Name="Message" Type="nvarchar" Mode="In" />
          <Parameter Name="IsSucessful" Type="int" Mode="InOut" />
        </Function>
        <Function Name="usp_Driver_Enquiry_Execute" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="FirstName" Type="nvarchar" Mode="In" />
          <Parameter Name="LastName" Type="nvarchar" Mode="In" />
          <Parameter Name="PhoneNumber" Type="nvarchar" Mode="In" />
          <Parameter Name="EmailID" Type="nvarchar" Mode="In" />
          <Parameter Name="IsWhatsAppNumbe" Type="bit" Mode="In" />
          <Parameter Name="Address" Type="nvarchar" Mode="In" />
          <Parameter Name="IsSucessful" Type="int" Mode="InOut" />
        </Function>
        <Function Name="usp_Get_About_Us" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Car_Category_Details" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Cities" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Comapny_Info" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Discount_Coupon_Details" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="CouponCode" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Get_Driver_Booking_info" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vendor_Id" Type="nvarchar" Mode="In" />
          <Parameter Name="statusid" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Get_Driver_Login_and_info" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="emailid" Type="nvarchar" Mode="In" />
          <Parameter Name="mobileno" Type="nvarchar" Mode="In" />
          <Parameter Name="password" Type="nvarchar" Mode="In" />
          <Parameter Name="flag" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Get_FAQ" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Privacy_Policy" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Services" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="usp_Get_Template_Master" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="TemplateName" Type="nvarchar" Mode="In" />
          <Parameter Name="TemplateType" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Get_User_Details" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserID" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="usp_Get_UserLogin_Details" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserEmailId" Type="nvarchar" Mode="In" />
          <Parameter Name="UserMobileNumber" Type="nvarchar" Mode="In" />
          <Parameter Name="UserPassword" Type="nvarchar" Mode="In" />
          <Parameter Name="IsOTPLogin" Type="bit" Mode="In" />
          <Parameter Name="ID" Type="int" Mode="In" />
        </Function>
        <Function Name="usp_update_otp" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserType" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="OTP" Type="int" Mode="In" />
          <Parameter Name="IsSucessful" Type="bit" Mode="InOut" />
        </Function>
        <Function Name="usp_UserLogin_Execute" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserEmailId" Type="nvarchar" Mode="In" />
          <Parameter Name="UserMobileNumber" Type="nvarchar" Mode="In" />
          <Parameter Name="UserPassword" Type="nvarchar" Mode="In" />
          <Parameter Name="IsOTPLogin" Type="bit" Mode="In" />
          <Parameter Name="IsSucessful" Type="int" Mode="InOut" />
        </Function>
        <EntityContainer Name="RLTCARMODELStoreContainer">
          <EntitySet Name="__EFMigrationsHistory" EntityType="Self.__EFMigrationsHistory" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="C__EFMigrationsHistory" EntityType="Self.C__EFMigrationsHistory" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Products" EntityType="Self.Products" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_AboutUs" EntityType="Self.RLT_AboutUs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ADMIN_ROLE" EntityType="Self.RLT_ADMIN_ROLE" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ADMIN_USER" EntityType="Self.RLT_ADMIN_USER" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Admin_User_Info" EntityType="Self.RLT_Admin_User_Info" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BANK_NAMES" EntityType="Self.RLT_BANK_NAMES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING" EntityType="Self.RLT_BOOKING" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_FARE" EntityType="Self.RLT_BOOKING_FARE" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_GST" EntityType="Self.RLT_BOOKING_GST" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_MODE" EntityType="Self.RLT_BOOKING_MODE" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_PAYMENT_DETAILS" EntityType="Self.RLT_BOOKING_PAYMENT_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_RULES" EntityType="Self.RLT_BOOKING_RULES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKING_STATUS" EntityType="Self.RLT_BOOKING_STATUS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_BOOKINGS" EntityType="Self.RLT_BOOKINGS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_CATEGORY" EntityType="Self.RLT_CAR_CATEGORY" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_CHARGES_FECILITIES_DETAILS" EntityType="Self.RLT_CAR_CHARGES_FECILITIES_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_COMPANY" EntityType="Self.RLT_CAR_COMPANY" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_DETAILS" EntityType="Self.RLT_CAR_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_DOCS" EntityType="Self.RLT_CAR_DOCS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_DRIVER_DETAILS" EntityType="Self.RLT_CAR_DRIVER_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_DRIVER_DOCS" EntityType="Self.RLT_CAR_DRIVER_DOCS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_FUEL_TYPES" EntityType="Self.RLT_CAR_FUEL_TYPES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_MODEL" EntityType="Self.RLT_CAR_MODEL" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_OWNER_BANK_DETAILS" EntityType="Self.RLT_CAR_OWNER_BANK_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_OWNER_DETAILS" EntityType="Self.RLT_CAR_OWNER_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CAR_SEGMENT" EntityType="Self.RLT_CAR_SEGMENT" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CITY" EntityType="Self.RLT_CITY" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CLASS_MASTER" EntityType="Self.RLT_CLASS_MASTER" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Company_Details" EntityType="Self.RLT_Company_Details" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_CONTACT_ENQUIRIES" EntityType="Self.RLT_CONTACT_ENQUIRIES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ContactInformation" EntityType="Self.RLT_ContactInformation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_COUNTRY" EntityType="Self.RLT_COUNTRY" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Department_Name" EntityType="Self.RLT_Department_Name" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_DISCOUNT_COUPON" EntityType="Self.RLT_DISCOUNT_COUPON" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_DOCUMNETS_NAME" EntityType="Self.RLT_DOCUMNETS_NAME" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_DRIVER_APPROVE_STATUS" EntityType="Self.RLT_DRIVER_APPROVE_STATUS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_DRIVER_ENQUIRIES" EntityType="Self.RLT_DRIVER_ENQUIRIES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ENTITY_GENERALS" EntityType="Self.RLT_ENTITY_GENERALS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_FAQ_Details" EntityType="Self.RLT_FAQ_Details" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_LOCATION_CODE" EntityType="Self.RLT_LOCATION_CODE" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_LOG" EntityType="Self.RLT_LOG" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_MENU_MASTER" EntityType="Self.RLT_MENU_MASTER" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_NextId" EntityType="Self.RLT_NextId" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_PAYMENT_METHOD" EntityType="Self.RLT_PAYMENT_METHOD" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_PrivacyPolicy" EntityType="Self.RLT_PrivacyPolicy" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ROLE_MENU_MAPPING" EntityType="Self.RLT_ROLE_MENU_MAPPING" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ROUTE_PLAN" EntityType="Self.RLT_ROUTE_PLAN" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_ROUTES_DETAILS" EntityType="Self.RLT_ROUTES_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Services" EntityType="Self.RLT_Services" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_STATE" EntityType="Self.RLT_STATE" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_SUBSCRIBERS" EntityType="Self.RLT_SUBSCRIBERS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Template_Mail_Message" EntityType="Self.RLT_Template_Mail_Message" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Template_Master" EntityType="Self.RLT_Template_Master" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_TermsConditions" EntityType="Self.RLT_TermsConditions" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_TRIP_TYPES" EntityType="Self.RLT_TRIP_TYPES" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_USER_LOGIN_DETAILS" EntityType="Self.RLT_USER_LOGIN_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_UserFeedBack" EntityType="Self.RLT_UserFeedBack" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Vendor_Bank_Details" EntityType="Self.RLT_Vendor_Bank_Details" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_VENDOR_CAR_DETAILS" EntityType="Self.RLT_VENDOR_CAR_DETAILS" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Vendor_Details" EntityType="Self.RLT_Vendor_Details" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLT_Vendor_Docs" EntityType="Self.RLT_Vendor_Docs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLTBookingAuditTrails" EntityType="Self.RLTBookingAuditTrails" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLTConfigurationAuditTrails" EntityType="Self.RLTConfigurationAuditTrails" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLTMostFavoriteRoutes" EntityType="Self.RLTMostFavoriteRoutes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLTResources" EntityType="Self.RLTResources" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RLTResourceStaus" EntityType="Self.RLTResourceStaus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RoleClaims" EntityType="Self.RoleClaims" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Roles" EntityType="Self.Roles" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TempBookings" EntityType="Self.TempBookings" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserClaims" EntityType="Self.UserClaims" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserDiscountAvaileds" EntityType="Self.UserDiscountAvaileds" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserLogins" EntityType="Self.UserLogins" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserRoles" EntityType="Self.UserRoles" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Users" EntityType="Self.Users" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserTokens" EntityType="Self.UserTokens" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Vendor_Car_Driver_Docs" EntityType="Self.Vendor_Car_Driver_Docs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RefreshToken" EntityType="Self.RefreshToken" Schema="Identity" store:Type="Tables" />
          <EntitySet Name="Role" EntityType="Self.Role" Schema="Identity" store:Type="Tables" />
          <EntitySet Name="RoleClaims1" EntityType="Self.RoleClaims1" Schema="Identity" Table="RoleClaims" store:Type="Tables" />
          <EntitySet Name="User" EntityType="Self.User" Schema="Identity" store:Type="Tables" />
          <EntitySet Name="UserClaims1" EntityType="Self.UserClaims1" Schema="Identity" Table="UserClaims" store:Type="Tables" />
          <EntitySet Name="UserLogins1" EntityType="Self.UserLogins1" Schema="Identity" Table="UserLogins" store:Type="Tables" />
          <EntitySet Name="UserRoles1" EntityType="Self.UserRoles1" Schema="Identity" Table="UserRoles" store:Type="Tables" />
          <EntitySet Name="UserTokens1" EntityType="Self.UserTokens1" Schema="Identity" Table="UserTokens" store:Type="Tables" />
          <AssociationSet Name="FK_RefreshToken_User_ApplicationUserId" Association="Self.FK_RefreshToken_User_ApplicationUserId">
            <End Role="User" EntitySet="User" />
            <End Role="RefreshToken" EntitySet="RefreshToken" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD">
            <End Role="RLT_PAYMENT_METHOD" EntitySet="RLT_PAYMENT_METHOD" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES">
            <End Role="RLT_TRIP_TYPES" EntitySet="RLT_TRIP_TYPES" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS">
            <End Role="RLT_USER_LOGIN_DETAILS" EntitySet="RLT_USER_LOGIN_DETAILS" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_CAR_CHARGES_FECILITIES_DETAILS" EntitySet="RLT_CAR_CHARGES_FECILITIES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_COMPANY" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_COMPANY">
            <End Role="RLT_CAR_COMPANY" EntitySet="RLT_CAR_COMPANY" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES">
            <End Role="RLT_CAR_FUEL_TYPES" EntitySet="RLT_CAR_FUEL_TYPES" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES" Association="Self.FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES">
            <End Role="RLT_BANK_NAMES" EntitySet="RLT_BANK_NAMES" />
            <End Role="RLT_CAR_OWNER_BANK_DETAILS" EntitySet="RLT_CAR_OWNER_BANK_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CITY_RLT_STATE" Association="Self.FK_RLT_CITY_RLT_STATE">
            <End Role="RLT_STATE" EntitySet="RLT_STATE" />
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_LOCATION_CODE_RLT_CITY" Association="Self.FK_RLT_LOCATION_CODE_RLT_CITY">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_LOCATION_CODE" EntitySet="RLT_LOCATION_CODE" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES">
            <End Role="RLT_TRIP_TYPES" EntitySet="RLT_TRIP_TYPES" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_STATE_RLT_COUNTRY" Association="Self.FK_RLT_STATE_RLT_COUNTRY">
            <End Role="RLT_COUNTRY" EntitySet="RLT_COUNTRY" />
            <End Role="RLT_STATE" EntitySet="RLT_STATE" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY">
            <End Role="RLT_CAR_COMPANY" EntitySet="RLT_CAR_COMPANY" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES">
            <End Role="RLT_CAR_FUEL_TYPES" EntitySet="RLT_CAR_FUEL_TYPES" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details">
            <End Role="RLT_Vendor_Details" EntitySet="RLT_Vendor_Details" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME" Association="Self.FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME">
            <End Role="RLT_DOCUMNETS_NAME" EntitySet="RLT_DOCUMNETS_NAME" />
            <End Role="RLT_Vendor_Docs" EntitySet="RLT_Vendor_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_Vendor_Docs_RLT_Vendor_Details" Association="Self.FK_RLT_Vendor_Docs_RLT_Vendor_Details">
            <End Role="RLT_Vendor_Details" EntitySet="RLT_Vendor_Details" />
            <End Role="RLT_Vendor_Docs" EntitySet="RLT_Vendor_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_RoleClaims_Role_RoleId" Association="Self.FK_RoleClaims_Role_RoleId">
            <End Role="Roles" EntitySet="Roles" />
            <End Role="RoleClaims" EntitySet="RoleClaims" />
          </AssociationSet>
          <AssociationSet Name="FK_RoleClaims_Role_RoleId1" Association="Self.FK_RoleClaims_Role_RoleId1">
            <End Role="Role" EntitySet="Role" />
            <End Role="RoleClaims1" EntitySet="RoleClaims1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserClaims_User_UserId" Association="Self.FK_UserClaims_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserClaims" EntitySet="UserClaims" />
          </AssociationSet>
          <AssociationSet Name="FK_UserClaims_User_UserId1" Association="Self.FK_UserClaims_User_UserId1">
            <End Role="User" EntitySet="User" />
            <End Role="UserClaims1" EntitySet="UserClaims1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserLogins_User_UserId" Association="Self.FK_UserLogins_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserLogins" EntitySet="UserLogins" />
          </AssociationSet>
          <AssociationSet Name="FK_UserLogins_User_UserId1" Association="Self.FK_UserLogins_User_UserId1">
            <End Role="User" EntitySet="User" />
            <End Role="UserLogins1" EntitySet="UserLogins1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_Role" Association="Self.FK_UserRoles_Role">
            <End Role="Roles" EntitySet="Roles" />
            <End Role="UserRoles" EntitySet="UserRoles" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_Role_RoleId" Association="Self.FK_UserRoles_Role_RoleId">
            <End Role="Role" EntitySet="Role" />
            <End Role="UserRoles1" EntitySet="UserRoles1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_Roles" Association="Self.FK_UserRoles_Roles">
            <End Role="Roles" EntitySet="Roles" />
            <End Role="UserRoles" EntitySet="UserRoles" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_User" Association="Self.FK_UserRoles_User">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserRoles" EntitySet="UserRoles" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_User_UserId" Association="Self.FK_UserRoles_User_UserId">
            <End Role="User" EntitySet="User" />
            <End Role="UserRoles1" EntitySet="UserRoles1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserRoles_Users" Association="Self.FK_UserRoles_Users">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserRoles" EntitySet="UserRoles" />
          </AssociationSet>
          <AssociationSet Name="FK_UserTokens_User_UserId" Association="Self.FK_UserTokens_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserTokens" EntitySet="UserTokens" />
          </AssociationSet>
          <AssociationSet Name="FK_UserTokens_User_UserId1" Association="Self.FK_UserTokens_User_UserId1">
            <End Role="User" EntitySet="User" />
            <End Role="UserTokens1" EntitySet="UserTokens1" />
          </AssociationSet>
          <AssociationSet Name="FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME" Association="Self.FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME">
            <End Role="RLT_DOCUMNETS_NAME" EntitySet="RLT_DOCUMNETS_NAME" />
            <End Role="Vendor_Car_Driver_Docs" EntitySet="Vendor_Car_Driver_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS" Association="Self.FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS">
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
            <End Role="Vendor_Car_Driver_Docs" EntitySet="Vendor_Car_Driver_Docs" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RLTCARMODEL" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="C__EFMigrationsHistory">
          <Key>
            <PropertyRef Name="MigrationId" />
          </Key>
          <Property Name="MigrationId" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProductVersion" Type="String" MaxLength="32" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="C__EFMigrationsHistory1">
          <Key>
            <PropertyRef Name="MigrationId" />
          </Key>
          <Property Name="MigrationId" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProductVersion" Type="String" MaxLength="32" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Product">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CreatedBy" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Created" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="LastModifiedBy" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="LastModified" Type="DateTime" Precision="3" />
          <Property Name="Name" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Barcode" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Rate" Type="Decimal" Precision="18" Scale="6" Nullable="false" />
        </EntityType>
        <EntityType Name="RLT_AboutUs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="AboutUsDetails" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ADMIN_ROLE">
          <Key>
            <PropertyRef Name="RoleId" />
          </Key>
          <Property Name="RoleId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Role" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Role_Description" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ADMIN_USER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RoleId" Type="Int32" />
          <Property Name="UserName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserPWD" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserPhoto" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="UserFirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserMiddleName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="UserLastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserEmailID" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="UserMobileNo" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="VendorId" Type="Int32" />
          <Property Name="CarOwnerId" Type="Int32" />
          <Property Name="EmployeeId" Type="Int32" />
          <Property Name="AadharId" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="DepartmentId" Type="Int32" />
          <Property Name="Address" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="IsAdmin" Type="Int32" />
          <Property Name="IsDeleted" Type="Boolean" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Is2TfaAuthentication" Type="Boolean" />
          <Property Name="ID" Type="Guid" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedBy" Type="Int32" />
          <Property Name="CreatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_Admin_User_Info">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Employee_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Photo" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="DOB" Type="DateTime" Precision="3" />
          <Property Name="Phone_1" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Phone_2" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Phone_1_IsWhatsup" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Marrital_Status" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Spouses_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Father_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mother_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Current_Permanent_Same" Type="Boolean" />
          <Property Name="Current_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Permanent_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Permanent_Address1" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Permanent_Address2" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Permanent_Address_State" Type="Int32" />
          <Property Name="Permanent_Address_City" Type="Int32" />
          <Property Name="Current_Address1" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Current_Address2" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Current_Address_State" Type="Int32" />
          <Property Name="Current_Address_City" Type="Int32" />
          <Property Name="C10th_School" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="C10th_PassingYear" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="C10th_Percentage" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="C12th_School" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="C12th_PassingYear" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="C12th_Percentage" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Degree_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Degree_College" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Degree_PassingYear" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Degree_Percentage" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Master_Degree_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Master_Degree_College" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Master_Degree_PassingYear" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Master_Degree_Percentage" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Employee_ID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Department" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Designation" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Supervisor" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Work_City" Type="Int32" />
          <Property Name="Work_Phone" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Work_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Work_StartDate" Type="DateTime" Precision="3" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
          <Property Name="UserId" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_BANK_NAMES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Bank_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_OWNER_BANK_DETAILS" Relationship="Self.FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES" FromRole="RLT_BANK_NAMES" ToRole="RLT_CAR_OWNER_BANK_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_BOOKING">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Booking_Id" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="City_From_Id" Type="Int32" />
          <Property Name="City_To_Id" Type="Int32" />
          <Property Name="Trip_Type_Id" Type="Int32" />
          <Property Name="Car_Category_Id" Type="Int32" />
          <Property Name="Duration" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Distance" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Basic_Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Driver_Charge" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Toll_Charge" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="GST" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="GST_Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Coupon_Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Coupon_Discount" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Booking_Date" Type="DateTime" Precision="3" />
          <Property Name="PickUp_Address" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="DropOff_Address" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="PickUp_Date" Type="DateTime" Precision="3" />
          <Property Name="PickUp_Time" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mobile_No1" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mobile_No2" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mail_Id" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mode_Of_Payment_Id" Type="Int32" />
          <Property Name="Vendor_Id" Type="Int32" />
          <Property Name="Car_Id" Type="Int32" />
          <Property Name="Booking_Status_Id" Type="Int32" />
          <Property Name="Booking_Remark" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Invoice_No" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Invoice_Date" Type="DateTime" Precision="3" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Updated_By" Type="Int32" />
          <Property Name="razorpay_payment_id" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_order_id" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_signature" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_status" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="payment_link" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="PickUpAddressLatitude" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="PickUpAddressLongitude" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="BookingEditRemark" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="driver_id" Type="Int32" />
          <Property Name="completepickupaddress" Type="String" MaxLength="400" FixedLength="false" Unicode="true" />
          <Property Name="completedropoffpaddress" Type="String" MaxLength="400" FixedLength="false" Unicode="true" />
          <Property Name="IsFullOnlinePayment" Type="Boolean" />
          <Property Name="CashAmountToPayDriver" Type="Decimal" Precision="18" Scale="0" />
          <Property Name="TollCharge" Type="Decimal" Precision="18" Scale="0" />
          <Property Name="PaymentOption" Type="Int32" />
          <Property Name="DropOffDate" Type="DateTime" Precision="3" />
          <Property Name="DropOffTime" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="PaymentDate" Type="DateTime" Precision="3" />
          <Property Name="BookedBy" Type="String" MaxLength="900" FixedLength="false" Unicode="true" />
          <Property Name="IsAdminBooked" Type="Boolean" />
          <Property Name="PaymentType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="PartialPaymentAmount" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="RemainingAmountForDriver" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Booking_Created_By" Type="String" MaxLength="450" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_FARE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="City_From" Type="Int32" />
          <Property Name="City_To" Type="Int32" />
          <Property Name="Trip_Type_Id" Type="Int32" />
          <Property Name="Car_Category_Id" Type="Int32" />
          <Property Name="Basic_Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Driver_Charge" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Toll_Charge" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Total_Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="GST" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="GST_Amount" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Final_Fare" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Remark" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Updated_By" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_GST">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GST" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="SGST" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="CGST" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Updated_By" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_MODE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Booking_Mode_Name" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Counrty_Abbr" Type="String" MaxLength="5" FixedLength="false" Unicode="false" />
          <Property Name="Is_Active" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_PAYMENT_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Passenger_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Phone" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Alt_Phone" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Special_Request_Comment" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Trip_Purpose" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Trip_Type_PKID" Type="Int32" />
          <Property Name="Passenger_Pickup_City_PKID" Type="Int32" />
          <Property Name="Passenger_Pickup_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Passenger_Dropoff_City_PKID" Type="Int32" />
          <Property Name="Passenger_Dropoff_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Booking_Login_PKID" Type="Int64" />
          <Property Name="Base_Fare" Type="Double" />
          <Property Name="Driver_Allowance" Type="Double" />
          <Property Name="Service_Tax" Type="Double" />
          <Property Name="Discount" Type="Double" />
          <Property Name="Discount_Coupon" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Total_Booking_Amount" Type="Double" />
          <Property Name="Payment_Mode_PKID" Type="Int32" />
          <Property Name="Payment_Amount_Recieved" Type="Double" />
          <Property Name="Payment_Need_to_Collect" Type="Double" />
          <Property Name="Payment_Issue_Comment" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Payment_TransactionId" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Payment_Transaction_Status" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CITY" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff" FromRole="RLT_BOOKING_PAYMENT_DETAILS" ToRole="RLT_CITY" />
          <NavigationProperty Name="RLT_CITY1" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP" FromRole="RLT_BOOKING_PAYMENT_DETAILS" ToRole="RLT_CITY" />
          <NavigationProperty Name="RLT_PAYMENT_METHOD" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD" FromRole="RLT_BOOKING_PAYMENT_DETAILS" ToRole="RLT_PAYMENT_METHOD" />
          <NavigationProperty Name="RLT_TRIP_TYPES" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES" FromRole="RLT_BOOKING_PAYMENT_DETAILS" ToRole="RLT_TRIP_TYPES" />
          <NavigationProperty Name="RLT_USER_LOGIN_DETAILS" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS" FromRole="RLT_BOOKING_PAYMENT_DETAILS" ToRole="RLT_USER_LOGIN_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_RULES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Trip_Type_Id" Type="Int32" />
          <Property Name="Car_Category_Id" Type="Int32" />
          <Property Name="Distance_From" Type="Int32" />
          <Property Name="Distance_To" Type="Int32" />
          <Property Name="NewDistance" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Updated_By" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_BOOKING_STATUS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="BOOKING_STATUS" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Modified_By" Type="Int32" />
          <Property Name="Modified_Date" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="RLT_BOOKINGS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Booking_Id" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="City_From_Id" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="City_To_Id" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="PickUp_Address" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="DropOff_Address" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="PickUpLatLong" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="DropOffLatLong" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="TravelerName" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="TravelerEmail" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="TravelerPhone" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="BookingSessionID" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="PaymentOption" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="PaymentMode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="IsWhatsAppNumber" Type="Boolean" />
          <Property Name="CashToPayDriver" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="DriverNightCharge" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Trip_Type_Id" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Car_Category_Id" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Duration" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Distance" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Basic_Fare" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Charge" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Toll_Charge" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="GST" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Fare" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="GST_Fare" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Coupon_Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Coupon_Discount" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Booking_Date" Type="DateTime" Precision="3" />
          <Property Name="PickUp_Date" Type="DateTime" Precision="3" />
          <Property Name="PickUp_Time" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Id" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Car_Id" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Booking_Status_Id" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Booking_Remark" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Invoice_No" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Invoice_Date" Type="DateTime" Precision="3" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Updated_By" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_payment_id" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_order_id" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_signature" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="razorpay_status" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLT_CAR_CATEGORY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Car_Category_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Car_Category_Abbr" Type="String" MaxLength="60" FixedLength="false" Unicode="true" />
          <Property Name="Per_KM_fare" Type="Double" />
          <Property Name="Capacity" Type="Int32" />
          <Property Name="Features" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Car_Categroy_Image" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_CHARGES_FECILITIES_DETAILS" Relationship="Self.FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_CAR_CATEGORY" ToRole="RLT_CAR_CHARGES_FECILITIES_DETAILS" />
          <NavigationProperty Name="RLT_CAR_MODEL" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY" FromRole="RLT_CAR_CATEGORY" ToRole="RLT_CAR_MODEL" />
          <NavigationProperty Name="RLT_ROUTES_DETAILS" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_CAR_CATEGORY" ToRole="RLT_ROUTES_DETAILS" />
          <NavigationProperty Name="RLT_VENDOR_CAR_DETAILS" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_CAR_CATEGORY" ToRole="RLT_VENDOR_CAR_DETAILS" />
          <Property Name="Base_Fare" Type="Decimal" Precision="10" Scale="2" />
        </EntityType>
        <EntityType Name="RLT_CAR_CHARGES_FECILITIES_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Car_Category_PKID" Type="Int32" />
          <Property Name="Car_Charges_PKM" Type="Double" />
          <Property Name="Car_Waiting_Charges_PM" Type="Double" />
          <Property Name="Car_Driver_Charges_PD" Type="Double" />
          <Property Name="Is_Contain_AC" Type="Boolean" />
          <Property Name="Total_Number_Seats" Type="String" MaxLength="10" FixedLength="true" Unicode="true" />
          <Property Name="Is_Smoking_Allow" Type="Boolean" />
          <Property Name="Is_Luggage_Allow" Type="Boolean" />
          <Property Name="Is_Pet_Allow" Type="Boolean" />
          <Property Name="Inclusive_Charges_Comment" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Extras_Charges_Comment" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_CATEGORY" Relationship="Self.FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_CAR_CHARGES_FECILITIES_DETAILS" ToRole="RLT_CAR_CATEGORY" />
        </EntityType>
        <EntityType Name="RLT_CAR_COMPANY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Company_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_MODEL" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_COMPANY" FromRole="RLT_CAR_COMPANY" ToRole="RLT_CAR_MODEL" />
          <NavigationProperty Name="RLT_VENDOR_CAR_DETAILS" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY" FromRole="RLT_CAR_COMPANY" ToRole="RLT_VENDOR_CAR_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_CAR_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Vendor_PKID" Type="Int32" />
          <Property Name="Car_Owner_PKID" Type="Int32" />
          <Property Name="Car_Number" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Car_Purchase_Year" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Car_Manufacturing_Year" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Car_Company_PKID" Type="Int32" />
          <Property Name="Car_Model_PKID" Type="Int32" />
          <Property Name="Car_Registered_Document" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="Car_Registered_City_PKID" Type="Int32" />
          <Property Name="Car_Fuel_Type_Status" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Update_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CAR_DOCS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Car_PKID" Type="Int32" Nullable="false" />
          <Property Name="Car_Doc_Id" Type="Int32" Nullable="false" />
          <Property Name="Car_Doc_EndDate" Type="DateTime" Precision="3" />
          <Property Name="Document_Path" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Is_Verified" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CAR_DRIVER_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Vendor_PKID" Type="Int32" />
          <Property Name="Car_Owner_PKID" Type="Int32" />
          <Property Name="Car_PKID" Type="Int64" />
          <Property Name="Driver_Name" Type="String" MaxLength="75" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Photo" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Driver_DOB" Type="DateTime" Precision="3" />
          <Property Name="Driver_FName" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Driver_MName" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Driver_LName" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Drive_Phone_1" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Drive_Phone_2" Type="String" MaxLength="10" FixedLength="true" Unicode="true" />
          <Property Name="Phone_1_IsWhatsup" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Email" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Driver_DL" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Aadhaar" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Father_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Mother_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Marrital_Status" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Gender" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Current_Permanent_Same" Type="Boolean" />
          <Property Name="Driver_Current_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Permanent_Address" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Permanent_Address1" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Permanent_Address2" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Permanent_Address_State" Type="Int32" />
          <Property Name="Driver_Permanent_Address_City" Type="Int32" />
          <Property Name="Driver_Current_Address1" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Current_Address2" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Current_Address_State" Type="Int32" />
          <Property Name="Driver_Current_Address_City" Type="Int32" />
          <Property Name="Driver_Smoking_Status" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Drinking_Status" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Eating_Type" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Religion" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
          <Property Name="Will_Owner_Drive_Car_Status" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="Driver_Pwd" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="OTPUpdatedDateTime" Type="DateTime" Precision="3" />
          <Property Name="OTPNumber" Type="Int32" />
          <Property Name="UserID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Special_Remarking" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLT_CAR_DRIVER_DOCS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CarDriver_PKID" Type="Int32" Nullable="false" />
          <Property Name="CarDriver_Doc_Id" Type="Int32" Nullable="false" />
          <Property Name="CarDriver_Doc_EndDate" Type="DateTime" Precision="3" />
          <Property Name="Document_Path" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Is_Verified" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CAR_FUEL_TYPES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="CAR_FUEL_TYPE" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_MODEL" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES" FromRole="RLT_CAR_FUEL_TYPES" ToRole="RLT_CAR_MODEL" />
          <NavigationProperty Name="RLT_VENDOR_CAR_DETAILS" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES" FromRole="RLT_CAR_FUEL_TYPES" ToRole="RLT_VENDOR_CAR_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_CAR_MODEL">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Car_Model_Name" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Car_Category_PKID" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Car_Fuel_Type_Status" Type="Int32" />
          <Property Name="Car_Company_PKID" Type="Int32" />
          <Property Name="Car_Segment_PKID" Type="Int32" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_CATEGORY" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY" FromRole="RLT_CAR_MODEL" ToRole="RLT_CAR_CATEGORY" />
          <NavigationProperty Name="RLT_CAR_COMPANY" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_COMPANY" FromRole="RLT_CAR_MODEL" ToRole="RLT_CAR_COMPANY" />
          <NavigationProperty Name="RLT_CAR_FUEL_TYPES" Relationship="Self.FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES" FromRole="RLT_CAR_MODEL" ToRole="RLT_CAR_FUEL_TYPES" />
        </EntityType>
        <EntityType Name="RLT_CAR_OWNER_BANK_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Car_Owner_PKID" Type="Int32" />
          <Property Name="Bank_Name_PKID" Type="Int32" />
          <Property Name="Bank_IFSC_Code" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Bank_Acc_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Bank_Acc_Number" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Bank_Acc_Branch_Address" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Bank_Registered_Phone" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Bank_Cancelled_Chk_Photo" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Creatde_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
          <Property Name="Updater_Comment" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="RLT_BANK_NAMES" Relationship="Self.FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES" FromRole="RLT_CAR_OWNER_BANK_DETAILS" ToRole="RLT_BANK_NAMES" />
        </EntityType>
        <EntityType Name="RLT_CAR_OWNER_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Vendor_PKID" Type="Int32" />
          <Property Name="Car_Owner_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Photo" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Phone1" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Phone2" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Phone1_IsWhatsup" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_DL" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Aadhaar" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Owner_Address" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Update_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CAR_SEGMENT">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Car_Segment" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Car_Segment_Description" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Per_KM_fare" Type="Double" />
          <Property Name="Capacity" Type="Int32" />
          <Property Name="features" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CITY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="City_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="City_Abbr" Type="String" MaxLength="5" FixedLength="false" Unicode="false" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="State_PKID" Type="Int32" />
          <Property Name="latitude" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="longitude" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="eLoc" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="orderIndex" Type="Int32" />
          <Property Name="score" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_BOOKING_PAYMENT_DETAILS" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff" FromRole="RLT_CITY" ToRole="RLT_BOOKING_PAYMENT_DETAILS" />
          <NavigationProperty Name="RLT_BOOKING_PAYMENT_DETAILS1" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP" FromRole="RLT_CITY" ToRole="RLT_BOOKING_PAYMENT_DETAILS" />
          <NavigationProperty Name="RLT_STATE" Relationship="Self.FK_RLT_CITY_RLT_STATE" FromRole="RLT_CITY" ToRole="RLT_STATE" />
          <NavigationProperty Name="RLT_LOCATION_CODE" Relationship="Self.FK_RLT_LOCATION_CODE_RLT_CITY" FromRole="RLT_CITY" ToRole="RLT_LOCATION_CODE" />
          <NavigationProperty Name="RLT_ROUTES_DETAILS" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF" FromRole="RLT_CITY" ToRole="RLT_ROUTES_DETAILS" />
          <NavigationProperty Name="RLT_ROUTES_DETAILS1" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP" FromRole="RLT_CITY" ToRole="RLT_ROUTES_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_CLASS_MASTER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Class_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Class_Charges_PKMs" Type="Double" />
          <Property Name="Waiting_Charges" Type="Int32" />
          <Property Name="Driver_Night_Charges" Type="Int32" />
          <Property Name="Other_Charges" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_Company_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Company_Name" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Company_Address" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="State_GSTIN" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="SAC_Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_CONTACT_ENQUIRIES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNumber" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="EnquiryMessage" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="EMailID" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="RequestDate" Type="DateTime" Precision="3" />
          <Property Name="Remark" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="IsWhatsAppNumber" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ContactInformation">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Address" Type="String" MaxLength="350" FixedLength="false" Unicode="true" />
          <Property Name="SalesTelNo" Type="String" MaxLength="170" FixedLength="false" Unicode="true" />
          <Property Name="BusinessEmailNo" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="WorkingHours" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="GPSCordinationLatitude" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="GPSCordinationLongitude" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="isActive" Type="Boolean" />
          <Property Name="CreateDate" Type="DateTime" Precision="3" />
          <Property Name="SalesEmailNo" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="BusinessTelNo" Type="String" MaxLength="170" FixedLength="false" Unicode="true" />
          <Property Name="GoogleMapLink" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="FBLink" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="TwitterLink" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="InstagramLink" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="PinsterLink" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="GooglePlusLink" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_COUNTRY">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Country_Name" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Counrty_Abbr" Type="String" MaxLength="5" FixedLength="false" Unicode="false" />
          <Property Name="Is_Active" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_STATE" Relationship="Self.FK_RLT_STATE_RLT_COUNTRY" FromRole="RLT_COUNTRY" ToRole="RLT_STATE" />
        </EntityType>
        <EntityType Name="RLT_Department_Name">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DepartmentName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_DISCOUNT_COUPON">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Discount_Coupon" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Discount" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DiscountImage" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="Coupon_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Coupon_Remark" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Max_Discount" Type="Int32" />
          <Property Name="Fare_When_Applied" Type="Int32" />
          <Property Name="Coupon_Last_Date" Type="DateTime" Precision="3" />
          <Property Name="CouponMaxTimeAllowance" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Updated_By" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_DOCUMNETS_NAME">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Doc_For" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Doc_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Is_Doc_Req" Type="Boolean" />
          <Property Name="Is_Doc_Expiry_Date" Type="Boolean" />
          <Property Name="Is_Active" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_Vendor_Docs" Relationship="Self.FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME" FromRole="RLT_DOCUMNETS_NAME" ToRole="RLT_Vendor_Docs" />
          <NavigationProperty Name="Vendor_Car_Driver_Docs" Relationship="Self.FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME" FromRole="RLT_DOCUMNETS_NAME" ToRole="Vendor_Car_Driver_Docs" />
        </EntityType>
        <EntityType Name="RLT_DRIVER_APPROVE_STATUS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Status" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_By" Type="Int32" />
          <Property Name="Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Modified_By" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_DRIVER_ENQUIRIES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Driver_Name" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Phone_1" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Car_No" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Mail" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Address" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="RequestDate" Type="DateTime" Precision="3" />
          <Property Name="Driver_Approving_Status" Type="Int32" />
          <Property Name="Remark" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Created_BY" Type="Int32" />
          <Property Name="Updated_BY" Type="Int32" />
          <Property Name="IsWhatsAppNumber" Type="Boolean" />
        </EntityType>
        <EntityType Name="RLT_ENTITY_GENERALS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Entity_Action_Code" Type="Int32" />
          <Property Name="Entity_Flow_Code" Type="Int32" />
          <Property Name="Entity_Flow_Run_By" Type="Int32" Nullable="false" />
          <Property Name="Action_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_FAQ_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="FAQDetails" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_LOCATION_CODE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ZIP_Code" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Creatded_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="City_PKID" Type="Int32" />
          <Property Name="Location_Name" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CITY" Relationship="Self.FK_RLT_LOCATION_CODE_RLT_CITY" FromRole="RLT_LOCATION_CODE" ToRole="RLT_CITY" />
        </EntityType>
        <EntityType Name="RLT_LOG">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="Date" />
            <PropertyRef Name="Thread" />
            <PropertyRef Name="Level" />
            <PropertyRef Name="Logger" />
            <PropertyRef Name="Message" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Date" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Thread" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Level" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Logger" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Message" Type="String" MaxLength="4000" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Exception" Type="String" MaxLength="2000" FixedLength="false" Unicode="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_MENU_MASTER">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MenuName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="MenuIcon" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="MenuURL" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="PageId" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ParentMenuId" Type="Int32" Nullable="false" />
          <Property Name="IsAdminMenu" Type="Int32" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ActiveMenuClass" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="OrderNumber" Type="Double" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedBy" Type="Int32" />
          <Property Name="ActionName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ControllerName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLT_NextId">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Count" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_PAYMENT_METHOD">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Payment_Method_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_BOOKING_PAYMENT_DETAILS" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD" FromRole="RLT_PAYMENT_METHOD" ToRole="RLT_BOOKING_PAYMENT_DETAILS" />
          <Property Name="PhonePePaymentMethod" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLT_PrivacyPolicy">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PrivacyPolicy" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ROLE_MENU_MAPPING">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Role_Id" Type="Int32" Nullable="false" />
          <Property Name="Menu_Id" Type="Int32" Nullable="false" />
          <Property Name="Is_Add" Type="Boolean" Nullable="false" />
          <Property Name="Is_Edit" Type="Boolean" Nullable="false" />
          <Property Name="Is_Delete" Type="Boolean" Nullable="false" />
          <Property Name="Is_View" Type="Boolean" Nullable="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="String" MaxLength="75" FixedLength="false" Unicode="true" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ROUTE_PLAN">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="From_City_PKID" Type="Int32" />
          <Property Name="To_City_PKID" Type="Int32" />
          <Property Name="Car_PKID" Type="Int32" />
          <Property Name="Fecilities_PKID" Type="Int32" />
          <Property Name="Car_Class_PKID" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Charges_Master_PKID" Type="Int32" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_ROUTES_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="Route_Distance_KM" Type="Int32" />
          <Property Name="Total_Paid_Tolls_In_Route" Type="Int32" />
          <Property Name="Pickup_City_PKID" Type="Int32" />
          <Property Name="Dropoff_City_PKID" Type="Int32" />
          <Property Name="Car_Category_PKID" Type="Int32" />
          <Property Name="Car_Trip_Type_PKID" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_CATEGORY" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_ROUTES_DETAILS" ToRole="RLT_CAR_CATEGORY" />
          <NavigationProperty Name="RLT_CITY" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF" FromRole="RLT_ROUTES_DETAILS" ToRole="RLT_CITY" />
          <NavigationProperty Name="RLT_CITY1" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP" FromRole="RLT_ROUTES_DETAILS" ToRole="RLT_CITY" />
          <NavigationProperty Name="RLT_TRIP_TYPES" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES" FromRole="RLT_ROUTES_DETAILS" ToRole="RLT_TRIP_TYPES" />
        </EntityType>
        <EntityType Name="RLT_Services">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ServiceName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ServiceIconClass" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ServiceImagePath" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="BriefIntroAboutService" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="AboutService" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_STATE">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="State_Name" Type="String" MaxLength="40" FixedLength="false" Unicode="false" />
          <Property Name="State_Abbr" Type="String" MaxLength="5" FixedLength="false" Unicode="false" />
          <Property Name="Counrty_PKID" Type="Int32" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CITY" Relationship="Self.FK_RLT_CITY_RLT_STATE" FromRole="RLT_STATE" ToRole="RLT_CITY" />
          <NavigationProperty Name="RLT_COUNTRY" Relationship="Self.FK_RLT_STATE_RLT_COUNTRY" FromRole="RLT_STATE" ToRole="RLT_COUNTRY" />
        </EntityType>
        <EntityType Name="RLT_SUBSCRIBERS">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SubsciberMailID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="IsValid" Type="Boolean" />
          <Property Name="SubscriptionDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_Template_Mail_Message">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SendToType" Type="Int32" />
          <Property Name="SendToId" Type="Int64" />
          <Property Name="Mail_SMS_Type" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="TemplateId" Type="Int32" />
          <Property Name="MobileNo" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="MailId" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Subject" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Message_Body" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_Template_Master">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Mail_SMS_Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Template_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Subject" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Message_Body" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_TermsConditions">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TermsConditions" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_TRIP_TYPES">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Trip_Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_BOOKING_PAYMENT_DETAILS" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES" FromRole="RLT_TRIP_TYPES" ToRole="RLT_BOOKING_PAYMENT_DETAILS" />
          <NavigationProperty Name="RLT_ROUTES_DETAILS" Relationship="Self.FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES" FromRole="RLT_TRIP_TYPES" ToRole="RLT_ROUTES_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_USER_LOGIN_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PK_GUID" Type="Guid" />
          <Property Name="RLT_User_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="RLT_User_Phone" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="RLT_User_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="RLT_User_Login_Signup_Method" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="RLT_User_Aggreed" Type="String" MaxLength="10" FixedLength="true" Unicode="true" />
          <Property Name="RLT_User_Pwd" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="RLT_User_Gender" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="RLT_User_DOB" Type="DateTime" Precision="3" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Login_Date_Time" Type="DateTime" Precision="3" />
          <Property Name="RLT_User_2FA_Auth_Status" Type="Boolean" />
          <Property Name="OTPNumber" Type="Int32" />
          <Property Name="OTPUpdatedDateTime" Type="DateTime" Precision="3" />
          <Property Name="UserID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="RLT_BOOKING_PAYMENT_DETAILS" Relationship="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS" FromRole="RLT_USER_LOGIN_DETAILS" ToRole="RLT_BOOKING_PAYMENT_DETAILS" />
        </EntityType>
        <EntityType Name="RLT_UserFeedBack">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="EmailID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNo" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Country" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Photo" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Message" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreateDate" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Bank_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Bank_Name_PKID" Type="Int32" />
          <Property Name="IFSC_CODE" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Account_Holder_Name" Type="String" MaxLength="60" FixedLength="false" Unicode="true" />
          <Property Name="Account_Number" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Vendor_Ref_PKID" Type="Int32" Nullable="false" />
          <Property Name="Bank_Status" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="LastModified_Date" Type="DateTime" Precision="3" />
          <Property Name="Lastmodified_By" Type="Int32" Nullable="false" />
          <Property Name="Is_active" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLT_VENDOR_CAR_DETAILS">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Car_Company_Ref" Type="Int32" Nullable="false" />
          <Property Name="Car_Segment_Ref" Type="Int32" Nullable="false" />
          <Property Name="Car_Fuel_Type_Ref" Type="Int32" Nullable="false" />
          <Property Name="Car_Number" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Car_Registration_Date" Type="DateTime" Precision="3" />
          <Property Name="Driver_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Phone1" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Driver_Phone2" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Ref_ID" Type="Int32" Nullable="false" />
          <Property Name="Is_Active" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="CreatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_CAR_CATEGORY" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY" FromRole="RLT_VENDOR_CAR_DETAILS" ToRole="RLT_CAR_CATEGORY" />
          <NavigationProperty Name="RLT_CAR_COMPANY" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY" FromRole="RLT_VENDOR_CAR_DETAILS" ToRole="RLT_CAR_COMPANY" />
          <NavigationProperty Name="RLT_CAR_FUEL_TYPES" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES" FromRole="RLT_VENDOR_CAR_DETAILS" ToRole="RLT_CAR_FUEL_TYPES" />
          <NavigationProperty Name="RLT_Vendor_Details" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details" FromRole="RLT_VENDOR_CAR_DETAILS" ToRole="RLT_Vendor_Details" />
          <NavigationProperty Name="Vendor_Car_Driver_Docs" Relationship="Self.FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS" FromRole="RLT_VENDOR_CAR_DETAILS" ToRole="Vendor_Car_Driver_Docs" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Details">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Vendor_Company_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Owner_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Photo" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_EmailID" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Phone1" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Phone2" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Phone1_IsWhatsup" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Address" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Member_Id" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_LoginID" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Vendor_Pwd" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" />
          <Property Name="Is_2FA_Activated" Type="Boolean" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Updated_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" />
          <Property Name="CreatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_VENDOR_CAR_DETAILS" Relationship="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details" FromRole="RLT_Vendor_Details" ToRole="RLT_VENDOR_CAR_DETAILS" />
          <NavigationProperty Name="RLT_Vendor_Docs" Relationship="Self.FK_RLT_Vendor_Docs_RLT_Vendor_Details" FromRole="RLT_Vendor_Details" ToRole="RLT_Vendor_Docs" />
        </EntityType>
        <EntityType Name="RLT_Vendor_Docs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Vendor_Doc_Id" Type="Int32" Nullable="false" />
          <Property Name="Vendor_Ref_PKID" Type="Int32" Nullable="false" />
          <Property Name="Document_Path" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Is_Verified" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="Comments" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_DOCUMNETS_NAME" Relationship="Self.FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME" FromRole="RLT_Vendor_Docs" ToRole="RLT_DOCUMNETS_NAME" />
          <NavigationProperty Name="RLT_Vendor_Details" Relationship="Self.FK_RLT_Vendor_Docs_RLT_Vendor_Details" FromRole="RLT_Vendor_Docs" ToRole="RLT_Vendor_Details" />
        </EntityType>
        <EntityType Name="RLTBookingAuditTrail">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLTConfigurationAuditTrail">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RequestDataChange" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ResourceActionCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLTMostFavoriteRoute">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PickUpCityID" Type="Int32" />
          <Property Name="DropOffCityID" Type="Int32" />
          <Property Name="CarCategoryID" Type="Int32" />
          <Property Name="TripType" Type="Int32" />
          <Property Name="Fare" Type="Decimal" Precision="18" Scale="0" />
          <Property Name="GSTFare" Type="Decimal" Precision="18" Scale="0" />
          <Property Name="Distance" Type="Decimal" Precision="18" Scale="0" />
          <Property Name="TravelDuration" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Facilities" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
          <Property Name="CityImage" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="RLTResource">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ResourceCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="ResourceCodeText" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ResourceStatus" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RLTResourceStau">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="StatusName" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="RoleClaim">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RoleId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ClaimType" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ClaimValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Role" Relationship="Self.FK_RoleClaims_Role_RoleId" FromRole="RoleClaims" ToRole="Roles" />
        </EntityType>
        <EntityType Name="Role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="ConcurrencyStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="RoleClaims" Relationship="Self.FK_RoleClaims_Role_RoleId" FromRole="Roles" ToRole="RoleClaims" />
          <NavigationProperty Name="Users" Relationship="Self.UserRoles" FromRole="Roles" ToRole="Users" />
        </EntityType>
        <EntityType Name="TempBooking">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="User_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Mobile_Number" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="PickUp_Address" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="DropOff_Address" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Travel_Date" Type="DateTime" Precision="3" />
          <Property Name="Travel_Time" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Is_Active" Type="Boolean" Nullable="false" />
          <Property Name="Booking_Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="UserClaim">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ClaimType" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ClaimValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserClaims_User_UserId" FromRole="UserClaims" ToRole="Users" />
        </EntityType>
        <EntityType Name="UserDiscountAvailed">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserId" Type="String" MaxLength="900" FixedLength="false" Unicode="true" />
          <Property Name="DiscountCouponId" Type="Int32" />
          <Property Name="AvailedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="UpdatedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="UpdatedBy" Type="Int32" />
        </EntityType>
        <EntityType Name="UserLogin">
          <Key>
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="ProviderKey" />
          </Key>
          <Property Name="LoginProvider" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProviderKey" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProviderDisplayName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserLogins_User_UserId" FromRole="UserLogins" ToRole="Users" />
        </EntityType>
        <EntityType Name="User">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedUserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedEmail" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="EmailConfirmed" Type="Boolean" Nullable="false" />
          <Property Name="PasswordHash" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="SecurityStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ConcurrencyStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNumber" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNumberConfirmed" Type="Boolean" Nullable="false" />
          <Property Name="TwoFactorEnabled" Type="Boolean" Nullable="false" />
          <Property Name="LockoutEnd" Type="DateTimeOffset" Precision="7" />
          <Property Name="LockoutEnabled" Type="Boolean" Nullable="false" />
          <Property Name="AccessFailedCount" Type="Int32" Nullable="false" />
          <Property Name="FirstName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserType" Type="Int32" Nullable="false" />
          <Property Name="CurrentOTPNumber" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="OTPExpireTimeInMinute" Type="Int32" Nullable="false" />
          <Property Name="OTPGeneratedDate" Type="DateTime" Precision="3" />
          <Property Name="UserProfilePicture" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="UserClaims" Relationship="Self.FK_UserClaims_User_UserId" FromRole="Users" ToRole="UserClaims" />
          <NavigationProperty Name="UserLogins" Relationship="Self.FK_UserLogins_User_UserId" FromRole="Users" ToRole="UserLogins" />
          <NavigationProperty Name="UserTokens" Relationship="Self.FK_UserTokens_User_UserId" FromRole="Users" ToRole="UserTokens" />
          <NavigationProperty Name="Roles" Relationship="Self.UserRoles" FromRole="Users" ToRole="Roles" />
        </EntityType>
        <EntityType Name="UserToken">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LoginProvider" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Value" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserTokens_User_UserId" FromRole="UserTokens" ToRole="Users" />
        </EntityType>
        <EntityType Name="Vendor_Car_Driver_Docs">
          <Key>
            <PropertyRef Name="PKID" />
          </Key>
          <Property Name="PKID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Doc_Name" Type="Int32" Nullable="false" />
          <Property Name="Vendor_Car_Ref" Type="Int32" Nullable="false" />
          <Property Name="Document_Path" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Is_Verified" Type="Boolean" Nullable="false" />
          <Property Name="Created_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_Date" Type="DateTime" Precision="3" />
          <Property Name="Last_Modified_By" Type="Int32" Nullable="false" />
          <Property Name="Comments" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="CreatedBy" Type="Int32" />
          <NavigationProperty Name="RLT_DOCUMNETS_NAME" Relationship="Self.FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME" FromRole="Vendor_Car_Driver_Docs" ToRole="RLT_DOCUMNETS_NAME" />
          <NavigationProperty Name="RLT_VENDOR_CAR_DETAILS" Relationship="Self.FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS" FromRole="Vendor_Car_Driver_Docs" ToRole="RLT_VENDOR_CAR_DETAILS" />
        </EntityType>
        <EntityType Name="RefreshToken">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Token" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Expires" Type="DateTime" Nullable="false" Precision="7" />
          <Property Name="Created" Type="DateTime" Nullable="false" Precision="7" />
          <Property Name="CreatedByIp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Revoked" Type="DateTime" Precision="7" />
          <Property Name="RevokedByIp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ReplacedByToken" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ApplicationUserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="User" Relationship="Self.FK_RefreshToken_User_ApplicationUserId" FromRole="RefreshToken" ToRole="User" />
        </EntityType>
        <EntityType Name="Role1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="ConcurrencyStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="RoleClaims1" Relationship="Self.FK_RoleClaims_Role_RoleId1" FromRole="Role" ToRole="RoleClaims1" />
          <NavigationProperty Name="Users" Relationship="Self.UserRoles1" FromRole="Role" ToRole="User" />
        </EntityType>
        <EntityType Name="RoleClaims1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RoleId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ClaimType" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ClaimValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Role" Relationship="Self.FK_RoleClaims_Role_RoleId1" FromRole="RoleClaims1" ToRole="Role" />
        </EntityType>
        <EntityType Name="User1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedUserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="NormalizedEmail" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="EmailConfirmed" Type="Boolean" Nullable="false" />
          <Property Name="PasswordHash" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="SecurityStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ConcurrencyStamp" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNumber" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="PhoneNumberConfirmed" Type="Boolean" Nullable="false" />
          <Property Name="TwoFactorEnabled" Type="Boolean" Nullable="false" />
          <Property Name="LockoutEnd" Type="DateTimeOffset" Precision="7" />
          <Property Name="LockoutEnabled" Type="Boolean" Nullable="false" />
          <Property Name="AccessFailedCount" Type="Int32" Nullable="false" />
          <Property Name="FirstName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserType" Type="Int32" Nullable="false" />
          <Property Name="OTPExpireTimeInMinute" Type="Int32" Nullable="false" />
          <Property Name="UserProfilePicture" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="RefreshTokens" Relationship="Self.FK_RefreshToken_User_ApplicationUserId" FromRole="User" ToRole="RefreshToken" />
          <NavigationProperty Name="UserClaims1" Relationship="Self.FK_UserClaims_User_UserId1" FromRole="User" ToRole="UserClaims1" />
          <NavigationProperty Name="UserLogins1" Relationship="Self.FK_UserLogins_User_UserId1" FromRole="User" ToRole="UserLogins1" />
          <NavigationProperty Name="UserTokens1" Relationship="Self.FK_UserTokens_User_UserId1" FromRole="User" ToRole="UserTokens1" />
          <NavigationProperty Name="Roles" Relationship="Self.UserRoles1" FromRole="User" ToRole="Role" />
          <Property Name="CurrentOTPNumber" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="OTPGeneratedDate" Type="DateTime" Precision="7" />
        </EntityType>
        <EntityType Name="UserClaims1">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ClaimType" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ClaimValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserClaims_User_UserId1" FromRole="UserClaims1" ToRole="User" />
        </EntityType>
        <EntityType Name="UserLogins1">
          <Key>
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="ProviderKey" />
          </Key>
          <Property Name="LoginProvider" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProviderKey" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProviderDisplayName" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserLogins_User_UserId1" FromRole="UserLogins1" ToRole="User" />
        </EntityType>
        <EntityType Name="UserTokens1">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="LoginProvider" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="UserId" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LoginProvider" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="450" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Value" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="User" Relationship="Self.FK_UserTokens_User_UserId1" FromRole="UserTokens1" ToRole="User" />
        </EntityType>
        <Association Name="FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES">
          <End Role="RLT_BANK_NAMES" Type="Self.RLT_BANK_NAMES" Multiplicity="0..1" />
          <End Role="RLT_CAR_OWNER_BANK_DETAILS" Type="Self.RLT_CAR_OWNER_BANK_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_BANK_NAMES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_OWNER_BANK_DETAILS">
              <PropertyRef Name="Bank_Name_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Dropoff_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Pickup_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD">
          <End Role="RLT_PAYMENT_METHOD" Type="Self.RLT_PAYMENT_METHOD" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_PAYMENT_METHOD">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Payment_Mode_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES">
          <End Role="RLT_TRIP_TYPES" Type="Self.RLT_TRIP_TYPES" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_TRIP_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Passenger_Trip_Type_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS">
          <End Role="RLT_USER_LOGIN_DETAILS" Type="Self.RLT_USER_LOGIN_DETAILS" Multiplicity="0..1" />
          <End Role="RLT_BOOKING_PAYMENT_DETAILS" Type="Self.RLT_BOOKING_PAYMENT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_USER_LOGIN_DETAILS">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_BOOKING_PAYMENT_DETAILS">
              <PropertyRef Name="Booking_Login_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_CAR_CHARGES_FECILITIES_DETAILS" Type="Self.RLT_CAR_CHARGES_FECILITIES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_CHARGES_FECILITIES_DETAILS">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Car_Category_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY">
          <End Role="RLT_CAR_CATEGORY" Type="Self.RLT_CAR_CATEGORY" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_CATEGORY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Segment_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_COMPANY">
          <End Role="RLT_CAR_COMPANY" Type="Self.RLT_CAR_COMPANY" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_COMPANY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Company_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY">
          <End Role="RLT_CAR_COMPANY" Type="Self.RLT_CAR_COMPANY" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_COMPANY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Company_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES">
          <End Role="RLT_CAR_FUEL_TYPES" Type="Self.RLT_CAR_FUEL_TYPES" Multiplicity="0..1" />
          <End Role="RLT_CAR_MODEL" Type="Self.RLT_CAR_MODEL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_FUEL_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CAR_MODEL">
              <PropertyRef Name="Car_Fuel_Type_Status" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES">
          <End Role="RLT_CAR_FUEL_TYPES" Type="Self.RLT_CAR_FUEL_TYPES" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CAR_FUEL_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Car_Fuel_Type_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_CITY_RLT_STATE">
          <End Role="RLT_STATE" Type="Self.RLT_STATE" Multiplicity="0..1" />
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_STATE">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_CITY">
              <PropertyRef Name="State_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_LOCATION_CODE_RLT_CITY">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_LOCATION_CODE" Type="Self.RLT_LOCATION_CODE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_LOCATION_CODE">
              <PropertyRef Name="City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Dropoff_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP">
          <End Role="RLT_CITY" Type="Self.RLT_CITY" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_CITY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Pickup_City_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_STATE_RLT_COUNTRY">
          <End Role="RLT_COUNTRY" Type="Self.RLT_COUNTRY" Multiplicity="0..1" />
          <End Role="RLT_STATE" Type="Self.RLT_STATE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_COUNTRY">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_STATE">
              <PropertyRef Name="Counrty_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME">
          <End Role="RLT_DOCUMNETS_NAME" Type="Self.RLT_DOCUMNETS_NAME" Multiplicity="1" />
          <End Role="RLT_Vendor_Docs" Type="Self.RLT_Vendor_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_DOCUMNETS_NAME">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_Vendor_Docs">
              <PropertyRef Name="Vendor_Doc_Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME">
          <End Role="RLT_DOCUMNETS_NAME" Type="Self.RLT_DOCUMNETS_NAME" Multiplicity="1" />
          <End Role="Vendor_Car_Driver_Docs" Type="Self.Vendor_Car_Driver_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_DOCUMNETS_NAME">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="Vendor_Car_Driver_Docs">
              <PropertyRef Name="Doc_Name" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES">
          <End Role="RLT_TRIP_TYPES" Type="Self.RLT_TRIP_TYPES" Multiplicity="0..1" />
          <End Role="RLT_ROUTES_DETAILS" Type="Self.RLT_ROUTES_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_TRIP_TYPES">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_ROUTES_DETAILS">
              <PropertyRef Name="Car_Trip_Type_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details">
          <End Role="RLT_Vendor_Details" Type="Self.RLT_Vendor_Details" Multiplicity="1" />
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_Vendor_Details">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="Vendor_Ref_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS">
          <End Role="RLT_VENDOR_CAR_DETAILS" Type="Self.RLT_VENDOR_CAR_DETAILS" Multiplicity="1" />
          <End Role="Vendor_Car_Driver_Docs" Type="Self.Vendor_Car_Driver_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_VENDOR_CAR_DETAILS">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="Vendor_Car_Driver_Docs">
              <PropertyRef Name="Vendor_Car_Ref" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RLT_Vendor_Docs_RLT_Vendor_Details">
          <End Role="RLT_Vendor_Details" Type="Self.RLT_Vendor_Details" Multiplicity="1" />
          <End Role="RLT_Vendor_Docs" Type="Self.RLT_Vendor_Docs" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RLT_Vendor_Details">
              <PropertyRef Name="PKID" />
            </Principal>
            <Dependent Role="RLT_Vendor_Docs">
              <PropertyRef Name="Vendor_Ref_PKID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RoleClaims_Role_RoleId">
          <End Role="Roles" Type="Self.Role" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="RoleClaims" Type="Self.RoleClaim" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Roles">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RoleClaims">
              <PropertyRef Name="RoleId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserClaims_User_UserId">
          <End Role="Users" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserClaims" Type="Self.UserClaim" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserClaims">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserLogins_User_UserId">
          <End Role="Users" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserLogins" Type="Self.UserLogin" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserLogins">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserTokens_User_UserId">
          <End Role="Users" Type="Self.User" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserTokens" Type="Self.UserToken" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Users">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserTokens">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RefreshToken_User_ApplicationUserId">
          <End Role="User" Type="Self.User1" Multiplicity="0..1" />
          <End Role="RefreshToken" Type="Self.RefreshToken" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RefreshToken">
              <PropertyRef Name="ApplicationUserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_RoleClaims_Role_RoleId1">
          <End Role="Role" Type="Self.Role1" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="RoleClaims1" Type="Self.RoleClaims1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RoleClaims1">
              <PropertyRef Name="RoleId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserClaims_User_UserId1">
          <End Role="User" Type="Self.User1" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserClaims1" Type="Self.UserClaims1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserClaims1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserLogins_User_UserId1">
          <End Role="User" Type="Self.User1" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserLogins1" Type="Self.UserLogins1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserLogins1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_UserTokens_User_UserId1">
          <End Role="User" Type="Self.User1" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="UserTokens1" Type="Self.UserTokens1" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="User">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="UserTokens1">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="UserRoles">
          <End Role="Roles" Type="Self.Role" Multiplicity="*" />
          <End Role="Users" Type="Self.User" Multiplicity="*" />
        </Association>
        <Association Name="UserRoles1">
          <End Role="Role" Type="Self.Role1" Multiplicity="*" />
          <End Role="User" Type="Self.User1" Multiplicity="*" />
        </Association>
        <EntityContainer Name="RLTDBContext" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="C__EFMigrationsHistory" EntityType="Self.C__EFMigrationsHistory" />
          <EntitySet Name="C__EFMigrationsHistory1" EntityType="Self.C__EFMigrationsHistory1" />
          <EntitySet Name="Products" EntityType="Self.Product" />
          <EntitySet Name="RLT_AboutUs" EntityType="Self.RLT_AboutUs" />
          <EntitySet Name="RLT_ADMIN_ROLE" EntityType="Self.RLT_ADMIN_ROLE" />
          <EntitySet Name="RLT_ADMIN_USER" EntityType="Self.RLT_ADMIN_USER" />
          <EntitySet Name="RLT_Admin_User_Info" EntityType="Self.RLT_Admin_User_Info" />
          <EntitySet Name="RLT_BANK_NAMES" EntityType="Self.RLT_BANK_NAMES" />
          <EntitySet Name="RLT_BOOKING" EntityType="Self.RLT_BOOKING" />
          <EntitySet Name="RLT_BOOKING_FARE" EntityType="Self.RLT_BOOKING_FARE" />
          <EntitySet Name="RLT_BOOKING_GST" EntityType="Self.RLT_BOOKING_GST" />
          <EntitySet Name="RLT_BOOKING_MODE" EntityType="Self.RLT_BOOKING_MODE" />
          <EntitySet Name="RLT_BOOKING_PAYMENT_DETAILS" EntityType="Self.RLT_BOOKING_PAYMENT_DETAILS" />
          <EntitySet Name="RLT_BOOKING_RULES" EntityType="Self.RLT_BOOKING_RULES" />
          <EntitySet Name="RLT_BOOKING_STATUS" EntityType="Self.RLT_BOOKING_STATUS" />
          <EntitySet Name="RLT_BOOKINGS" EntityType="Self.RLT_BOOKINGS" />
          <EntitySet Name="RLT_CAR_CATEGORY" EntityType="Self.RLT_CAR_CATEGORY" />
          <EntitySet Name="RLT_CAR_CHARGES_FECILITIES_DETAILS" EntityType="Self.RLT_CAR_CHARGES_FECILITIES_DETAILS" />
          <EntitySet Name="RLT_CAR_COMPANY" EntityType="Self.RLT_CAR_COMPANY" />
          <EntitySet Name="RLT_CAR_DETAILS" EntityType="Self.RLT_CAR_DETAILS" />
          <EntitySet Name="RLT_CAR_DOCS" EntityType="Self.RLT_CAR_DOCS" />
          <EntitySet Name="RLT_CAR_DRIVER_DETAILS" EntityType="Self.RLT_CAR_DRIVER_DETAILS" />
          <EntitySet Name="RLT_CAR_DRIVER_DOCS" EntityType="Self.RLT_CAR_DRIVER_DOCS" />
          <EntitySet Name="RLT_CAR_FUEL_TYPES" EntityType="Self.RLT_CAR_FUEL_TYPES" />
          <EntitySet Name="RLT_CAR_MODEL" EntityType="Self.RLT_CAR_MODEL" />
          <EntitySet Name="RLT_CAR_OWNER_BANK_DETAILS" EntityType="Self.RLT_CAR_OWNER_BANK_DETAILS" />
          <EntitySet Name="RLT_CAR_OWNER_DETAILS" EntityType="Self.RLT_CAR_OWNER_DETAILS" />
          <EntitySet Name="RLT_CAR_SEGMENT" EntityType="Self.RLT_CAR_SEGMENT" />
          <EntitySet Name="RLT_CITY" EntityType="Self.RLT_CITY" />
          <EntitySet Name="RLT_CLASS_MASTER" EntityType="Self.RLT_CLASS_MASTER" />
          <EntitySet Name="RLT_Company_Details" EntityType="Self.RLT_Company_Details" />
          <EntitySet Name="RLT_CONTACT_ENQUIRIES" EntityType="Self.RLT_CONTACT_ENQUIRIES" />
          <EntitySet Name="RLT_ContactInformation" EntityType="Self.RLT_ContactInformation" />
          <EntitySet Name="RLT_COUNTRY" EntityType="Self.RLT_COUNTRY" />
          <EntitySet Name="RLT_Department_Name" EntityType="Self.RLT_Department_Name" />
          <EntitySet Name="RLT_DISCOUNT_COUPON" EntityType="Self.RLT_DISCOUNT_COUPON" />
          <EntitySet Name="RLT_DOCUMNETS_NAME" EntityType="Self.RLT_DOCUMNETS_NAME" />
          <EntitySet Name="RLT_DRIVER_APPROVE_STATUS" EntityType="Self.RLT_DRIVER_APPROVE_STATUS" />
          <EntitySet Name="RLT_DRIVER_ENQUIRIES" EntityType="Self.RLT_DRIVER_ENQUIRIES" />
          <EntitySet Name="RLT_ENTITY_GENERALS" EntityType="Self.RLT_ENTITY_GENERALS" />
          <EntitySet Name="RLT_FAQ_Details" EntityType="Self.RLT_FAQ_Details" />
          <EntitySet Name="RLT_LOCATION_CODE" EntityType="Self.RLT_LOCATION_CODE" />
          <EntitySet Name="RLT_LOG" EntityType="Self.RLT_LOG" />
          <EntitySet Name="RLT_MENU_MASTER" EntityType="Self.RLT_MENU_MASTER" />
          <EntitySet Name="RLT_NextId" EntityType="Self.RLT_NextId" />
          <EntitySet Name="RLT_PAYMENT_METHOD" EntityType="Self.RLT_PAYMENT_METHOD" />
          <EntitySet Name="RLT_PrivacyPolicy" EntityType="Self.RLT_PrivacyPolicy" />
          <EntitySet Name="RLT_ROLE_MENU_MAPPING" EntityType="Self.RLT_ROLE_MENU_MAPPING" />
          <EntitySet Name="RLT_ROUTE_PLAN" EntityType="Self.RLT_ROUTE_PLAN" />
          <EntitySet Name="RLT_ROUTES_DETAILS" EntityType="Self.RLT_ROUTES_DETAILS" />
          <EntitySet Name="RLT_Services" EntityType="Self.RLT_Services" />
          <EntitySet Name="RLT_STATE" EntityType="Self.RLT_STATE" />
          <EntitySet Name="RLT_SUBSCRIBERS" EntityType="Self.RLT_SUBSCRIBERS" />
          <EntitySet Name="RLT_Template_Mail_Message" EntityType="Self.RLT_Template_Mail_Message" />
          <EntitySet Name="RLT_Template_Master" EntityType="Self.RLT_Template_Master" />
          <EntitySet Name="RLT_TermsConditions" EntityType="Self.RLT_TermsConditions" />
          <EntitySet Name="RLT_TRIP_TYPES" EntityType="Self.RLT_TRIP_TYPES" />
          <EntitySet Name="RLT_USER_LOGIN_DETAILS" EntityType="Self.RLT_USER_LOGIN_DETAILS" />
          <EntitySet Name="RLT_UserFeedBack" EntityType="Self.RLT_UserFeedBack" />
          <EntitySet Name="RLT_Vendor_Bank_Details" EntityType="Self.RLT_Vendor_Bank_Details" />
          <EntitySet Name="RLT_VENDOR_CAR_DETAILS" EntityType="Self.RLT_VENDOR_CAR_DETAILS" />
          <EntitySet Name="RLT_Vendor_Details" EntityType="Self.RLT_Vendor_Details" />
          <EntitySet Name="RLT_Vendor_Docs" EntityType="Self.RLT_Vendor_Docs" />
          <EntitySet Name="RLTBookingAuditTrails" EntityType="Self.RLTBookingAuditTrail" />
          <EntitySet Name="RLTConfigurationAuditTrails" EntityType="Self.RLTConfigurationAuditTrail" />
          <EntitySet Name="RLTMostFavoriteRoutes" EntityType="Self.RLTMostFavoriteRoute" />
          <EntitySet Name="RLTResources" EntityType="Self.RLTResource" />
          <EntitySet Name="RLTResourceStaus" EntityType="Self.RLTResourceStau" />
          <EntitySet Name="RoleClaims" EntityType="Self.RoleClaim" />
          <EntitySet Name="Roles" EntityType="Self.Role" />
          <EntitySet Name="TempBookings" EntityType="Self.TempBooking" />
          <EntitySet Name="UserClaims" EntityType="Self.UserClaim" />
          <EntitySet Name="UserDiscountAvaileds" EntityType="Self.UserDiscountAvailed" />
          <EntitySet Name="UserLogins" EntityType="Self.UserLogin" />
          <EntitySet Name="Users" EntityType="Self.User" />
          <EntitySet Name="UserTokens" EntityType="Self.UserToken" />
          <EntitySet Name="Vendor_Car_Driver_Docs" EntityType="Self.Vendor_Car_Driver_Docs" />
          <EntitySet Name="RefreshTokens" EntityType="Self.RefreshToken" />
          <EntitySet Name="Roles1" EntityType="Self.Role1" />
          <EntitySet Name="RoleClaims1" EntityType="Self.RoleClaims1" />
          <EntitySet Name="Users1" EntityType="Self.User1" />
          <EntitySet Name="UserClaims1" EntityType="Self.UserClaims1" />
          <EntitySet Name="UserLogins1" EntityType="Self.UserLogins1" />
          <EntitySet Name="UserTokens1" EntityType="Self.UserTokens1" />
          <AssociationSet Name="FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES" Association="Self.FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES">
            <End Role="RLT_BANK_NAMES" EntitySet="RLT_BANK_NAMES" />
            <End Role="RLT_CAR_OWNER_BANK_DETAILS" EntitySet="RLT_CAR_OWNER_BANK_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD">
            <End Role="RLT_PAYMENT_METHOD" EntitySet="RLT_PAYMENT_METHOD" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES">
            <End Role="RLT_TRIP_TYPES" EntitySet="RLT_TRIP_TYPES" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS" Association="Self.FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS">
            <End Role="RLT_USER_LOGIN_DETAILS" EntitySet="RLT_USER_LOGIN_DETAILS" />
            <End Role="RLT_BOOKING_PAYMENT_DETAILS" EntitySet="RLT_BOOKING_PAYMENT_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_CAR_CHARGES_FECILITIES_DETAILS" EntitySet="RLT_CAR_CHARGES_FECILITIES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY">
            <End Role="RLT_CAR_CATEGORY" EntitySet="RLT_CAR_CATEGORY" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_COMPANY" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_COMPANY">
            <End Role="RLT_CAR_COMPANY" EntitySet="RLT_CAR_COMPANY" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY">
            <End Role="RLT_CAR_COMPANY" EntitySet="RLT_CAR_COMPANY" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES" Association="Self.FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES">
            <End Role="RLT_CAR_FUEL_TYPES" EntitySet="RLT_CAR_FUEL_TYPES" />
            <End Role="RLT_CAR_MODEL" EntitySet="RLT_CAR_MODEL" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES">
            <End Role="RLT_CAR_FUEL_TYPES" EntitySet="RLT_CAR_FUEL_TYPES" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_CITY_RLT_STATE" Association="Self.FK_RLT_CITY_RLT_STATE">
            <End Role="RLT_STATE" EntitySet="RLT_STATE" />
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_LOCATION_CODE_RLT_CITY" Association="Self.FK_RLT_LOCATION_CODE_RLT_CITY">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_LOCATION_CODE" EntitySet="RLT_LOCATION_CODE" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP">
            <End Role="RLT_CITY" EntitySet="RLT_CITY" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_STATE_RLT_COUNTRY" Association="Self.FK_RLT_STATE_RLT_COUNTRY">
            <End Role="RLT_COUNTRY" EntitySet="RLT_COUNTRY" />
            <End Role="RLT_STATE" EntitySet="RLT_STATE" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME" Association="Self.FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME">
            <End Role="RLT_DOCUMNETS_NAME" EntitySet="RLT_DOCUMNETS_NAME" />
            <End Role="RLT_Vendor_Docs" EntitySet="RLT_Vendor_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME" Association="Self.FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME">
            <End Role="RLT_DOCUMNETS_NAME" EntitySet="RLT_DOCUMNETS_NAME" />
            <End Role="Vendor_Car_Driver_Docs" EntitySet="Vendor_Car_Driver_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES" Association="Self.FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES">
            <End Role="RLT_TRIP_TYPES" EntitySet="RLT_TRIP_TYPES" />
            <End Role="RLT_ROUTES_DETAILS" EntitySet="RLT_ROUTES_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details" Association="Self.FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details">
            <End Role="RLT_Vendor_Details" EntitySet="RLT_Vendor_Details" />
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
          </AssociationSet>
          <AssociationSet Name="FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS" Association="Self.FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS">
            <End Role="RLT_VENDOR_CAR_DETAILS" EntitySet="RLT_VENDOR_CAR_DETAILS" />
            <End Role="Vendor_Car_Driver_Docs" EntitySet="Vendor_Car_Driver_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_RLT_Vendor_Docs_RLT_Vendor_Details" Association="Self.FK_RLT_Vendor_Docs_RLT_Vendor_Details">
            <End Role="RLT_Vendor_Details" EntitySet="RLT_Vendor_Details" />
            <End Role="RLT_Vendor_Docs" EntitySet="RLT_Vendor_Docs" />
          </AssociationSet>
          <AssociationSet Name="FK_RoleClaims_Role_RoleId" Association="Self.FK_RoleClaims_Role_RoleId">
            <End Role="Roles" EntitySet="Roles" />
            <End Role="RoleClaims" EntitySet="RoleClaims" />
          </AssociationSet>
          <AssociationSet Name="FK_UserClaims_User_UserId" Association="Self.FK_UserClaims_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserClaims" EntitySet="UserClaims" />
          </AssociationSet>
          <AssociationSet Name="FK_UserLogins_User_UserId" Association="Self.FK_UserLogins_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserLogins" EntitySet="UserLogins" />
          </AssociationSet>
          <AssociationSet Name="FK_UserTokens_User_UserId" Association="Self.FK_UserTokens_User_UserId">
            <End Role="Users" EntitySet="Users" />
            <End Role="UserTokens" EntitySet="UserTokens" />
          </AssociationSet>
          <AssociationSet Name="FK_RefreshToken_User_ApplicationUserId" Association="Self.FK_RefreshToken_User_ApplicationUserId">
            <End Role="User" EntitySet="Users1" />
            <End Role="RefreshToken" EntitySet="RefreshTokens" />
          </AssociationSet>
          <AssociationSet Name="FK_RoleClaims_Role_RoleId1" Association="Self.FK_RoleClaims_Role_RoleId1">
            <End Role="Role" EntitySet="Roles1" />
            <End Role="RoleClaims1" EntitySet="RoleClaims1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserClaims_User_UserId1" Association="Self.FK_UserClaims_User_UserId1">
            <End Role="User" EntitySet="Users1" />
            <End Role="UserClaims1" EntitySet="UserClaims1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserLogins_User_UserId1" Association="Self.FK_UserLogins_User_UserId1">
            <End Role="User" EntitySet="Users1" />
            <End Role="UserLogins1" EntitySet="UserLogins1" />
          </AssociationSet>
          <AssociationSet Name="FK_UserTokens_User_UserId1" Association="Self.FK_UserTokens_User_UserId1">
            <End Role="User" EntitySet="Users1" />
            <End Role="UserTokens1" EntitySet="UserTokens1" />
          </AssociationSet>
          <AssociationSet Name="UserRoles" Association="Self.UserRoles">
            <End Role="Roles" EntitySet="Roles" />
            <End Role="Users" EntitySet="Users" />
          </AssociationSet>
          <AssociationSet Name="UserRoles1" Association="Self.UserRoles1">
            <End Role="Role" EntitySet="Roles1" />
            <End Role="User" EntitySet="Users1" />
          </AssociationSet>
          <FunctionImport Name="Proc_GetUserDtl" ReturnType="Collection(RLTCARMODEL.Proc_GetUserDtl_Result)">
          <Parameter Name="UserId" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="SP_GET_REPORT_DATA" ReturnType="Collection(RLTCARMODEL.SP_GET_REPORT_DATA_Result)">
            <Parameter Name="Flag" Mode="In" Type="Int32" />
            <Parameter Name="CityFrom" Mode="In" Type="Int32" />
            <Parameter Name="CityTo" Mode="In" Type="Int32" />
            <Parameter Name="FromDate" Mode="In" Type="DateTime" />
            <Parameter Name="ToDate" Mode="In" Type="DateTime" />
            <Parameter Name="BookingStatusId" Mode="In" Type="Int32" />
            <Parameter Name="BookingId" Mode="In" Type="String" />
            <Parameter Name="CustomerName" Mode="In" Type="String" />
            <Parameter Name="CustomerMobile" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Booking_Create">
            <Parameter Name="Booking_Id" Mode="In" Type="String" />
            <Parameter Name="PickUpCity" Mode="In" Type="String" />
            <Parameter Name="DropOffCity" Mode="In" Type="String" />
            <Parameter Name="TripType" Mode="In" Type="String" />
            <Parameter Name="CarCategory" Mode="In" Type="String" />
            <Parameter Name="Duration" Mode="In" Type="String" />
            <Parameter Name="Distance" Mode="In" Type="Decimal" />
            <Parameter Name="BasicFare" Mode="In" Type="Decimal" />
            <Parameter Name="DriverCharge" Mode="In" Type="Decimal" />
            <Parameter Name="GST" Mode="In" Type="Decimal" />
            <Parameter Name="Fare" Mode="In" Type="Decimal" />
            <Parameter Name="GSTFare" Mode="In" Type="Decimal" />
            <Parameter Name="CouponCode" Mode="In" Type="String" />
            <Parameter Name="CouponDiscount" Mode="In" Type="Decimal" />
            <Parameter Name="PickUpAddress" Mode="In" Type="String" />
            <Parameter Name="DropOffAddress" Mode="In" Type="String" />
            <Parameter Name="PickUpDate" Mode="In" Type="DateTime" />
            <Parameter Name="PickUpTime" Mode="In" Type="String" />
            <Parameter Name="TravelerName" Mode="In" Type="String" />
            <Parameter Name="PhoneNumber" Mode="In" Type="String" />
            <Parameter Name="MailId" Mode="In" Type="String" />
            <Parameter Name="PaymentMode" Mode="In" Type="Int32" />
            <Parameter Name="BookingCreatedBy" Mode="In" Type="String" />
            <Parameter Name="RazorpayPaymentID" Mode="In" Type="String" />
            <Parameter Name="RazorpayOrderID" Mode="In" Type="String" />
            <Parameter Name="RazorpaySignature" Mode="In" Type="String" />
            <Parameter Name="RazorpayStatus" Mode="In" Type="String" />
            <Parameter Name="PickUpAddressLongLat" Mode="In" Type="String" />
            <Parameter Name="PickUpAddressLongitude" Mode="In" Type="String" />
            <Parameter Name="CashAmountToPayDriver" Mode="In" Type="Decimal" />
            <Parameter Name="PaymentOption" Mode="In" Type="Int32" />
            <Parameter Name="TollCharge" Mode="In" Type="Decimal" />
            <Parameter Name="result" Mode="InOut" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Booking_Execute">
            <Parameter Name="Booking_Id" Mode="In" Type="String" />
            <Parameter Name="City_From_Id" Mode="In" Type="String" />
            <Parameter Name="City_To_Id" Mode="In" Type="String" />
            <Parameter Name="PickUp_Address" Mode="In" Type="String" />
            <Parameter Name="DropOff_Address" Mode="In" Type="String" />
            <Parameter Name="PickUpLatLong" Mode="In" Type="String" />
            <Parameter Name="DropOffLatLong" Mode="In" Type="String" />
            <Parameter Name="TravelerName" Mode="In" Type="String" />
            <Parameter Name="TravelerEmail" Mode="In" Type="String" />
            <Parameter Name="TravelerPhone" Mode="In" Type="String" />
            <Parameter Name="BookingSessionID" Mode="In" Type="String" />
            <Parameter Name="PaymentOption" Mode="In" Type="String" />
            <Parameter Name="PaymentMode" Mode="In" Type="String" />
            <Parameter Name="IsWhatsAppNumber" Mode="In" Type="Boolean" />
            <Parameter Name="CashToPayDriver" Mode="In" Type="String" />
            <Parameter Name="DriverNightCharge" Mode="In" Type="String" />
            <Parameter Name="Trip_Type_Id" Mode="In" Type="String" />
            <Parameter Name="Car_Category_Id" Mode="In" Type="String" />
            <Parameter Name="Distance" Mode="In" Type="String" />
            <Parameter Name="Basic_Fare" Mode="In" Type="String" />
            <Parameter Name="Toll_Charge" Mode="In" Type="String" />
            <Parameter Name="GST" Mode="In" Type="String" />
            <Parameter Name="Fare" Mode="In" Type="String" />
            <Parameter Name="GST_Fare" Mode="In" Type="String" />
            <Parameter Name="Coupon_Code" Mode="In" Type="String" />
            <Parameter Name="Coupon_Discount" Mode="In" Type="String" />
            <Parameter Name="PickUp_Date" Mode="In" Type="String" />
            <Parameter Name="PickUp_Time" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Contact_Enquiry_Execute">
            <Parameter Name="Name" Mode="In" Type="String" />
            <Parameter Name="PhoneNumber" Mode="In" Type="String" />
            <Parameter Name="EmailID" Mode="In" Type="String" />
            <Parameter Name="IsWhatsAppNumbe" Mode="In" Type="Boolean" />
            <Parameter Name="Message" Mode="In" Type="String" />
            <Parameter Name="IsSucessful" Mode="InOut" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="usp_Driver_Enquiry_Execute">
            <Parameter Name="FirstName" Mode="In" Type="String" />
            <Parameter Name="LastName" Mode="In" Type="String" />
            <Parameter Name="PhoneNumber" Mode="In" Type="String" />
            <Parameter Name="EmailID" Mode="In" Type="String" />
            <Parameter Name="IsWhatsAppNumbe" Mode="In" Type="Boolean" />
            <Parameter Name="Address" Mode="In" Type="String" />
            <Parameter Name="IsSucessful" Mode="InOut" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_About_Us" ReturnType="Collection(String)" />
          <FunctionImport Name="usp_Get_Car_Category_Details" ReturnType="Collection(RLTCARMODEL.usp_Get_Car_Category_Details_Result)" />
          <FunctionImport Name="usp_Get_Cities" ReturnType="Collection(RLTCARMODEL.usp_Get_Cities_Result)" />
          <FunctionImport Name="usp_Get_Comapny_Info" ReturnType="Collection(RLTCARMODEL.usp_Get_Comapny_Info_Result)" />
          <FunctionImport Name="usp_Get_Discount_Coupon_Details" ReturnType="Collection(RLTCARMODEL.usp_Get_Discount_Coupon_Details_Result)">
          <Parameter Name="CouponCode" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_Driver_Booking_info" ReturnType="Collection(RLTCARMODEL.usp_Get_Driver_Booking_info_Result)">
            <Parameter Name="Vendor_Id" Mode="In" Type="String" />
            <Parameter Name="statusid" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_Driver_Login_and_info" ReturnType="Collection(Int32)">
            <Parameter Name="emailid" Mode="In" Type="String" />
            <Parameter Name="mobileno" Mode="In" Type="String" />
            <Parameter Name="password" Mode="In" Type="String" />
            <Parameter Name="flag" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_FAQ" ReturnType="Collection(String)" />
          <FunctionImport Name="usp_Get_Privacy_Policy" ReturnType="Collection(String)" />
          <FunctionImport Name="usp_Get_Services" ReturnType="Collection(RLTCARMODEL.usp_Get_Services_Result)" />
          <FunctionImport Name="usp_Get_Template_Master" ReturnType="Collection(RLTCARMODEL.usp_Get_Template_Master_Result)">
            <Parameter Name="TemplateName" Mode="In" Type="String" />
            <Parameter Name="TemplateType" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_User_Details" ReturnType="Collection(RLTCARMODEL.usp_Get_User_Details_Result)">
          <Parameter Name="UserID" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_Get_UserLogin_Details" ReturnType="Collection(RLTCARMODEL.usp_Get_UserLogin_Details_Result)">
            <Parameter Name="UserEmailId" Mode="In" Type="String" />
            <Parameter Name="UserMobileNumber" Mode="In" Type="String" />
            <Parameter Name="UserPassword" Mode="In" Type="String" />
            <Parameter Name="IsOTPLogin" Mode="In" Type="Boolean" />
            <Parameter Name="ID" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="usp_update_otp">
            <Parameter Name="UserType" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="OTP" Mode="In" Type="Int32" />
            <Parameter Name="IsSucessful" Mode="InOut" Type="Boolean" />
          </FunctionImport>
          <FunctionImport Name="usp_UserLogin_Execute">
            <Parameter Name="UserEmailId" Mode="In" Type="String" />
            <Parameter Name="UserMobileNumber" Mode="In" Type="String" />
            <Parameter Name="UserPassword" Mode="In" Type="String" />
            <Parameter Name="IsOTPLogin" Mode="In" Type="Boolean" />
            <Parameter Name="IsSucessful" Mode="InOut" Type="Int32" />
          </FunctionImport>
        </EntityContainer>
        <ComplexType Name="Proc_GetUserDtl_Result">
          <Property Type="Int32" Name="PKID" Nullable="false" />
          <Property Type="Int32" Name="RoleId" Nullable="true" />
          <Property Type="String" Name="UserName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="UserPWD" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="UserPhoto" Nullable="true" MaxLength="500" />
          <Property Type="String" Name="UserFirstName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="UserMiddleName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="UserLastName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="UserEmailID" Nullable="true" MaxLength="100" />
          <Property Type="String" Name="UserMobileNo" Nullable="true" MaxLength="20" />
          <Property Type="Int32" Name="VendorId" Nullable="true" />
          <Property Type="Int32" Name="CarOwnerId" Nullable="true" />
          <Property Type="Int32" Name="EmployeeId" Nullable="true" />
          <Property Type="String" Name="AadharId" Nullable="true" MaxLength="50" />
          <Property Type="Int32" Name="DepartmentId" Nullable="true" />
          <Property Type="String" Name="Address" Nullable="true" MaxLength="500" />
          <Property Type="DateTime" Name="CreatedDate" Nullable="true" Precision="23" />
          <Property Type="Boolean" Name="IsActive" Nullable="true" />
          <Property Type="Int32" Name="IsAdmin" Nullable="true" />
          <Property Type="Boolean" Name="IsDeleted" Nullable="true" />
          <Property Type="String" Name="Description" Nullable="true" />
          <Property Type="Boolean" Name="Is2TfaAuthentication" Nullable="true" />
          <Property Type="Guid" Name="ID" Nullable="true" />
          <Property Type="DateTime" Name="UpdatedDate" Nullable="true" Precision="23" />
          <Property Type="Int32" Name="UpdatedBy" Nullable="true" />
          <Property Type="Int32" Name="CreatedBy" Nullable="true" />
        </ComplexType>
        <ComplexType Name="SP_GET_REPORT_DATA_Result">
          <Property Type="String" Name="Booking_Id" Nullable="true" MaxLength="20" />
          <Property Type="String" Name="City_From" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="City_To" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="Trip_Type" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Car_Category" Nullable="true" MaxLength="60" />
          <Property Type="Decimal" Name="Fare" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Decimal" Name="GST_Fare" Nullable="true" Precision="18" Scale="2" />
          <Property Type="String" Name="Booking_Date" Nullable="true" MaxLength="15" />
          <Property Type="String" Name="PickUp_Address" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="DropOff_Address" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="PickUp_Date" Nullable="true" MaxLength="15" />
          <Property Type="String" Name="PickUp_Time" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Name" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Mobile_No1" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Mobile_No2" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Mail_Id" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Payment_Method_Name" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Vendor_Company_Name" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Car_Number" Nullable="true" MaxLength="20" />
          <Property Type="String" Name="BOOKING_STATUS" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Driver_Name" Nullable="true" MaxLength="75" />
          <Property Type="String" Name="Booking_Remark" Nullable="true" MaxLength="500" />
        </ComplexType>
        <ComplexType Name="usp_Get_Car_Category_Details_Result">
          <Property Type="String" Name="Car_Category_Abbr" Nullable="true" MaxLength="60" />
          <Property Type="Double" Name="Per_KM_fare" Nullable="true" />
          <Property Type="Int32" Name="Capacity" Nullable="true" />
          <Property Type="String" Name="Features" Nullable="true" MaxLength="500" />
          <Property Type="Int32" Name="PKID" Nullable="false" />
          <Property Type="String" Name="Car_Categroy_Image" Nullable="true" MaxLength="150" />
        </ComplexType>
        <ComplexType Name="usp_Get_Cities_Result">
          <Property Type="String" Name="City_Name" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="longitude" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="latitude" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="eLoc" Nullable="true" MaxLength="50" />
        </ComplexType>
        <ComplexType Name="usp_Get_Comapny_Info_Result">
          <Property Type="Int32" Name="PKID" Nullable="false" />
          <Property Type="String" Name="Address" Nullable="true" MaxLength="350" />
          <Property Type="String" Name="SalesTelNo" Nullable="true" MaxLength="170" />
          <Property Type="String" Name="BusinessEmailNo" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="WorkingHours" Nullable="true" MaxLength="250" />
          <Property Type="String" Name="GPSCordinationLatitude" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="GPSCordinationLongitude" Nullable="true" MaxLength="50" />
          <Property Type="Boolean" Name="isActive" Nullable="true" />
          <Property Type="DateTime" Name="CreateDate" Nullable="true" Precision="23" />
          <Property Type="String" Name="SalesEmailNo" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="BusinessTelNo" Nullable="true" MaxLength="170" />
          <Property Type="String" Name="GoogleMapLink" Nullable="true" />
          <Property Type="String" Name="FBLink" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="TwitterLink" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="InstagramLink" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="PinsterLink" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="GooglePlusLink" Nullable="true" MaxLength="200" />
          <Property Type="DateTime" Name="UpdatedDate" Nullable="true" Precision="23" />
          <Property Type="Int32" Name="CreatedBy" Nullable="true" />
          <Property Type="Int32" Name="UpdatedBy" Nullable="true" />
        </ComplexType>
        <ComplexType Name="usp_Get_Discount_Coupon_Details_Result">
          <Property Type="String" Name="Discount_Coupon" Nullable="true" MaxLength="50" />
          <Property Type="Decimal" Name="Discount" Nullable="true" Precision="18" Scale="2" />
          <Property Type="String" Name="Coupon_Name" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Coupon_Remark" Nullable="true" MaxLength="500" />
          <Property Type="Int32" Name="Max_Discount" Nullable="true" />
          <Property Type="Int32" Name="Fare_When_Applied" Nullable="true" />
          <Property Type="DateTime" Name="Coupon_Last_Date" Nullable="true" Precision="23" />
        </ComplexType>
        <ComplexType Name="usp_Get_Driver_Booking_info_Result">
          <Property Type="String" Name="Booking_Id" Nullable="true" MaxLength="20" />
          <Property Type="Decimal" Name="Fare" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Decimal" Name="Distance" Nullable="true" Precision="18" Scale="2" />
          <Property Type="String" Name="from_Destination" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="To_Destination" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="Car_Category_Name" Nullable="true" MaxLength="30" />
          <Property Type="String" Name="BOOKING_STATUS" Nullable="true" MaxLength="50" />
          <Property Type="Int32" Name="statusid" Nullable="false" />
          <Property Type="String" Name="Car_Model_Name" Nullable="true" />
        </ComplexType>
        <ComplexType Name="usp_Get_Services_Result">
          <Property Type="Int32" Name="PKID" Nullable="false" />
          <Property Type="String" Name="ServiceName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="ServiceIconClass" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="ServiceImagePath" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="BriefIntroAboutService" Nullable="true" MaxLength="200" />
          <Property Type="String" Name="AboutService" Nullable="true" />
          <Property Type="Boolean" Name="IsActive" Nullable="true" />
          <Property Type="DateTime" Name="CreatedDate" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="UpdatedDate" Nullable="true" Precision="23" />
          <Property Type="Int32" Name="CreatedBy" Nullable="true" />
          <Property Type="Int32" Name="UpdatedBy" Nullable="true" />
        </ComplexType>
        <ComplexType Name="usp_Get_Template_Master_Result">
          <Property Type="String" Name="Message_Body" Nullable="true" />
          <Property Type="String" Name="Template_Name" Nullable="true" MaxLength="50" />
        </ComplexType>
        <ComplexType Name="usp_Get_User_Details_Result">
          <Property Type="Int64" Name="PKID" Nullable="false" />
          <Property Type="String" Name="RLT_User_Email" Nullable="true" MaxLength="50" />
          <Property Type="DateTime" Name="RLT_User_DOB" Nullable="true" Precision="23" />
          <Property Type="String" Name="RLT_User_Phone" Nullable="true" MaxLength="15" />
          <Property Type="String" Name="RLT_User_Gender" Nullable="true" MaxLength="10" />
          <Property Type="DateTime" Name="Last_Login_Date_Time" Nullable="true" Precision="23" />
          <Property Type="String" Name="RLT_User_Name" Nullable="true" MaxLength="50" />
        </ComplexType>
        <ComplexType Name="usp_Get_UserLogin_Details_Result">
          <Property Type="Int64" Name="PKID" Nullable="false" />
          <Property Type="String" Name="RLT_User_Email" Nullable="true" MaxLength="50" />
          <Property Type="DateTime" Name="RLT_User_DOB" Nullable="true" Precision="23" />
          <Property Type="String" Name="RLT_User_Phone" Nullable="true" MaxLength="15" />
          <Property Type="String" Name="RLT_User_Gender" Nullable="true" MaxLength="10" />
          <Property Type="DateTime" Name="Last_Login_Date_Time" Nullable="true" Precision="23" />
          <Property Type="String" Name="RLT_User_Name" Nullable="true" MaxLength="50" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="RLTCARMODELStoreContainer" CdmEntityContainer="RLTDBContext">
          <EntitySetMapping Name="C__EFMigrationsHistory">
            <EntityTypeMapping TypeName="RLTCARMODEL.C__EFMigrationsHistory">
              <MappingFragment StoreEntitySet="__EFMigrationsHistory">
                <ScalarProperty Name="MigrationId" ColumnName="MigrationId" />
                <ScalarProperty Name="ProductVersion" ColumnName="ProductVersion" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="C__EFMigrationsHistory1">
            <EntityTypeMapping TypeName="RLTCARMODEL.C__EFMigrationsHistory1">
              <MappingFragment StoreEntitySet="C__EFMigrationsHistory">
                <ScalarProperty Name="MigrationId" ColumnName="MigrationId" />
                <ScalarProperty Name="ProductVersion" ColumnName="ProductVersion" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Products">
            <EntityTypeMapping TypeName="RLTCARMODEL.Product">
              <MappingFragment StoreEntitySet="Products">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Created" ColumnName="Created" />
                <ScalarProperty Name="LastModifiedBy" ColumnName="LastModifiedBy" />
                <ScalarProperty Name="LastModified" ColumnName="LastModified" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Barcode" ColumnName="Barcode" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="Rate" ColumnName="Rate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_AboutUs">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_AboutUs">
              <MappingFragment StoreEntitySet="RLT_AboutUs">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="AboutUsDetails" ColumnName="AboutUsDetails" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ADMIN_ROLE">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ADMIN_ROLE">
              <MappingFragment StoreEntitySet="RLT_ADMIN_ROLE">
                <ScalarProperty Name="RoleId" ColumnName="RoleId" />
                <ScalarProperty Name="Role" ColumnName="Role" />
                <ScalarProperty Name="Role_Description" ColumnName="Role_Description" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ADMIN_USER">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ADMIN_USER">
              <MappingFragment StoreEntitySet="RLT_ADMIN_USER">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="RoleId" ColumnName="RoleId" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="UserPWD" ColumnName="UserPWD" />
                <ScalarProperty Name="UserPhoto" ColumnName="UserPhoto" />
                <ScalarProperty Name="UserFirstName" ColumnName="UserFirstName" />
                <ScalarProperty Name="UserMiddleName" ColumnName="UserMiddleName" />
                <ScalarProperty Name="UserLastName" ColumnName="UserLastName" />
                <ScalarProperty Name="UserEmailID" ColumnName="UserEmailID" />
                <ScalarProperty Name="UserMobileNo" ColumnName="UserMobileNo" />
                <ScalarProperty Name="VendorId" ColumnName="VendorId" />
                <ScalarProperty Name="CarOwnerId" ColumnName="CarOwnerId" />
                <ScalarProperty Name="EmployeeId" ColumnName="EmployeeId" />
                <ScalarProperty Name="AadharId" ColumnName="AadharId" />
                <ScalarProperty Name="DepartmentId" ColumnName="DepartmentId" />
                <ScalarProperty Name="Address" ColumnName="Address" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsAdmin" ColumnName="IsAdmin" />
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="Is2TfaAuthentication" ColumnName="Is2TfaAuthentication" />
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Admin_User_Info">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Admin_User_Info">
              <MappingFragment StoreEntitySet="RLT_Admin_User_Info">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Employee_Name" ColumnName="Employee_Name" />
                <ScalarProperty Name="Photo" ColumnName="Photo" />
                <ScalarProperty Name="DOB" ColumnName="DOB" />
                <ScalarProperty Name="Phone_1" ColumnName="Phone_1" />
                <ScalarProperty Name="Phone_2" ColumnName="Phone_2" />
                <ScalarProperty Name="Phone_1_IsWhatsup" ColumnName="Phone_1_IsWhatsup" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="Marrital_Status" ColumnName="Marrital_Status" />
                <ScalarProperty Name="Spouses_Name" ColumnName="Spouses_Name" />
                <ScalarProperty Name="Father_Name" ColumnName="Father_Name" />
                <ScalarProperty Name="Mother_Name" ColumnName="Mother_Name" />
                <ScalarProperty Name="Current_Permanent_Same" ColumnName="Current_Permanent_Same" />
                <ScalarProperty Name="Current_Address" ColumnName="Current_Address" />
                <ScalarProperty Name="Permanent_Address" ColumnName="Permanent_Address" />
                <ScalarProperty Name="Permanent_Address1" ColumnName="Permanent_Address1" />
                <ScalarProperty Name="Permanent_Address2" ColumnName="Permanent_Address2" />
                <ScalarProperty Name="Permanent_Address_State" ColumnName="Permanent_Address_State" />
                <ScalarProperty Name="Permanent_Address_City" ColumnName="Permanent_Address_City" />
                <ScalarProperty Name="Current_Address1" ColumnName="Current_Address1" />
                <ScalarProperty Name="Current_Address2" ColumnName="Current_Address2" />
                <ScalarProperty Name="Current_Address_State" ColumnName="Current_Address_State" />
                <ScalarProperty Name="Current_Address_City" ColumnName="Current_Address_City" />
                <ScalarProperty Name="C10th_School" ColumnName="C10th_School" />
                <ScalarProperty Name="C10th_PassingYear" ColumnName="C10th_PassingYear" />
                <ScalarProperty Name="C10th_Percentage" ColumnName="C10th_Percentage" />
                <ScalarProperty Name="C12th_School" ColumnName="C12th_School" />
                <ScalarProperty Name="C12th_PassingYear" ColumnName="C12th_PassingYear" />
                <ScalarProperty Name="C12th_Percentage" ColumnName="C12th_Percentage" />
                <ScalarProperty Name="Degree_Name" ColumnName="Degree_Name" />
                <ScalarProperty Name="Degree_College" ColumnName="Degree_College" />
                <ScalarProperty Name="Degree_PassingYear" ColumnName="Degree_PassingYear" />
                <ScalarProperty Name="Degree_Percentage" ColumnName="Degree_Percentage" />
                <ScalarProperty Name="Master_Degree_Name" ColumnName="Master_Degree_Name" />
                <ScalarProperty Name="Master_Degree_College" ColumnName="Master_Degree_College" />
                <ScalarProperty Name="Master_Degree_PassingYear" ColumnName="Master_Degree_PassingYear" />
                <ScalarProperty Name="Master_Degree_Percentage" ColumnName="Master_Degree_Percentage" />
                <ScalarProperty Name="Employee_ID" ColumnName="Employee_ID" />
                <ScalarProperty Name="Department" ColumnName="Department" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Supervisor" ColumnName="Supervisor" />
                <ScalarProperty Name="Work_City" ColumnName="Work_City" />
                <ScalarProperty Name="Work_Phone" ColumnName="Work_Phone" />
                <ScalarProperty Name="Work_Email" ColumnName="Work_Email" />
                <ScalarProperty Name="Work_StartDate" ColumnName="Work_StartDate" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BANK_NAMES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BANK_NAMES">
              <MappingFragment StoreEntitySet="RLT_BANK_NAMES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Bank_Name" ColumnName="Bank_Name" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING">
              <MappingFragment StoreEntitySet="RLT_BOOKING">
                <ScalarProperty Name="Booking_Created_By" ColumnName="Booking_Created_By" />
                <ScalarProperty Name="RemainingAmountForDriver" ColumnName="RemainingAmountForDriver" />
                <ScalarProperty Name="PartialPaymentAmount" ColumnName="PartialPaymentAmount" />
                <ScalarProperty Name="PaymentType" ColumnName="PaymentType" />
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Booking_Id" ColumnName="Booking_Id" />
                <ScalarProperty Name="City_From_Id" ColumnName="City_From_Id" />
                <ScalarProperty Name="City_To_Id" ColumnName="City_To_Id" />
                <ScalarProperty Name="Trip_Type_Id" ColumnName="Trip_Type_Id" />
                <ScalarProperty Name="Car_Category_Id" ColumnName="Car_Category_Id" />
                <ScalarProperty Name="Duration" ColumnName="Duration" />
                <ScalarProperty Name="Distance" ColumnName="Distance" />
                <ScalarProperty Name="Basic_Fare" ColumnName="Basic_Fare" />
                <ScalarProperty Name="Driver_Charge" ColumnName="Driver_Charge" />
                <ScalarProperty Name="Toll_Charge" ColumnName="Toll_Charge" />
                <ScalarProperty Name="GST" ColumnName="GST" />
                <ScalarProperty Name="Fare" ColumnName="Fare" />
                <ScalarProperty Name="GST_Fare" ColumnName="GST_Fare" />
                <ScalarProperty Name="Coupon_Code" ColumnName="Coupon_Code" />
                <ScalarProperty Name="Coupon_Discount" ColumnName="Coupon_Discount" />
                <ScalarProperty Name="Booking_Date" ColumnName="Booking_Date" />
                <ScalarProperty Name="PickUp_Address" ColumnName="PickUp_Address" />
                <ScalarProperty Name="DropOff_Address" ColumnName="DropOff_Address" />
                <ScalarProperty Name="PickUp_Date" ColumnName="PickUp_Date" />
                <ScalarProperty Name="PickUp_Time" ColumnName="PickUp_Time" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Mobile_No1" ColumnName="Mobile_No1" />
                <ScalarProperty Name="Mobile_No2" ColumnName="Mobile_No2" />
                <ScalarProperty Name="Mail_Id" ColumnName="Mail_Id" />
                <ScalarProperty Name="Mode_Of_Payment_Id" ColumnName="Mode_Of_Payment_Id" />
                <ScalarProperty Name="Vendor_Id" ColumnName="Vendor_Id" />
                <ScalarProperty Name="Car_Id" ColumnName="Car_Id" />
                <ScalarProperty Name="Booking_Status_Id" ColumnName="Booking_Status_Id" />
                <ScalarProperty Name="Booking_Remark" ColumnName="Booking_Remark" />
                <ScalarProperty Name="Invoice_No" ColumnName="Invoice_No" />
                <ScalarProperty Name="Invoice_Date" ColumnName="Invoice_Date" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
                <ScalarProperty Name="razorpay_payment_id" ColumnName="razorpay_payment_id" />
                <ScalarProperty Name="razorpay_order_id" ColumnName="razorpay_order_id" />
                <ScalarProperty Name="razorpay_signature" ColumnName="razorpay_signature" />
                <ScalarProperty Name="razorpay_status" ColumnName="razorpay_status" />
                <ScalarProperty Name="payment_link" ColumnName="payment_link" />
                <ScalarProperty Name="PickUpAddressLatitude" ColumnName="PickUpAddressLatitude" />
                <ScalarProperty Name="PickUpAddressLongitude" ColumnName="PickUpAddressLongitude" />
                <ScalarProperty Name="BookingEditRemark" ColumnName="BookingEditRemark" />
                <ScalarProperty Name="driver_id" ColumnName="driver_id" />
                <ScalarProperty Name="completepickupaddress" ColumnName="completepickupaddress" />
                <ScalarProperty Name="completedropoffpaddress" ColumnName="completedropoffpaddress" />
                <ScalarProperty Name="IsFullOnlinePayment" ColumnName="IsFullOnlinePayment" />
                <ScalarProperty Name="CashAmountToPayDriver" ColumnName="CashAmountToPayDriver" />
                <ScalarProperty Name="TollCharge" ColumnName="TollCharge" />
                <ScalarProperty Name="PaymentOption" ColumnName="PaymentOption" />
                <ScalarProperty Name="DropOffDate" ColumnName="DropOffDate" />
                <ScalarProperty Name="DropOffTime" ColumnName="DropOffTime" />
                <ScalarProperty Name="PaymentDate" ColumnName="PaymentDate" />
                <ScalarProperty Name="BookedBy" ColumnName="BookedBy" />
                <ScalarProperty Name="IsAdminBooked" ColumnName="IsAdminBooked" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_FARE">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_FARE">
              <MappingFragment StoreEntitySet="RLT_BOOKING_FARE">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="City_From" ColumnName="City_From" />
                <ScalarProperty Name="City_To" ColumnName="City_To" />
                <ScalarProperty Name="Trip_Type_Id" ColumnName="Trip_Type_Id" />
                <ScalarProperty Name="Car_Category_Id" ColumnName="Car_Category_Id" />
                <ScalarProperty Name="Basic_Fare" ColumnName="Basic_Fare" />
                <ScalarProperty Name="Driver_Charge" ColumnName="Driver_Charge" />
                <ScalarProperty Name="Toll_Charge" ColumnName="Toll_Charge" />
                <ScalarProperty Name="Total_Fare" ColumnName="Total_Fare" />
                <ScalarProperty Name="GST" ColumnName="GST" />
                <ScalarProperty Name="GST_Amount" ColumnName="GST_Amount" />
                <ScalarProperty Name="Final_Fare" ColumnName="Final_Fare" />
                <ScalarProperty Name="Remark" ColumnName="Remark" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_GST">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_GST">
              <MappingFragment StoreEntitySet="RLT_BOOKING_GST">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="GST" ColumnName="GST" />
                <ScalarProperty Name="SGST" ColumnName="SGST" />
                <ScalarProperty Name="CGST" ColumnName="CGST" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_MODE">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_MODE">
              <MappingFragment StoreEntitySet="RLT_BOOKING_MODE">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Booking_Mode_Name" ColumnName="Booking_Mode_Name" />
                <ScalarProperty Name="Counrty_Abbr" ColumnName="Counrty_Abbr" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_PAYMENT_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_PAYMENT_DETAILS">
              <MappingFragment StoreEntitySet="RLT_BOOKING_PAYMENT_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Passenger_Name" ColumnName="Passenger_Name" />
                <ScalarProperty Name="Passenger_Email" ColumnName="Passenger_Email" />
                <ScalarProperty Name="Passenger_Phone" ColumnName="Passenger_Phone" />
                <ScalarProperty Name="Passenger_Alt_Phone" ColumnName="Passenger_Alt_Phone" />
                <ScalarProperty Name="Passenger_Special_Request_Comment" ColumnName="Passenger_Special_Request_Comment" />
                <ScalarProperty Name="Passenger_Trip_Purpose" ColumnName="Passenger_Trip_Purpose" />
                <ScalarProperty Name="Passenger_Trip_Type_PKID" ColumnName="Passenger_Trip_Type_PKID" />
                <ScalarProperty Name="Passenger_Pickup_City_PKID" ColumnName="Passenger_Pickup_City_PKID" />
                <ScalarProperty Name="Passenger_Pickup_Address" ColumnName="Passenger_Pickup_Address" />
                <ScalarProperty Name="Passenger_Dropoff_City_PKID" ColumnName="Passenger_Dropoff_City_PKID" />
                <ScalarProperty Name="Passenger_Dropoff_Address" ColumnName="Passenger_Dropoff_Address" />
                <ScalarProperty Name="Booking_Login_PKID" ColumnName="Booking_Login_PKID" />
                <ScalarProperty Name="Base_Fare" ColumnName="Base_Fare" />
                <ScalarProperty Name="Driver_Allowance" ColumnName="Driver_Allowance" />
                <ScalarProperty Name="Service_Tax" ColumnName="Service_Tax" />
                <ScalarProperty Name="Discount" ColumnName="Discount" />
                <ScalarProperty Name="Discount_Coupon" ColumnName="Discount_Coupon" />
                <ScalarProperty Name="Total_Booking_Amount" ColumnName="Total_Booking_Amount" />
                <ScalarProperty Name="Payment_Mode_PKID" ColumnName="Payment_Mode_PKID" />
                <ScalarProperty Name="Payment_Amount_Recieved" ColumnName="Payment_Amount_Recieved" />
                <ScalarProperty Name="Payment_Need_to_Collect" ColumnName="Payment_Need_to_Collect" />
                <ScalarProperty Name="Payment_Issue_Comment" ColumnName="Payment_Issue_Comment" />
                <ScalarProperty Name="Payment_TransactionId" ColumnName="Payment_TransactionId" />
                <ScalarProperty Name="Payment_Transaction_Status" ColumnName="Payment_Transaction_Status" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_RULES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_RULES">
              <MappingFragment StoreEntitySet="RLT_BOOKING_RULES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Trip_Type_Id" ColumnName="Trip_Type_Id" />
                <ScalarProperty Name="Car_Category_Id" ColumnName="Car_Category_Id" />
                <ScalarProperty Name="Distance_From" ColumnName="Distance_From" />
                <ScalarProperty Name="Distance_To" ColumnName="Distance_To" />
                <ScalarProperty Name="NewDistance" ColumnName="NewDistance" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKING_STATUS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKING_STATUS">
              <MappingFragment StoreEntitySet="RLT_BOOKING_STATUS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="BOOKING_STATUS" ColumnName="BOOKING_STATUS" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Modified_By" ColumnName="Modified_By" />
                <ScalarProperty Name="Modified_Date" ColumnName="Modified_Date" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_BOOKINGS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_BOOKINGS">
              <MappingFragment StoreEntitySet="RLT_BOOKINGS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Booking_Id" ColumnName="Booking_Id" />
                <ScalarProperty Name="City_From_Id" ColumnName="City_From_Id" />
                <ScalarProperty Name="City_To_Id" ColumnName="City_To_Id" />
                <ScalarProperty Name="PickUp_Address" ColumnName="PickUp_Address" />
                <ScalarProperty Name="DropOff_Address" ColumnName="DropOff_Address" />
                <ScalarProperty Name="PickUpLatLong" ColumnName="PickUpLatLong" />
                <ScalarProperty Name="DropOffLatLong" ColumnName="DropOffLatLong" />
                <ScalarProperty Name="TravelerName" ColumnName="TravelerName" />
                <ScalarProperty Name="TravelerEmail" ColumnName="TravelerEmail" />
                <ScalarProperty Name="TravelerPhone" ColumnName="TravelerPhone" />
                <ScalarProperty Name="BookingSessionID" ColumnName="BookingSessionID" />
                <ScalarProperty Name="PaymentOption" ColumnName="PaymentOption" />
                <ScalarProperty Name="PaymentMode" ColumnName="PaymentMode" />
                <ScalarProperty Name="IsWhatsAppNumber" ColumnName="IsWhatsAppNumber" />
                <ScalarProperty Name="CashToPayDriver" ColumnName="CashToPayDriver" />
                <ScalarProperty Name="DriverNightCharge" ColumnName="DriverNightCharge" />
                <ScalarProperty Name="Trip_Type_Id" ColumnName="Trip_Type_Id" />
                <ScalarProperty Name="Car_Category_Id" ColumnName="Car_Category_Id" />
                <ScalarProperty Name="Duration" ColumnName="Duration" />
                <ScalarProperty Name="Distance" ColumnName="Distance" />
                <ScalarProperty Name="Basic_Fare" ColumnName="Basic_Fare" />
                <ScalarProperty Name="Driver_Charge" ColumnName="Driver_Charge" />
                <ScalarProperty Name="Toll_Charge" ColumnName="Toll_Charge" />
                <ScalarProperty Name="GST" ColumnName="GST" />
                <ScalarProperty Name="Fare" ColumnName="Fare" />
                <ScalarProperty Name="GST_Fare" ColumnName="GST_Fare" />
                <ScalarProperty Name="Coupon_Code" ColumnName="Coupon_Code" />
                <ScalarProperty Name="Coupon_Discount" ColumnName="Coupon_Discount" />
                <ScalarProperty Name="Booking_Date" ColumnName="Booking_Date" />
                <ScalarProperty Name="PickUp_Date" ColumnName="PickUp_Date" />
                <ScalarProperty Name="PickUp_Time" ColumnName="PickUp_Time" />
                <ScalarProperty Name="Vendor_Id" ColumnName="Vendor_Id" />
                <ScalarProperty Name="Car_Id" ColumnName="Car_Id" />
                <ScalarProperty Name="Booking_Status_Id" ColumnName="Booking_Status_Id" />
                <ScalarProperty Name="Booking_Remark" ColumnName="Booking_Remark" />
                <ScalarProperty Name="Invoice_No" ColumnName="Invoice_No" />
                <ScalarProperty Name="Invoice_Date" ColumnName="Invoice_Date" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
                <ScalarProperty Name="razorpay_payment_id" ColumnName="razorpay_payment_id" />
                <ScalarProperty Name="razorpay_order_id" ColumnName="razorpay_order_id" />
                <ScalarProperty Name="razorpay_signature" ColumnName="razorpay_signature" />
                <ScalarProperty Name="razorpay_status" ColumnName="razorpay_status" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_CATEGORY">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_CATEGORY">
              <MappingFragment StoreEntitySet="RLT_CAR_CATEGORY">
                <ScalarProperty Name="Base_Fare" ColumnName="Base_Fare" />
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Car_Category_Name" ColumnName="Car_Category_Name" />
                <ScalarProperty Name="Car_Category_Abbr" ColumnName="Car_Category_Abbr" />
                <ScalarProperty Name="Per_KM_fare" ColumnName="Per_KM_fare" />
                <ScalarProperty Name="Capacity" ColumnName="Capacity" />
                <ScalarProperty Name="Features" ColumnName="Features" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Car_Categroy_Image" ColumnName="Car_Categroy_Image" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_CHARGES_FECILITIES_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_CHARGES_FECILITIES_DETAILS">
              <MappingFragment StoreEntitySet="RLT_CAR_CHARGES_FECILITIES_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Car_Category_PKID" ColumnName="Car_Category_PKID" />
                <ScalarProperty Name="Car_Charges_PKM" ColumnName="Car_Charges_PKM" />
                <ScalarProperty Name="Car_Waiting_Charges_PM" ColumnName="Car_Waiting_Charges_PM" />
                <ScalarProperty Name="Car_Driver_Charges_PD" ColumnName="Car_Driver_Charges_PD" />
                <ScalarProperty Name="Is_Contain_AC" ColumnName="Is_Contain_AC" />
                <ScalarProperty Name="Total_Number_Seats" ColumnName="Total_Number_Seats" />
                <ScalarProperty Name="Is_Smoking_Allow" ColumnName="Is_Smoking_Allow" />
                <ScalarProperty Name="Is_Luggage_Allow" ColumnName="Is_Luggage_Allow" />
                <ScalarProperty Name="Is_Pet_Allow" ColumnName="Is_Pet_Allow" />
                <ScalarProperty Name="Inclusive_Charges_Comment" ColumnName="Inclusive_Charges_Comment" />
                <ScalarProperty Name="Extras_Charges_Comment" ColumnName="Extras_Charges_Comment" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_COMPANY">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_COMPANY">
              <MappingFragment StoreEntitySet="RLT_CAR_COMPANY">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Company_Name" ColumnName="Company_Name" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_DETAILS">
              <MappingFragment StoreEntitySet="RLT_CAR_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Vendor_PKID" ColumnName="Vendor_PKID" />
                <ScalarProperty Name="Car_Owner_PKID" ColumnName="Car_Owner_PKID" />
                <ScalarProperty Name="Car_Number" ColumnName="Car_Number" />
                <ScalarProperty Name="Car_Purchase_Year" ColumnName="Car_Purchase_Year" />
                <ScalarProperty Name="Car_Manufacturing_Year" ColumnName="Car_Manufacturing_Year" />
                <ScalarProperty Name="Car_Company_PKID" ColumnName="Car_Company_PKID" />
                <ScalarProperty Name="Car_Model_PKID" ColumnName="Car_Model_PKID" />
                <ScalarProperty Name="Car_Registered_Document" ColumnName="Car_Registered_Document" />
                <ScalarProperty Name="Car_Registered_City_PKID" ColumnName="Car_Registered_City_PKID" />
                <ScalarProperty Name="Car_Fuel_Type_Status" ColumnName="Car_Fuel_Type_Status" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Update_Date" ColumnName="Update_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_DOCS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_DOCS">
              <MappingFragment StoreEntitySet="RLT_CAR_DOCS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Car_PKID" ColumnName="Car_PKID" />
                <ScalarProperty Name="Car_Doc_Id" ColumnName="Car_Doc_Id" />
                <ScalarProperty Name="Car_Doc_EndDate" ColumnName="Car_Doc_EndDate" />
                <ScalarProperty Name="Document_Path" ColumnName="Document_Path" />
                <ScalarProperty Name="Is_Verified" ColumnName="Is_Verified" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Last_Modified_Date" ColumnName="Last_Modified_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_DRIVER_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_DRIVER_DETAILS">
              <MappingFragment StoreEntitySet="RLT_CAR_DRIVER_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Vendor_PKID" ColumnName="Vendor_PKID" />
                <ScalarProperty Name="Car_Owner_PKID" ColumnName="Car_Owner_PKID" />
                <ScalarProperty Name="Car_PKID" ColumnName="Car_PKID" />
                <ScalarProperty Name="Driver_Name" ColumnName="Driver_Name" />
                <ScalarProperty Name="Driver_Photo" ColumnName="Driver_Photo" />
                <ScalarProperty Name="Driver_DOB" ColumnName="Driver_DOB" />
                <ScalarProperty Name="Driver_FName" ColumnName="Driver_FName" />
                <ScalarProperty Name="Driver_MName" ColumnName="Driver_MName" />
                <ScalarProperty Name="Driver_LName" ColumnName="Driver_LName" />
                <ScalarProperty Name="Drive_Phone_1" ColumnName="Drive_Phone_1" />
                <ScalarProperty Name="Drive_Phone_2" ColumnName="Drive_Phone_2" />
                <ScalarProperty Name="Phone_1_IsWhatsup" ColumnName="Phone_1_IsWhatsup" />
                <ScalarProperty Name="Driver_Email" ColumnName="Driver_Email" />
                <ScalarProperty Name="Driver_DL" ColumnName="Driver_DL" />
                <ScalarProperty Name="Driver_Aadhaar" ColumnName="Driver_Aadhaar" />
                <ScalarProperty Name="Driver_Father_Name" ColumnName="Driver_Father_Name" />
                <ScalarProperty Name="Driver_Mother_Name" ColumnName="Driver_Mother_Name" />
                <ScalarProperty Name="Driver_Marrital_Status" ColumnName="Driver_Marrital_Status" />
                <ScalarProperty Name="Driver_Gender" ColumnName="Driver_Gender" />
                <ScalarProperty Name="Driver_Current_Permanent_Same" ColumnName="Driver_Current_Permanent_Same" />
                <ScalarProperty Name="Driver_Current_Address" ColumnName="Driver_Current_Address" />
                <ScalarProperty Name="Driver_Permanent_Address" ColumnName="Driver_Permanent_Address" />
                <ScalarProperty Name="Driver_Permanent_Address1" ColumnName="Driver_Permanent_Address1" />
                <ScalarProperty Name="Driver_Permanent_Address2" ColumnName="Driver_Permanent_Address2" />
                <ScalarProperty Name="Driver_Permanent_Address_State" ColumnName="Driver_Permanent_Address_State" />
                <ScalarProperty Name="Driver_Permanent_Address_City" ColumnName="Driver_Permanent_Address_City" />
                <ScalarProperty Name="Driver_Current_Address1" ColumnName="Driver_Current_Address1" />
                <ScalarProperty Name="Driver_Current_Address2" ColumnName="Driver_Current_Address2" />
                <ScalarProperty Name="Driver_Current_Address_State" ColumnName="Driver_Current_Address_State" />
                <ScalarProperty Name="Driver_Current_Address_City" ColumnName="Driver_Current_Address_City" />
                <ScalarProperty Name="Driver_Smoking_Status" ColumnName="Driver_Smoking_Status" />
                <ScalarProperty Name="Driver_Drinking_Status" ColumnName="Driver_Drinking_Status" />
                <ScalarProperty Name="Driver_Eating_Type" ColumnName="Driver_Eating_Type" />
                <ScalarProperty Name="Driver_Religion" ColumnName="Driver_Religion" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
                <ScalarProperty Name="Will_Owner_Drive_Car_Status" ColumnName="Will_Owner_Drive_Car_Status" />
                <ScalarProperty Name="Driver_Pwd" ColumnName="Driver_Pwd" />
                <ScalarProperty Name="OTPUpdatedDateTime" ColumnName="OTPUpdatedDateTime" />
                <ScalarProperty Name="OTPNumber" ColumnName="OTPNumber" />
                <ScalarProperty Name="UserID" ColumnName="UserID" />
                <ScalarProperty Name="Special_Remarking" ColumnName="Special_Remarking" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_DRIVER_DOCS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_DRIVER_DOCS">
              <MappingFragment StoreEntitySet="RLT_CAR_DRIVER_DOCS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="CarDriver_PKID" ColumnName="CarDriver_PKID" />
                <ScalarProperty Name="CarDriver_Doc_Id" ColumnName="CarDriver_Doc_Id" />
                <ScalarProperty Name="CarDriver_Doc_EndDate" ColumnName="CarDriver_Doc_EndDate" />
                <ScalarProperty Name="Document_Path" ColumnName="Document_Path" />
                <ScalarProperty Name="Is_Verified" ColumnName="Is_Verified" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Last_Modified_Date" ColumnName="Last_Modified_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_FUEL_TYPES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_FUEL_TYPES">
              <MappingFragment StoreEntitySet="RLT_CAR_FUEL_TYPES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="CAR_FUEL_TYPE" ColumnName="CAR_FUEL_TYPE" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_MODEL">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_MODEL">
              <MappingFragment StoreEntitySet="RLT_CAR_MODEL">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Car_Model_Name" ColumnName="Car_Model_Name" />
                <ScalarProperty Name="Car_Category_PKID" ColumnName="Car_Category_PKID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Car_Fuel_Type_Status" ColumnName="Car_Fuel_Type_Status" />
                <ScalarProperty Name="Car_Company_PKID" ColumnName="Car_Company_PKID" />
                <ScalarProperty Name="Car_Segment_PKID" ColumnName="Car_Segment_PKID" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_OWNER_BANK_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_OWNER_BANK_DETAILS">
              <MappingFragment StoreEntitySet="RLT_CAR_OWNER_BANK_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Car_Owner_PKID" ColumnName="Car_Owner_PKID" />
                <ScalarProperty Name="Bank_Name_PKID" ColumnName="Bank_Name_PKID" />
                <ScalarProperty Name="Bank_IFSC_Code" ColumnName="Bank_IFSC_Code" />
                <ScalarProperty Name="Car_Owner_Bank_Acc_Name" ColumnName="Car_Owner_Bank_Acc_Name" />
                <ScalarProperty Name="Car_Owner_Bank_Acc_Number" ColumnName="Car_Owner_Bank_Acc_Number" />
                <ScalarProperty Name="Bank_Acc_Branch_Address" ColumnName="Bank_Acc_Branch_Address" />
                <ScalarProperty Name="Car_Owner_Bank_Registered_Phone" ColumnName="Car_Owner_Bank_Registered_Phone" />
                <ScalarProperty Name="Car_Owner_Bank_Cancelled_Chk_Photo" ColumnName="Car_Owner_Bank_Cancelled_Chk_Photo" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Creatde_Date" ColumnName="Creatde_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
                <ScalarProperty Name="Updater_Comment" ColumnName="Updater_Comment" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_OWNER_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_OWNER_DETAILS">
              <MappingFragment StoreEntitySet="RLT_CAR_OWNER_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Vendor_PKID" ColumnName="Vendor_PKID" />
                <ScalarProperty Name="Car_Owner_Name" ColumnName="Car_Owner_Name" />
                <ScalarProperty Name="Car_Owner_Photo" ColumnName="Car_Owner_Photo" />
                <ScalarProperty Name="Car_Owner_Phone1" ColumnName="Car_Owner_Phone1" />
                <ScalarProperty Name="Car_Owner_Phone2" ColumnName="Car_Owner_Phone2" />
                <ScalarProperty Name="Phone1_IsWhatsup" ColumnName="Phone1_IsWhatsup" />
                <ScalarProperty Name="Car_Owner_Email" ColumnName="Car_Owner_Email" />
                <ScalarProperty Name="Car_Owner_DL" ColumnName="Car_Owner_DL" />
                <ScalarProperty Name="Car_Owner_Aadhaar" ColumnName="Car_Owner_Aadhaar" />
                <ScalarProperty Name="Car_Owner_Address" ColumnName="Car_Owner_Address" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Update_Date" ColumnName="Update_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CAR_SEGMENT">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CAR_SEGMENT">
              <MappingFragment StoreEntitySet="RLT_CAR_SEGMENT">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Car_Segment" ColumnName="Car_Segment" />
                <ScalarProperty Name="Car_Segment_Description" ColumnName="Car_Segment_Description" />
                <ScalarProperty Name="Per_KM_fare" ColumnName="Per_KM_fare" />
                <ScalarProperty Name="Capacity" ColumnName="Capacity" />
                <ScalarProperty Name="features" ColumnName="features" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CITY">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CITY">
              <MappingFragment StoreEntitySet="RLT_CITY">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="City_Name" ColumnName="City_Name" />
                <ScalarProperty Name="City_Abbr" ColumnName="City_Abbr" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="State_PKID" ColumnName="State_PKID" />
                <ScalarProperty Name="latitude" ColumnName="latitude" />
                <ScalarProperty Name="longitude" ColumnName="longitude" />
                <ScalarProperty Name="eLoc" ColumnName="eLoc" />
                <ScalarProperty Name="orderIndex" ColumnName="orderIndex" />
                <ScalarProperty Name="score" ColumnName="score" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CLASS_MASTER">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CLASS_MASTER">
              <MappingFragment StoreEntitySet="RLT_CLASS_MASTER">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Class_Name" ColumnName="Class_Name" />
                <ScalarProperty Name="Class_Charges_PKMs" ColumnName="Class_Charges_PKMs" />
                <ScalarProperty Name="Waiting_Charges" ColumnName="Waiting_Charges" />
                <ScalarProperty Name="Driver_Night_Charges" ColumnName="Driver_Night_Charges" />
                <ScalarProperty Name="Other_Charges" ColumnName="Other_Charges" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Company_Details">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Company_Details">
              <MappingFragment StoreEntitySet="RLT_Company_Details">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Company_Name" ColumnName="Company_Name" />
                <ScalarProperty Name="Company_Address" ColumnName="Company_Address" />
                <ScalarProperty Name="State_GSTIN" ColumnName="State_GSTIN" />
                <ScalarProperty Name="SAC_Code" ColumnName="SAC_Code" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_CONTACT_ENQUIRIES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_CONTACT_ENQUIRIES">
              <MappingFragment StoreEntitySet="RLT_CONTACT_ENQUIRIES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="PhoneNumber" ColumnName="PhoneNumber" />
                <ScalarProperty Name="EnquiryMessage" ColumnName="EnquiryMessage" />
                <ScalarProperty Name="EMailID" ColumnName="EMailID" />
                <ScalarProperty Name="RequestDate" ColumnName="RequestDate" />
                <ScalarProperty Name="Remark" ColumnName="Remark" />
                <ScalarProperty Name="IsWhatsAppNumber" ColumnName="IsWhatsAppNumber" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ContactInformation">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ContactInformation">
              <MappingFragment StoreEntitySet="RLT_ContactInformation">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Address" ColumnName="Address" />
                <ScalarProperty Name="SalesTelNo" ColumnName="SalesTelNo" />
                <ScalarProperty Name="BusinessEmailNo" ColumnName="BusinessEmailNo" />
                <ScalarProperty Name="WorkingHours" ColumnName="WorkingHours" />
                <ScalarProperty Name="GPSCordinationLatitude" ColumnName="GPSCordinationLatitude" />
                <ScalarProperty Name="GPSCordinationLongitude" ColumnName="GPSCordinationLongitude" />
                <ScalarProperty Name="isActive" ColumnName="isActive" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="SalesEmailNo" ColumnName="SalesEmailNo" />
                <ScalarProperty Name="BusinessTelNo" ColumnName="BusinessTelNo" />
                <ScalarProperty Name="GoogleMapLink" ColumnName="GoogleMapLink" />
                <ScalarProperty Name="FBLink" ColumnName="FBLink" />
                <ScalarProperty Name="TwitterLink" ColumnName="TwitterLink" />
                <ScalarProperty Name="InstagramLink" ColumnName="InstagramLink" />
                <ScalarProperty Name="PinsterLink" ColumnName="PinsterLink" />
                <ScalarProperty Name="GooglePlusLink" ColumnName="GooglePlusLink" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_COUNTRY">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_COUNTRY">
              <MappingFragment StoreEntitySet="RLT_COUNTRY">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Country_Name" ColumnName="Country_Name" />
                <ScalarProperty Name="Counrty_Abbr" ColumnName="Counrty_Abbr" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Department_Name">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Department_Name">
              <MappingFragment StoreEntitySet="RLT_Department_Name">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="DepartmentName" ColumnName="DepartmentName" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_DISCOUNT_COUPON">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_DISCOUNT_COUPON">
              <MappingFragment StoreEntitySet="RLT_DISCOUNT_COUPON">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Discount_Coupon" ColumnName="Discount_Coupon" />
                <ScalarProperty Name="Discount" ColumnName="Discount" />
                <ScalarProperty Name="DiscountImage" ColumnName="DiscountImage" />
                <ScalarProperty Name="Coupon_Name" ColumnName="Coupon_Name" />
                <ScalarProperty Name="Coupon_Remark" ColumnName="Coupon_Remark" />
                <ScalarProperty Name="Max_Discount" ColumnName="Max_Discount" />
                <ScalarProperty Name="Fare_When_Applied" ColumnName="Fare_When_Applied" />
                <ScalarProperty Name="Coupon_Last_Date" ColumnName="Coupon_Last_Date" />
                <ScalarProperty Name="CouponMaxTimeAllowance" ColumnName="CouponMaxTimeAllowance" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Updated_By" ColumnName="Updated_By" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_DOCUMNETS_NAME">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_DOCUMNETS_NAME">
              <MappingFragment StoreEntitySet="RLT_DOCUMNETS_NAME">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Doc_For" ColumnName="Doc_For" />
                <ScalarProperty Name="Doc_Name" ColumnName="Doc_Name" />
                <ScalarProperty Name="Is_Doc_Req" ColumnName="Is_Doc_Req" />
                <ScalarProperty Name="Is_Doc_Expiry_Date" ColumnName="Is_Doc_Expiry_Date" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_DRIVER_APPROVE_STATUS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_DRIVER_APPROVE_STATUS">
              <MappingFragment StoreEntitySet="RLT_DRIVER_APPROVE_STATUS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Created_By" ColumnName="Created_By" />
                <ScalarProperty Name="Modified_Date" ColumnName="Modified_Date" />
                <ScalarProperty Name="Modified_By" ColumnName="Modified_By" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_DRIVER_ENQUIRIES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_DRIVER_ENQUIRIES">
              <MappingFragment StoreEntitySet="RLT_DRIVER_ENQUIRIES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Driver_Name" ColumnName="Driver_Name" />
                <ScalarProperty Name="Driver_Phone_1" ColumnName="Driver_Phone_1" />
                <ScalarProperty Name="Driver_Car_No" ColumnName="Driver_Car_No" />
                <ScalarProperty Name="Driver_Mail" ColumnName="Driver_Mail" />
                <ScalarProperty Name="Driver_Address" ColumnName="Driver_Address" />
                <ScalarProperty Name="RequestDate" ColumnName="RequestDate" />
                <ScalarProperty Name="Driver_Approving_Status" ColumnName="Driver_Approving_Status" />
                <ScalarProperty Name="Remark" ColumnName="Remark" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Created_BY" ColumnName="Created_BY" />
                <ScalarProperty Name="Updated_BY" ColumnName="Updated_BY" />
                <ScalarProperty Name="IsWhatsAppNumber" ColumnName="IsWhatsAppNumber" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ENTITY_GENERALS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ENTITY_GENERALS">
              <MappingFragment StoreEntitySet="RLT_ENTITY_GENERALS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Entity_Action_Code" ColumnName="Entity_Action_Code" />
                <ScalarProperty Name="Entity_Flow_Code" ColumnName="Entity_Flow_Code" />
                <ScalarProperty Name="Entity_Flow_Run_By" ColumnName="Entity_Flow_Run_By" />
                <ScalarProperty Name="Action_Date" ColumnName="Action_Date" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_FAQ_Details">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_FAQ_Details">
              <MappingFragment StoreEntitySet="RLT_FAQ_Details">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="FAQDetails" ColumnName="FAQDetails" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_LOCATION_CODE">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_LOCATION_CODE">
              <MappingFragment StoreEntitySet="RLT_LOCATION_CODE">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="ZIP_Code" ColumnName="ZIP_Code" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Creatded_Date" ColumnName="Creatded_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="City_PKID" ColumnName="City_PKID" />
                <ScalarProperty Name="Location_Name" ColumnName="Location_Name" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_LOG">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_LOG">
              <MappingFragment StoreEntitySet="RLT_LOG">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Thread" ColumnName="Thread" />
                <ScalarProperty Name="Level" ColumnName="Level" />
                <ScalarProperty Name="Logger" ColumnName="Logger" />
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="Exception" ColumnName="Exception" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_MENU_MASTER">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_MENU_MASTER">
              <MappingFragment StoreEntitySet="RLT_MENU_MASTER">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="MenuName" ColumnName="MenuName" />
                <ScalarProperty Name="MenuIcon" ColumnName="MenuIcon" />
                <ScalarProperty Name="MenuURL" ColumnName="MenuURL" />
                <ScalarProperty Name="PageId" ColumnName="PageId" />
                <ScalarProperty Name="ParentMenuId" ColumnName="ParentMenuId" />
                <ScalarProperty Name="IsAdminMenu" ColumnName="IsAdminMenu" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ActiveMenuClass" ColumnName="ActiveMenuClass" />
                <ScalarProperty Name="OrderNumber" ColumnName="OrderNumber" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="ActionName" ColumnName="ActionName" />
                <ScalarProperty Name="ControllerName" ColumnName="ControllerName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_NextId">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_NextId">
              <MappingFragment StoreEntitySet="RLT_NextId">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Count" ColumnName="Count" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_PAYMENT_METHOD">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_PAYMENT_METHOD">
              <MappingFragment StoreEntitySet="RLT_PAYMENT_METHOD">
                <ScalarProperty Name="PhonePePaymentMethod" ColumnName="PhonePePaymentMethod" />
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Payment_Method_Name" ColumnName="Payment_Method_Name" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_PrivacyPolicy">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_PrivacyPolicy">
              <MappingFragment StoreEntitySet="RLT_PrivacyPolicy">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PrivacyPolicy" ColumnName="PrivacyPolicy" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ROLE_MENU_MAPPING">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ROLE_MENU_MAPPING">
              <MappingFragment StoreEntitySet="RLT_ROLE_MENU_MAPPING">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Role_Id" ColumnName="Role_Id" />
                <ScalarProperty Name="Menu_Id" ColumnName="Menu_Id" />
                <ScalarProperty Name="Is_Add" ColumnName="Is_Add" />
                <ScalarProperty Name="Is_Edit" ColumnName="Is_Edit" />
                <ScalarProperty Name="Is_Delete" ColumnName="Is_Delete" />
                <ScalarProperty Name="Is_View" ColumnName="Is_View" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ROUTE_PLAN">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ROUTE_PLAN">
              <MappingFragment StoreEntitySet="RLT_ROUTE_PLAN">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="From_City_PKID" ColumnName="From_City_PKID" />
                <ScalarProperty Name="To_City_PKID" ColumnName="To_City_PKID" />
                <ScalarProperty Name="Car_PKID" ColumnName="Car_PKID" />
                <ScalarProperty Name="Fecilities_PKID" ColumnName="Fecilities_PKID" />
                <ScalarProperty Name="Car_Class_PKID" ColumnName="Car_Class_PKID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Charges_Master_PKID" ColumnName="Charges_Master_PKID" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_ROUTES_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_ROUTES_DETAILS">
              <MappingFragment StoreEntitySet="RLT_ROUTES_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="Route_Distance_KM" ColumnName="Route_Distance_KM" />
                <ScalarProperty Name="Total_Paid_Tolls_In_Route" ColumnName="Total_Paid_Tolls_In_Route" />
                <ScalarProperty Name="Pickup_City_PKID" ColumnName="Pickup_City_PKID" />
                <ScalarProperty Name="Dropoff_City_PKID" ColumnName="Dropoff_City_PKID" />
                <ScalarProperty Name="Car_Category_PKID" ColumnName="Car_Category_PKID" />
                <ScalarProperty Name="Car_Trip_Type_PKID" ColumnName="Car_Trip_Type_PKID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Services">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Services">
              <MappingFragment StoreEntitySet="RLT_Services">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="ServiceName" ColumnName="ServiceName" />
                <ScalarProperty Name="ServiceIconClass" ColumnName="ServiceIconClass" />
                <ScalarProperty Name="ServiceImagePath" ColumnName="ServiceImagePath" />
                <ScalarProperty Name="BriefIntroAboutService" ColumnName="BriefIntroAboutService" />
                <ScalarProperty Name="AboutService" ColumnName="AboutService" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_STATE">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_STATE">
              <MappingFragment StoreEntitySet="RLT_STATE">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="State_Name" ColumnName="State_Name" />
                <ScalarProperty Name="State_Abbr" ColumnName="State_Abbr" />
                <ScalarProperty Name="Counrty_PKID" ColumnName="Counrty_PKID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_SUBSCRIBERS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_SUBSCRIBERS">
              <MappingFragment StoreEntitySet="RLT_SUBSCRIBERS">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="SubsciberMailID" ColumnName="SubsciberMailID" />
                <ScalarProperty Name="IsValid" ColumnName="IsValid" />
                <ScalarProperty Name="SubscriptionDate" ColumnName="SubscriptionDate" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Template_Mail_Message">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Template_Mail_Message">
              <MappingFragment StoreEntitySet="RLT_Template_Mail_Message">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="SendToType" ColumnName="SendToType" />
                <ScalarProperty Name="SendToId" ColumnName="SendToId" />
                <ScalarProperty Name="Mail_SMS_Type" ColumnName="Mail_SMS_Type" />
                <ScalarProperty Name="TemplateId" ColumnName="TemplateId" />
                <ScalarProperty Name="MobileNo" ColumnName="MobileNo" />
                <ScalarProperty Name="MailId" ColumnName="MailId" />
                <ScalarProperty Name="Subject" ColumnName="Subject" />
                <ScalarProperty Name="Message_Body" ColumnName="Message_Body" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Template_Master">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Template_Master">
              <MappingFragment StoreEntitySet="RLT_Template_Master">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Mail_SMS_Type" ColumnName="Mail_SMS_Type" />
                <ScalarProperty Name="Template_Name" ColumnName="Template_Name" />
                <ScalarProperty Name="Subject" ColumnName="Subject" />
                <ScalarProperty Name="Message_Body" ColumnName="Message_Body" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_TermsConditions">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_TermsConditions">
              <MappingFragment StoreEntitySet="RLT_TermsConditions">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="TermsConditions" ColumnName="TermsConditions" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_TRIP_TYPES">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_TRIP_TYPES">
              <MappingFragment StoreEntitySet="RLT_TRIP_TYPES">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Trip_Type" ColumnName="Trip_Type" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_USER_LOGIN_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_USER_LOGIN_DETAILS">
              <MappingFragment StoreEntitySet="RLT_USER_LOGIN_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="PK_GUID" ColumnName="PK_GUID" />
                <ScalarProperty Name="RLT_User_Name" ColumnName="RLT_User_Name" />
                <ScalarProperty Name="RLT_User_Phone" ColumnName="RLT_User_Phone" />
                <ScalarProperty Name="RLT_User_Email" ColumnName="RLT_User_Email" />
                <ScalarProperty Name="RLT_User_Login_Signup_Method" ColumnName="RLT_User_Login_Signup_Method" />
                <ScalarProperty Name="RLT_User_Aggreed" ColumnName="RLT_User_Aggreed" />
                <ScalarProperty Name="RLT_User_Pwd" ColumnName="RLT_User_Pwd" />
                <ScalarProperty Name="RLT_User_Gender" ColumnName="RLT_User_Gender" />
                <ScalarProperty Name="RLT_User_DOB" ColumnName="RLT_User_DOB" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Last_Login_Date_Time" ColumnName="Last_Login_Date_Time" />
                <ScalarProperty Name="RLT_User_2FA_Auth_Status" ColumnName="RLT_User_2FA_Auth_Status" />
                <ScalarProperty Name="OTPNumber" ColumnName="OTPNumber" />
                <ScalarProperty Name="OTPUpdatedDateTime" ColumnName="OTPUpdatedDateTime" />
                <ScalarProperty Name="UserID" ColumnName="UserID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_UserFeedBack">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_UserFeedBack">
              <MappingFragment StoreEntitySet="RLT_UserFeedBack">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="EmailID" ColumnName="EmailID" />
                <ScalarProperty Name="PhoneNo" ColumnName="PhoneNo" />
                <ScalarProperty Name="Country" ColumnName="Country" />
                <ScalarProperty Name="Photo" ColumnName="Photo" />
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Vendor_Bank_Details">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Vendor_Bank_Details">
              <MappingFragment StoreEntitySet="RLT_Vendor_Bank_Details">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Bank_Name_PKID" ColumnName="Bank_Name_PKID" />
                <ScalarProperty Name="IFSC_CODE" ColumnName="IFSC_CODE" />
                <ScalarProperty Name="Account_Holder_Name" ColumnName="Account_Holder_Name" />
                <ScalarProperty Name="Account_Number" ColumnName="Account_Number" />
                <ScalarProperty Name="Vendor_Ref_PKID" ColumnName="Vendor_Ref_PKID" />
                <ScalarProperty Name="Bank_Status" ColumnName="Bank_Status" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="LastModified_Date" ColumnName="LastModified_Date" />
                <ScalarProperty Name="Lastmodified_By" ColumnName="Lastmodified_By" />
                <ScalarProperty Name="Is_active" ColumnName="Is_active" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_VENDOR_CAR_DETAILS">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_VENDOR_CAR_DETAILS">
              <MappingFragment StoreEntitySet="RLT_VENDOR_CAR_DETAILS">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Car_Company_Ref" ColumnName="Car_Company_Ref" />
                <ScalarProperty Name="Car_Segment_Ref" ColumnName="Car_Segment_Ref" />
                <ScalarProperty Name="Car_Fuel_Type_Ref" ColumnName="Car_Fuel_Type_Ref" />
                <ScalarProperty Name="Car_Number" ColumnName="Car_Number" />
                <ScalarProperty Name="Car_Registration_Date" ColumnName="Car_Registration_Date" />
                <ScalarProperty Name="Driver_Name" ColumnName="Driver_Name" />
                <ScalarProperty Name="Driver_Phone1" ColumnName="Driver_Phone1" />
                <ScalarProperty Name="Driver_Phone2" ColumnName="Driver_Phone2" />
                <ScalarProperty Name="Vendor_Ref_ID" ColumnName="Vendor_Ref_ID" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Last_Modified_Date" ColumnName="Last_Modified_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Vendor_Details">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Vendor_Details">
              <MappingFragment StoreEntitySet="RLT_Vendor_Details">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Vendor_Company_Name" ColumnName="Vendor_Company_Name" />
                <ScalarProperty Name="Vendor_Owner_Name" ColumnName="Vendor_Owner_Name" />
                <ScalarProperty Name="Vendor_Photo" ColumnName="Vendor_Photo" />
                <ScalarProperty Name="Vendor_EmailID" ColumnName="Vendor_EmailID" />
                <ScalarProperty Name="Vendor_Phone1" ColumnName="Vendor_Phone1" />
                <ScalarProperty Name="Vendor_Phone2" ColumnName="Vendor_Phone2" />
                <ScalarProperty Name="Phone1_IsWhatsup" ColumnName="Phone1_IsWhatsup" />
                <ScalarProperty Name="Vendor_Address" ColumnName="Vendor_Address" />
                <ScalarProperty Name="Vendor_Member_Id" ColumnName="Vendor_Member_Id" />
                <ScalarProperty Name="Vendor_LoginID" ColumnName="Vendor_LoginID" />
                <ScalarProperty Name="Vendor_Pwd" ColumnName="Vendor_Pwd" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Is_2FA_Activated" ColumnName="Is_2FA_Activated" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Updated_Date" ColumnName="Updated_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLT_Vendor_Docs">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLT_Vendor_Docs">
              <MappingFragment StoreEntitySet="RLT_Vendor_Docs">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Vendor_Doc_Id" ColumnName="Vendor_Doc_Id" />
                <ScalarProperty Name="Vendor_Ref_PKID" ColumnName="Vendor_Ref_PKID" />
                <ScalarProperty Name="Document_Path" ColumnName="Document_Path" />
                <ScalarProperty Name="Is_Verified" ColumnName="Is_Verified" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Last_Modified_Date" ColumnName="Last_Modified_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="Comments" ColumnName="Comments" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLTBookingAuditTrails">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLTBookingAuditTrail">
              <MappingFragment StoreEntitySet="RLTBookingAuditTrails">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLTConfigurationAuditTrails">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLTConfigurationAuditTrail">
              <MappingFragment StoreEntitySet="RLTConfigurationAuditTrails">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="RequestDataChange" ColumnName="RequestDataChange" />
                <ScalarProperty Name="ResourceActionCode" ColumnName="ResourceActionCode" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLTMostFavoriteRoutes">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLTMostFavoriteRoute">
              <MappingFragment StoreEntitySet="RLTMostFavoriteRoutes">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="PickUpCityID" ColumnName="PickUpCityID" />
                <ScalarProperty Name="DropOffCityID" ColumnName="DropOffCityID" />
                <ScalarProperty Name="CarCategoryID" ColumnName="CarCategoryID" />
                <ScalarProperty Name="TripType" ColumnName="TripType" />
                <ScalarProperty Name="Fare" ColumnName="Fare" />
                <ScalarProperty Name="GSTFare" ColumnName="GSTFare" />
                <ScalarProperty Name="Distance" ColumnName="Distance" />
                <ScalarProperty Name="TravelDuration" ColumnName="TravelDuration" />
                <ScalarProperty Name="Facilities" ColumnName="Facilities" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="CityImage" ColumnName="CityImage" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLTResources">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLTResource">
              <MappingFragment StoreEntitySet="RLTResources">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="ResourceCode" ColumnName="ResourceCode" />
                <ScalarProperty Name="ResourceCodeText" ColumnName="ResourceCodeText" />
                <ScalarProperty Name="ResourceStatus" ColumnName="ResourceStatus" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RLTResourceStaus">
            <EntityTypeMapping TypeName="RLTCARMODEL.RLTResourceStau">
              <MappingFragment StoreEntitySet="RLTResourceStaus">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="StatusName" ColumnName="StatusName" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RoleClaims">
            <EntityTypeMapping TypeName="RLTCARMODEL.RoleClaim">
              <MappingFragment StoreEntitySet="RoleClaims">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="RoleId" ColumnName="RoleId" />
                <ScalarProperty Name="ClaimType" ColumnName="ClaimType" />
                <ScalarProperty Name="ClaimValue" ColumnName="ClaimValue" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Roles">
            <EntityTypeMapping TypeName="RLTCARMODEL.Role">
              <MappingFragment StoreEntitySet="Roles">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="NormalizedName" ColumnName="NormalizedName" />
                <ScalarProperty Name="ConcurrencyStamp" ColumnName="ConcurrencyStamp" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TempBookings">
            <EntityTypeMapping TypeName="RLTCARMODEL.TempBooking">
              <MappingFragment StoreEntitySet="TempBookings">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="User_Name" ColumnName="User_Name" />
                <ScalarProperty Name="Mobile_Number" ColumnName="Mobile_Number" />
                <ScalarProperty Name="PickUp_Address" ColumnName="PickUp_Address" />
                <ScalarProperty Name="DropOff_Address" ColumnName="DropOff_Address" />
                <ScalarProperty Name="Travel_Date" ColumnName="Travel_Date" />
                <ScalarProperty Name="Travel_Time" ColumnName="Travel_Time" />
                <ScalarProperty Name="Is_Active" ColumnName="Is_Active" />
                <ScalarProperty Name="Booking_Date" ColumnName="Booking_Date" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserClaims">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserClaim">
              <MappingFragment StoreEntitySet="UserClaims">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="ClaimType" ColumnName="ClaimType" />
                <ScalarProperty Name="ClaimValue" ColumnName="ClaimValue" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserDiscountAvaileds">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserDiscountAvailed">
              <MappingFragment StoreEntitySet="UserDiscountAvaileds">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="DiscountCouponId" ColumnName="DiscountCouponId" />
                <ScalarProperty Name="AvailedDate" ColumnName="AvailedDate" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserLogins">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserLogin">
              <MappingFragment StoreEntitySet="UserLogins">
                <ScalarProperty Name="LoginProvider" ColumnName="LoginProvider" />
                <ScalarProperty Name="ProviderKey" ColumnName="ProviderKey" />
                <ScalarProperty Name="ProviderDisplayName" ColumnName="ProviderDisplayName" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Users">
            <EntityTypeMapping TypeName="RLTCARMODEL.User">
              <MappingFragment StoreEntitySet="Users">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="NormalizedUserName" ColumnName="NormalizedUserName" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="NormalizedEmail" ColumnName="NormalizedEmail" />
                <ScalarProperty Name="EmailConfirmed" ColumnName="EmailConfirmed" />
                <ScalarProperty Name="PasswordHash" ColumnName="PasswordHash" />
                <ScalarProperty Name="SecurityStamp" ColumnName="SecurityStamp" />
                <ScalarProperty Name="ConcurrencyStamp" ColumnName="ConcurrencyStamp" />
                <ScalarProperty Name="PhoneNumber" ColumnName="PhoneNumber" />
                <ScalarProperty Name="PhoneNumberConfirmed" ColumnName="PhoneNumberConfirmed" />
                <ScalarProperty Name="TwoFactorEnabled" ColumnName="TwoFactorEnabled" />
                <ScalarProperty Name="LockoutEnd" ColumnName="LockoutEnd" />
                <ScalarProperty Name="LockoutEnabled" ColumnName="LockoutEnabled" />
                <ScalarProperty Name="AccessFailedCount" ColumnName="AccessFailedCount" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="UserType" ColumnName="UserType" />
                <ScalarProperty Name="CurrentOTPNumber" ColumnName="CurrentOTPNumber" />
                <ScalarProperty Name="OTPExpireTimeInMinute" ColumnName="OTPExpireTimeInMinute" />
                <ScalarProperty Name="OTPGeneratedDate" ColumnName="OTPGeneratedDate" />
                <ScalarProperty Name="UserProfilePicture" ColumnName="UserProfilePicture" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserTokens">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserToken">
              <MappingFragment StoreEntitySet="UserTokens">
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="LoginProvider" ColumnName="LoginProvider" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Value" ColumnName="Value" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Vendor_Car_Driver_Docs">
            <EntityTypeMapping TypeName="RLTCARMODEL.Vendor_Car_Driver_Docs">
              <MappingFragment StoreEntitySet="Vendor_Car_Driver_Docs">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Doc_Name" ColumnName="Doc_Name" />
                <ScalarProperty Name="Vendor_Car_Ref" ColumnName="Vendor_Car_Ref" />
                <ScalarProperty Name="Document_Path" ColumnName="Document_Path" />
                <ScalarProperty Name="Is_Verified" ColumnName="Is_Verified" />
                <ScalarProperty Name="Created_Date" ColumnName="Created_Date" />
                <ScalarProperty Name="Last_Modified_Date" ColumnName="Last_Modified_Date" />
                <ScalarProperty Name="Last_Modified_By" ColumnName="Last_Modified_By" />
                <ScalarProperty Name="Comments" ColumnName="Comments" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RefreshTokens">
            <EntityTypeMapping TypeName="RLTCARMODEL.RefreshToken">
              <MappingFragment StoreEntitySet="RefreshToken">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Token" ColumnName="Token" />
                <ScalarProperty Name="Expires" ColumnName="Expires" />
                <ScalarProperty Name="Created" ColumnName="Created" />
                <ScalarProperty Name="CreatedByIp" ColumnName="CreatedByIp" />
                <ScalarProperty Name="Revoked" ColumnName="Revoked" />
                <ScalarProperty Name="RevokedByIp" ColumnName="RevokedByIp" />
                <ScalarProperty Name="ReplacedByToken" ColumnName="ReplacedByToken" />
                <ScalarProperty Name="ApplicationUserId" ColumnName="ApplicationUserId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Roles1">
            <EntityTypeMapping TypeName="RLTCARMODEL.Role1">
              <MappingFragment StoreEntitySet="Role">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="NormalizedName" ColumnName="NormalizedName" />
                <ScalarProperty Name="ConcurrencyStamp" ColumnName="ConcurrencyStamp" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RoleClaims1">
            <EntityTypeMapping TypeName="RLTCARMODEL.RoleClaims1">
              <MappingFragment StoreEntitySet="RoleClaims1">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="RoleId" ColumnName="RoleId" />
                <ScalarProperty Name="ClaimType" ColumnName="ClaimType" />
                <ScalarProperty Name="ClaimValue" ColumnName="ClaimValue" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Users1">
            <EntityTypeMapping TypeName="RLTCARMODEL.User1">
              <MappingFragment StoreEntitySet="User">
                <ScalarProperty Name="OTPGeneratedDate" ColumnName="OTPGeneratedDate" />
                <ScalarProperty Name="CurrentOTPNumber" ColumnName="CurrentOTPNumber" />
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="NormalizedUserName" ColumnName="NormalizedUserName" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="NormalizedEmail" ColumnName="NormalizedEmail" />
                <ScalarProperty Name="EmailConfirmed" ColumnName="EmailConfirmed" />
                <ScalarProperty Name="PasswordHash" ColumnName="PasswordHash" />
                <ScalarProperty Name="SecurityStamp" ColumnName="SecurityStamp" />
                <ScalarProperty Name="ConcurrencyStamp" ColumnName="ConcurrencyStamp" />
                <ScalarProperty Name="PhoneNumber" ColumnName="PhoneNumber" />
                <ScalarProperty Name="PhoneNumberConfirmed" ColumnName="PhoneNumberConfirmed" />
                <ScalarProperty Name="TwoFactorEnabled" ColumnName="TwoFactorEnabled" />
                <ScalarProperty Name="LockoutEnd" ColumnName="LockoutEnd" />
                <ScalarProperty Name="LockoutEnabled" ColumnName="LockoutEnabled" />
                <ScalarProperty Name="AccessFailedCount" ColumnName="AccessFailedCount" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="UserType" ColumnName="UserType" />
                <ScalarProperty Name="OTPExpireTimeInMinute" ColumnName="OTPExpireTimeInMinute" />
                <ScalarProperty Name="UserProfilePicture" ColumnName="UserProfilePicture" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserClaims1">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserClaims1">
              <MappingFragment StoreEntitySet="UserClaims1">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="ClaimType" ColumnName="ClaimType" />
                <ScalarProperty Name="ClaimValue" ColumnName="ClaimValue" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserLogins1">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserLogins1">
              <MappingFragment StoreEntitySet="UserLogins1">
                <ScalarProperty Name="LoginProvider" ColumnName="LoginProvider" />
                <ScalarProperty Name="ProviderKey" ColumnName="ProviderKey" />
                <ScalarProperty Name="ProviderDisplayName" ColumnName="ProviderDisplayName" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserTokens1">
            <EntityTypeMapping TypeName="RLTCARMODEL.UserTokens1">
              <MappingFragment StoreEntitySet="UserTokens1">
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="LoginProvider" ColumnName="LoginProvider" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Value" ColumnName="Value" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <AssociationSetMapping Name="UserRoles" TypeName="RLTCARMODEL.UserRoles" StoreEntitySet="UserRoles">
            <EndProperty Name="Roles">
              <ScalarProperty Name="Id" ColumnName="Roles_Id" />
            </EndProperty>
            <EndProperty Name="Users">
              <ScalarProperty Name="Id" ColumnName="Users_Id" />
            </EndProperty>
          </AssociationSetMapping>
          <AssociationSetMapping Name="UserRoles1" TypeName="RLTCARMODEL.UserRoles1" StoreEntitySet="UserRoles1">
            <EndProperty Name="Role">
              <ScalarProperty Name="Id" ColumnName="RoleId" />
            </EndProperty>
            <EndProperty Name="User">
              <ScalarProperty Name="Id" ColumnName="UserId" />
            </EndProperty>
          </AssociationSetMapping>
          <FunctionImportMapping FunctionImportName="Proc_GetUserDtl" FunctionName="RLTCARMODEL.Store.Proc_GetUserDtl">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.Proc_GetUserDtl_Result">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="RoleId" ColumnName="RoleId" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="UserPWD" ColumnName="UserPWD" />
                <ScalarProperty Name="UserPhoto" ColumnName="UserPhoto" />
                <ScalarProperty Name="UserFirstName" ColumnName="UserFirstName" />
                <ScalarProperty Name="UserMiddleName" ColumnName="UserMiddleName" />
                <ScalarProperty Name="UserLastName" ColumnName="UserLastName" />
                <ScalarProperty Name="UserEmailID" ColumnName="UserEmailID" />
                <ScalarProperty Name="UserMobileNo" ColumnName="UserMobileNo" />
                <ScalarProperty Name="VendorId" ColumnName="VendorId" />
                <ScalarProperty Name="CarOwnerId" ColumnName="CarOwnerId" />
                <ScalarProperty Name="EmployeeId" ColumnName="EmployeeId" />
                <ScalarProperty Name="AadharId" ColumnName="AadharId" />
                <ScalarProperty Name="DepartmentId" ColumnName="DepartmentId" />
                <ScalarProperty Name="Address" ColumnName="Address" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsAdmin" ColumnName="IsAdmin" />
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="Is2TfaAuthentication" ColumnName="Is2TfaAuthentication" />
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="SP_GET_REPORT_DATA" FunctionName="RLTCARMODEL.Store.SP_GET_REPORT_DATA">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.SP_GET_REPORT_DATA_Result">
                <ScalarProperty Name="Booking_Id" ColumnName="Booking_Id" />
                <ScalarProperty Name="City_From" ColumnName="City_From" />
                <ScalarProperty Name="City_To" ColumnName="City_To" />
                <ScalarProperty Name="Trip_Type" ColumnName="Trip_Type" />
                <ScalarProperty Name="Car_Category" ColumnName="Car_Category" />
                <ScalarProperty Name="Fare" ColumnName="Fare" />
                <ScalarProperty Name="GST_Fare" ColumnName="GST_Fare" />
                <ScalarProperty Name="Booking_Date" ColumnName="Booking_Date" />
                <ScalarProperty Name="PickUp_Address" ColumnName="PickUp_Address" />
                <ScalarProperty Name="DropOff_Address" ColumnName="DropOff_Address" />
                <ScalarProperty Name="PickUp_Date" ColumnName="PickUp_Date" />
                <ScalarProperty Name="PickUp_Time" ColumnName="PickUp_Time" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Mobile_No1" ColumnName="Mobile_No1" />
                <ScalarProperty Name="Mobile_No2" ColumnName="Mobile_No2" />
                <ScalarProperty Name="Mail_Id" ColumnName="Mail_Id" />
                <ScalarProperty Name="Payment_Method_Name" ColumnName="Payment_Method_Name" />
                <ScalarProperty Name="Vendor_Company_Name" ColumnName="Vendor_Company_Name" />
                <ScalarProperty Name="Car_Number" ColumnName="Car_Number" />
                <ScalarProperty Name="BOOKING_STATUS" ColumnName="BOOKING_STATUS" />
                <ScalarProperty Name="Driver_Name" ColumnName="Driver_Name" />
                <ScalarProperty Name="Booking_Remark" ColumnName="Booking_Remark" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Booking_Create" FunctionName="RLTCARMODEL.Store.usp_Booking_Create" />
          <FunctionImportMapping FunctionImportName="usp_Booking_Execute" FunctionName="RLTCARMODEL.Store.usp_Booking_Execute" />
          <FunctionImportMapping FunctionImportName="usp_Contact_Enquiry_Execute" FunctionName="RLTCARMODEL.Store.usp_Contact_Enquiry_Execute" />
          <FunctionImportMapping FunctionImportName="usp_Driver_Enquiry_Execute" FunctionName="RLTCARMODEL.Store.usp_Driver_Enquiry_Execute" />
          <FunctionImportMapping FunctionImportName="usp_Get_About_Us" FunctionName="RLTCARMODEL.Store.usp_Get_About_Us" />
          <FunctionImportMapping FunctionImportName="usp_Get_Car_Category_Details" FunctionName="RLTCARMODEL.Store.usp_Get_Car_Category_Details">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Car_Category_Details_Result">
                <ScalarProperty Name="Car_Category_Abbr" ColumnName="Car_Category_Abbr" />
                <ScalarProperty Name="Per_KM_fare" ColumnName="Per_KM_fare" />
                <ScalarProperty Name="Capacity" ColumnName="Capacity" />
                <ScalarProperty Name="Features" ColumnName="Features" />
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Car_Categroy_Image" ColumnName="Car_Categroy_Image" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Cities" FunctionName="RLTCARMODEL.Store.usp_Get_Cities">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Cities_Result">
                <ScalarProperty Name="City_Name" ColumnName="City_Name" />
                <ScalarProperty Name="longitude" ColumnName="longitude" />
                <ScalarProperty Name="latitude" ColumnName="latitude" />
                <ScalarProperty Name="eLoc" ColumnName="eLoc" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Comapny_Info" FunctionName="RLTCARMODEL.Store.usp_Get_Comapny_Info">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Comapny_Info_Result">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="Address" ColumnName="Address" />
                <ScalarProperty Name="SalesTelNo" ColumnName="SalesTelNo" />
                <ScalarProperty Name="BusinessEmailNo" ColumnName="BusinessEmailNo" />
                <ScalarProperty Name="WorkingHours" ColumnName="WorkingHours" />
                <ScalarProperty Name="GPSCordinationLatitude" ColumnName="GPSCordinationLatitude" />
                <ScalarProperty Name="GPSCordinationLongitude" ColumnName="GPSCordinationLongitude" />
                <ScalarProperty Name="isActive" ColumnName="isActive" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="SalesEmailNo" ColumnName="SalesEmailNo" />
                <ScalarProperty Name="BusinessTelNo" ColumnName="BusinessTelNo" />
                <ScalarProperty Name="GoogleMapLink" ColumnName="GoogleMapLink" />
                <ScalarProperty Name="FBLink" ColumnName="FBLink" />
                <ScalarProperty Name="TwitterLink" ColumnName="TwitterLink" />
                <ScalarProperty Name="InstagramLink" ColumnName="InstagramLink" />
                <ScalarProperty Name="PinsterLink" ColumnName="PinsterLink" />
                <ScalarProperty Name="GooglePlusLink" ColumnName="GooglePlusLink" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Discount_Coupon_Details" FunctionName="RLTCARMODEL.Store.usp_Get_Discount_Coupon_Details">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Discount_Coupon_Details_Result">
                <ScalarProperty Name="Discount_Coupon" ColumnName="Discount_Coupon" />
                <ScalarProperty Name="Discount" ColumnName="Discount" />
                <ScalarProperty Name="Coupon_Name" ColumnName="Coupon_Name" />
                <ScalarProperty Name="Coupon_Remark" ColumnName="Coupon_Remark" />
                <ScalarProperty Name="Max_Discount" ColumnName="Max_Discount" />
                <ScalarProperty Name="Fare_When_Applied" ColumnName="Fare_When_Applied" />
                <ScalarProperty Name="Coupon_Last_Date" ColumnName="Coupon_Last_Date" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Driver_Booking_info" FunctionName="RLTCARMODEL.Store.usp_Get_Driver_Booking_info">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Driver_Booking_info_Result">
                <ScalarProperty Name="Booking_Id" ColumnName="Booking_Id" />
                <ScalarProperty Name="Fare" ColumnName="Fare" />
                <ScalarProperty Name="Distance" ColumnName="Distance" />
                <ScalarProperty Name="from_Destination" ColumnName="from_Destination" />
                <ScalarProperty Name="To_Destination" ColumnName="To_Destination" />
                <ScalarProperty Name="Car_Category_Name" ColumnName="Car_Category_Name" />
                <ScalarProperty Name="BOOKING_STATUS" ColumnName="BOOKING_STATUS" />
                <ScalarProperty Name="statusid" ColumnName="statusid" />
                <ScalarProperty Name="Car_Model_Name" ColumnName="Car_Model_Name" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Driver_Login_and_info" FunctionName="RLTCARMODEL.Store.usp_Get_Driver_Login_and_info" />
          <FunctionImportMapping FunctionImportName="usp_Get_FAQ" FunctionName="RLTCARMODEL.Store.usp_Get_FAQ" />
          <FunctionImportMapping FunctionImportName="usp_Get_Privacy_Policy" FunctionName="RLTCARMODEL.Store.usp_Get_Privacy_Policy" />
          <FunctionImportMapping FunctionImportName="usp_Get_Services" FunctionName="RLTCARMODEL.Store.usp_Get_Services">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Services_Result">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="ServiceName" ColumnName="ServiceName" />
                <ScalarProperty Name="ServiceIconClass" ColumnName="ServiceIconClass" />
                <ScalarProperty Name="ServiceImagePath" ColumnName="ServiceImagePath" />
                <ScalarProperty Name="BriefIntroAboutService" ColumnName="BriefIntroAboutService" />
                <ScalarProperty Name="AboutService" ColumnName="AboutService" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_Template_Master" FunctionName="RLTCARMODEL.Store.usp_Get_Template_Master">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_Template_Master_Result">
                <ScalarProperty Name="Message_Body" ColumnName="Message_Body" />
                <ScalarProperty Name="Template_Name" ColumnName="Template_Name" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_User_Details" FunctionName="RLTCARMODEL.Store.usp_Get_User_Details">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_User_Details_Result">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="RLT_User_Email" ColumnName="RLT_User_Email" />
                <ScalarProperty Name="RLT_User_DOB" ColumnName="RLT_User_DOB" />
                <ScalarProperty Name="RLT_User_Phone" ColumnName="RLT_User_Phone" />
                <ScalarProperty Name="RLT_User_Gender" ColumnName="RLT_User_Gender" />
                <ScalarProperty Name="Last_Login_Date_Time" ColumnName="Last_Login_Date_Time" />
                <ScalarProperty Name="RLT_User_Name" ColumnName="RLT_User_Name" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_Get_UserLogin_Details" FunctionName="RLTCARMODEL.Store.usp_Get_UserLogin_Details">
            <ResultMapping>
              <ComplexTypeMapping TypeName="RLTCARMODEL.usp_Get_UserLogin_Details_Result">
                <ScalarProperty Name="PKID" ColumnName="PKID" />
                <ScalarProperty Name="RLT_User_Email" ColumnName="RLT_User_Email" />
                <ScalarProperty Name="RLT_User_DOB" ColumnName="RLT_User_DOB" />
                <ScalarProperty Name="RLT_User_Phone" ColumnName="RLT_User_Phone" />
                <ScalarProperty Name="RLT_User_Gender" ColumnName="RLT_User_Gender" />
                <ScalarProperty Name="Last_Login_Date_Time" ColumnName="Last_Login_Date_Time" />
                <ScalarProperty Name="RLT_User_Name" ColumnName="RLT_User_Name" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_update_otp" FunctionName="RLTCARMODEL.Store.usp_update_otp" />
          <FunctionImportMapping FunctionImportName="usp_UserLogin_Execute" FunctionName="RLTCARMODEL.Store.usp_UserLogin_Execute" />
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>
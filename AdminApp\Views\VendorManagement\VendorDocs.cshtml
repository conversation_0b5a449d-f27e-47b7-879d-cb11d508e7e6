﻿@model DBLinker.Lib.Model.M_Vendor_Details

@{
    ViewBag.Title = "Vendor Docs";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Add Vendors Documents</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../VendorManagement/VendorManager">Vendors Manager </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Add Vendors Documents</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm("AddVendorDocs", "VendorManagement", FormMethod.Post, new { enctype = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()

            @Html.HiddenFor(x => x.PKID)

            <div class="row">
                <div class="col-md-12">
                    <div class="card card-box">
                        <div class="card-head">
                            <header>@Model.Vendor_Company_Name Documents</header>
                        </div>
                        <div class="card-body ">
                            <div class="table-scrollable">
                                <table class="table table-hover table-checkable order-column full-width" border="1" style="border:1px solid #dee2e6">
                                    <thead>
                                        <tr>

                                            <th width="30%">Document  Name</th>
                                            <th width="40%">Document</th>
                                            <th width="12%">View</th>
                                            <th width="18%">Verified</th>
                                        </tr>
                                    </thead>
                                    <tbody>


                                        @if (Model.M_Vendor_DocsList != null)
                                        {
                                            if (Model.M_Vendor_DocsList.Count != 0)
                                            {
                                                for (int i = 0; i < Model.M_Vendor_DocsList.Count; i++)
                                                {
                                                    <tr class="odd gradeX">
                                                        <td>
                                                            @Html.HiddenFor(a => Model.M_Vendor_DocsList[i].Vendor_Doc_Id)
                                                            @Html.HiddenFor(a => Model.M_Vendor_DocsList[i].Vendor_Doc_Path)
                                                            @Html.DisplayFor(a => Model.M_Vendor_DocsList[i].Vendor_Doc_Name)
                                                        </td>
                                                        <td>
                                                            @Html.TextBoxFor(a => Model.M_Vendor_DocsList[i].Vendor_Doc_UP, new { @class = "mdl-textfield__input", @type = "file", @style = "height: 60px !important;border: 2px dashed rgba(0, 0, 0, 0.3);*/: ; " })

                                                        </td>

                                                        <td>
                                                            @if (!string.IsNullOrEmpty(Model.M_Vendor_DocsList[i].Vendor_Doc_Path))
                                                            {
                                                                <a onClick='return popupSubWindow(this)' href=/Docs/VendorDocs/@Model.M_Vendor_DocsList[i].Vendor_Doc_Path><img src='~/Content/img/attachment.png' height='25' width='25' title="Click here to view the document."></a>
                                                            }
                                                        </td>

                                                        <td>                                                            
                                                            <div style="float:left;width:40%">
                                                                @Html.RadioButtonFor(a => Model.M_Vendor_DocsList[i].Is_Verified, true)
                                                                <label for="radio1"><span><span></span></span> Yes</label>
                                                            </div>
                                                            <div style="float:left;width:50%">
                                                                @Html.RadioButtonFor(a => Model.M_Vendor_DocsList[i].Is_Verified, false)
                                                                <label for="radio2"><span><span></span></span>No</label>
                                                            </div>


                                                        </td>
                                                    </tr>
                                                }
                                            }

                                        }

                                    </tbody>
                                </table>
                            </div>

                           </div>
                    </div>
                </div>
            </div>


            <div class="row" style="text-align:center;">
                <div class="col-md-12">
                    @(PermittedAction.Is_Edit ? Html.Raw("<button type='submit' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSave'>Save</button>") : Html.Raw("")) 
                    <a class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" href="../VendorManagement/VendorManager">Back</a>
                </div>
            </div>

        }

    </div>

</div>

<script src="~/Scripts/CrudFile/VendorDocsCrud.js"></script>
﻿$(document).ready(function () {

    $("#Booking").parent().parent().find("a").next().slideToggle(500);
    $("#Booking").parent().parent().toggleClass('toggled');
    $("#Booking").find("a").addClass("selectedMenu");

    $("#dvAddUpdate").slideUp();
    $("#btnSave").slideDown();
    $("#btnUpdate").slideUp();


    $("#btnAdd").click(function () {
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();

    });


    $("#btnSave").click(function () {
        if (IsValidate()) {

            var formData = new FormData($("#idFormBookingAdd")[0]);
            $.ajax({
                url: 'AddBooking',
                type: "POST",
                //traditional: true,
                data: formData,
                contentType: false,
                processData: false,
                //dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });

    $("#btnUpdate").click(function () {
        if (IsValidate()) {

            var formData = new FormData($("#idFormBookingAdd")[0]);
            $.ajax({
                url: 'AddBooking',
                type: "POST",
                //traditional: true,
                data: formData,
                contentType: false,
                processData: false,
                //dataType: "json",
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });


    $("#btnReset").click(function () {

        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });



});

function Edit(Id) {
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);
    
    $("#City_From_Id").val($("#City_From_Id_" + Id).val());
    $("#City_To_Id").val($("#City_To_Id_" + Id).val());
    $("#Trip_Type_Id").val($("#Trip_Type_Id_" + Id).val());
    $("#Car_Category_Id").val($("#Car_Category_Id_" + Id).val()); 

     if ($("#Booking_Date_" + Id).val() != 'undefined' && $("#Booking_Date_" + Id).val() != null) {
        var BookDate = $("#Booking_Date_" + Id).val().split('-');
        var newBookDate = BookDate[2] + '-' + BookDate[1] + '-' + BookDate[0]; 
        $("#Booking_Date").val(newBookDate);
    }
     if ($("#PickUp_Date_" + Id).val() != 'undefined' && $("#PickUp_Date_" + Id).val() != null) {
        var PickUpDate = $("#PickUp_Date_" + Id).val().split('-');
        var newPickUpDate = PickUpDate[2] + '-' + PickUpDate[1] + '-' + PickUpDate[0];
        $("#PickUp_Date").val(newPickUpDate);
    }
    
    $("#PickUp_Address").val($("#PickUp_Address_" + Id).val());
    $("#DropOff_Address").val($("#DropOff_Address_" + Id).val());
   
   

    $("#PickUp_Time").val($("#PickUp_Time_" + Id).val());
    $("#Name").val($("#Name_" + Id).val());
    $("#Mobile_No1").val($("#Mobile_No1_" + Id).val());
    $("#Mobile_No2").val($("#Mobile_No2_" + Id).val());
    $("#Mail_Id").val($("#Mail_Id_" + Id).val());
    $("#No_Of_Passenger").val($("#No_Of_Passenger_" + Id).val());
    $("#Remark").val($("#Remark_" + Id).val());    
    $("#Mode_Of_Payment_Id").val($("#Mode_Of_Payment_Id_" + Id).val());
    $("#Booking_Status_Id").val($("#Booking_Status_Id_" + Id).val());


    $("#Is_Active").val(true);
    $("#btnSave").slideUp();
    $("#btnUpdate").slideDown();
    $("#dvAddUpdate").slideDown();
}


function Delete(Id) {

    $("#PKID").val('');
    $("#PKID").val(Id);
    $("#hdnOperation").val('U');
    $("#hdnStatus").val(true);


    $("#City_From_Id").val($("#City_From_Id_" + Id).val());
    $("#City_To_Id").val($("#City_To_Id_" + Id).val());
    $("#Trip_Type_Id").val($("#Trip_Type_Id_" + Id).val());
    $("#Car_Category_Id").val($("#Car_Category_Id_" + Id).val());
    $("#Booking_Date").val($("#Booking_Date_" + Id).val());
    $("#PickUp_Address").val($("#PickUp_Address_" + Id).val());
    $("#DropOff_Address").val($("#DropOff_Address_" + Id).val());
    $("#PickUp_Date").val($("#PickUp_Date_" + Id).val());
    $("#PickUp_Time").val($("#PickUp_Time_" + Id).val());
    $("#Name").val($("#Name_" + Id).val());
    $("#Mobile_No1").val($("#Mobile_No1_" + Id).val());
    $("#Mobile_No2").val($("#Mobile_No2_" + Id).val());
    $("#Mail_Id").val($("#Mail_Id_" + Id).val());
    $("#Remark").val($("#Remark_" + Id).val());  
    $("#No_Of_Passenger").val($("#No_Of_Passenger_" + Id).val());
    $("#Mode_Of_Payment_Id").val($("#Mode_Of_Payment_Id_" + Id).val());
    $("#Booking_Status_Id").val($("#Booking_Status_Id_" + Id).val());
    
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#IsActive").val(true);
    }
    else {
        $("#IsActive").val(false);
    }

    $("#hdnOperation").val('U');
    $("#hdnStatus").val(false);


    if ($("#PKID").val() != "") {
        $.ajax({
            url: 'AddBooking',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            contentType: false,
            enctype: 'multipart/form-data',
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function ActivateDeactivate(Id) {
    $("#PKID").val('');
    $("#Is_Active").val($("#status_" + Id).val());
    $("#PKID").val(Id);
    if ($("#status_" + Id).val() == false) {
        $("#Is_Active").val(true);
    }
    else {
        $("#Is_Active").val(false);
    }

    $("#hdnOperation").val('U');


    if ($("#PKID").val() != "") {

        $.ajax({
            url: 'AddBooking',
            type: "POST",
            traditional: true,
            data: $("form").serialize(),
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    toastr.success("Data Successfully Updated!");
                    document.location.reload();
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");

                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });

    }


}

function resetForm() {

    $("#City_From_Id").val('');
    $("#City_To_Id").val('');
    $("#Trip_Type_Id").val('');
    $("#Car_Category_Id").val('');
    $("#Booking_Date").val('');
    $("#PickUp_Address").val('');
    $("#DropOff_Address").val('');
    $("#PickUp_Date").val('');
    $("#PickUp_Time").val('');
    $("#Name").val('');
    $("#Mobile_No1").val('');
    $("#Mobile_No2").val('');
    $("#Mail_Id").val('');
    $("#No_Of_Passenger").val('');
    $("#Mode_Of_Payment_Id").val('');
    $("#Booking_Status_Id").val('');

}

function IsValidate() {  
        
    if ($("#City_From_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select City From!");
        return false;
    }
    if ($("#City_To_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select City To!");
        return false;
    }
    if ($("#Trip_Type_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select Trip Type!");
        return false;
    }
    if ($("#Car_Category_Id").val() == "") {
        toastr.clear();
        toastr.error("Please select Car Category!");
        return false;
    }
    if ($("#PickUp_Date").val() == "") {
        toastr.clear();
        toastr.error("Please enter Pick Up Date!");
        return false;
    }
    if ($("#Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Name!");
        return false;
    }
    if ($("#Mobile_No1").val() == "") {
        toastr.clear();
        toastr.error("Please enter Mobile No 1!");
        return false;
    }

    return true;
}





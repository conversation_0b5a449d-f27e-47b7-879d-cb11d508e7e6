﻿@using AdminApp.Mapper;
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var dashboard = ViewBag.DashboardCounts as AdminApp.Controllers.DashboardCountsDto;
}
<style>
    .table-checkable tr > td:first-child, .table-checkable tr > th:first-child {
        text-align: left !important;
    }
</style>

<link href="~/Content/assets/plugins/circle-progress/Circle.css" rel="stylesheet" />

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Dashboard</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="index.html">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Dashboard</li>
                </ol>
            </div>
        </div>
        <!-- start widget -->
        <div>
            <section id="circleBar">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card border-primary mb-4 shadow-sm">
                            <div class="card-body">
                                <i class="fa fa-user-circle fa-2x mb-2 text-primary" aria-hidden="true"></i>
                                <h4 class="card-title">@dashboard.TotalVendors</h4>
                                <p class="card-text">Vendors</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-success mb-4 shadow-sm">
                            <div class="card-body">
                                <i class="fa fa-car fa-2x mb-2 text-success" aria-hidden="true"></i>
                                <h4 class="card-title">@dashboard.TotalCars</h4>
                                <p class="card-text">Cars</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-warning mb-4 shadow-sm">
                            <div class="card-body">
                                <i class="fa fa-id-badge fa-2x mb-2 text-warning" aria-hidden="true"></i>
                                <h4 class="card-title">@dashboard.TotalDrivers</h4>
                                <p class="card-text">Car Drivers</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-danger mb-4 shadow-sm">
                            <div class="card-body">
                                <i class="fa fa-suitcase fa-2x mb-2 text-danger" aria-hidden="true"></i>
                                <h4 class="card-title">@dashboard.TotalBookings</h4>
                                <p class="card-text">Total Booking</p>
                            </div>
                        </div>
                    </div>
                </div>


                @*<div class="row">
            <div class="col-md-3">
                <div class="round"
                     data-value="@ViewBag.TotalVendors"
                     data-size="200"
                     data-thickness="6">
                    <strong></strong>
                    <span><i class="fa fa-user-circle" aria-hidden="true"></i> Vendors</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="round"
                     data-value="@ViewBag.TotalCars"
                     data-size="200"
                     data-thickness="6">
                    <strong></strong>
                    <span><i class="fa fa-car" aria-hidden="true"></i> Cars</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="round"
                     data-value="@ViewBag.TotalDrivers"
                     data-size="200"
                     data-thickness="6">
                    <strong></strong>
                    <span><i class="fa fa-drivers-license" aria-hidden="true"></i> Car Drivers</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="round"
                     data-value="@ViewBag.TotalBookings"
                     data-size="200"
                     data-thickness="6">
                    <strong></strong>
                    <span><i class="fa fa-suitcase" aria-hidden="true"></i> Total Booking</span>
                </div>
            </div>
        </div>*@

            </section>


            <div class="row">
                <div class="col-md-12">
                    <div class="card card-box">
                        <div class="card-head">
                            <header>All Booking</header>

                        </div>
                        <div class="card-body ">

                            <div class="table-scrollable">
                                <table class="table table-hover table-checkable order-column full-width" id="example4">
                                    <thead>
                                        <tr>
                                            <th width="17%">Booking ID</th>
                                            <th width="12%">City From</th>
                                            <th width="12%">City To</th>
                                            <th width="10%">Trip Type</th>
                                            <th width="13%">Pick up Date</th>
                                            <th width="15%">
                                                Action Date
                                                <div class="tooltipBooking">
                                                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                    <span class="tooltiptext" style="width: 300px;">Booking/Cancel/Assigend/Completed etc.</span>
                                                </div>
                                            </th>
                                            <th width="23%">Booking Status</th>
                                            <th width="10%">View</th> 
                                            <th width="10%">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody> 
                                        @if (ViewBag.BookingList != null)
                                        {
                                            foreach (var item in ViewBag.BookingList)
                                            {
                                                var color = item.Booking_Status_Id == 1 ? "orange" : item.Booking_Status_Id == 2 ? "lawngreen" : item.Booking_Status_Id == 3 ? "blue" : item.Booking_Status_Id == 5 ? "red" : "black";
                                                <tr class="odd gradeX" style="color:@color">
                                                    <td>@item.Booking_Id</td>
                                                    <td>@item.City_From</td>
                                                    <td>@item.City_To</td>
                                                    <td>@item.Trip_Type</td>
                                                    @*<td>@item.Car_Category</td>*@
                                                    <td>@(item.PickUp_Date != null ? item.PickUp_Date.ToShortDateString() : "n/a")</td>
                                                    <td>@(item.Updated_Date != null ? item.Updated_Date.ToShortDateString() : "n/a")</td>
                                                    <td>@item.Booking_Status</td> 
                                                    <td>
                                                        <a href="../BookingManagement/ViewBooking?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Booking")" title="View Booking Info">
                                                            <i class="material-icons">remove_red_eye</i> 
                                                        </a>
                                                    </td> 
                                                    <td>
                                                        <a href="../BookingManagement/AssignCab?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Booking")" title="Action Against Current Booking">
                                                            <i class="material-icons">build</i>
                                                        </a>
                                                    </td> 
                                                </tr>
                                            }
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            @*<div class="row">
                    <div class="col-xl-3 col-md-6 col-12">
                        <div class="info-box bg-b-black">
                            <span class="info-box-icon push-bottom"><i class="fa fa-user-circle"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Vendors</span>
                                <a href="../VendorManagement/VendorManager" target="_blank" title="View Vendor List" style="color:white;">
                                    <span class="info-box-number">@ViewBag.TotalVendors</span>
                                </a>

                            </div>
                        </div>

                    </div>

                    <div class="col-xl-3 col-md-6 col-12">
                        <div class="info-box bg-b-yellow">
                            <span class="info-box-icon push-bottom"><i class="fa fa-car"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Cars</span>
                                <a href="../VendorManagement/CarManager" target="_blank" title="View Car List" style="color:white;">
                                    <span class="info-box-number">@ViewBag.TotalCars</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 col-12">
                        <div class="info-box bg-b-cyan">
                            <span class="info-box-icon push-bottom"><i class="fa fa-drivers-license"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Car Drivers</span>
                                <a href="../VendorManagement/CarDriverManager" target="_blank" title="View Driver List" style="color:white;">
                                    <span class="info-box-number">@ViewBag.TotalDrivers</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 col-12">
                        <div class="info-box bg-light-purple">
                            <span class="info-box-icon push-bottom"><i class="material-icons">card_travel</i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Totel Booking</span>
                                <a href="../BookingManagement/BookingList" target="_blank" title="View Booking List" style="color:white;">
                                    <span class="info-box-number">@ViewBag.TotalBookings</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>*@




            <div class="row">
                <div class="col-lg-5 col-md-12 col-sm-12 col-12">
                    <div class="card card-box">
                        <div class="card-head">
                            <header>Driver Enquiries </header>
                        </div>
                        <div class="card-body ">
                            <div class="table-wrap">
                                <div class="table-responsive tblHeightSet">
                                    <table class="table display product-overview mb-30" style="color:black!important">
                                        <thead>
                                            <tr>
                                                <th width="25%">Name</th>
                                                <th width="25%">Phone</th>
                                                <th width="25%">Date</th>
                                                <th width="10%"></th>
                                            </tr>
                                        </thead>
                                        <tbody>



                                            @if (ViewBag.DriverList != null)
                                            {
                                                foreach (var item in ViewBag.DriverList)
                                                {

                                                    <tr class="odd gradeX">


                                                        <td>@item.Driver_Name</td>
                                                        <td>@item.Driver_Phone_1</td>

                                                        <td>
                                                            @if (@item.RequestDate != null)
                                                            {
                                                                @item.RequestDate.ToShortDateString()
                                                            }

                                                        </td>

                                                        <td>
                                                            <a href="../VendorManagement/BecomeDriverRequest?id=@QueryStringEncoding.EncryptString(Convert.ToString(@item.PKID), "Request")" title="View Driver Request Info">
                                                                <i class="material-icons">remove_red_eye</i>

                                                            </a>
                                                        </td>

                                                    </tr>
                                                }

                                            }

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-7 col-md-12 col-sm-12 col-12">
                    <div class="card card-box">
                        <div class="card-head">
                            <header>Contact Enquiries </header>
                        </div>
                        <div class="card-body ">
                            <div class="table-wrap">
                                <div class="table-responsive tblHeightSet">
                                    <table class="table display product-overview mb-30" style="color:black!important">
                                        <thead>
                                            <tr>
                                                <th width="25%">Name</th>
                                                <th width="25%">Phone</th>
                                                <th width="25%">Email</th>
                                                <th width="25%">Message</th>
                                                <th width="15%">Date</th>

                                            </tr>
                                        </thead>
                                        <tbody>



                                            @if (ViewBag.ContactEnquiryList != null)
                                            {
                                                foreach (var item in ViewBag.ContactEnquiryList)
                                                {

                                                    <tr class="odd gradeX">


                                                        <td>@item.Name</td>
                                                        <td>@item.PhoneNumber</td>
                                                        <td>@item.EMailID</td>
                                                        <td>@item.EnquiryMessage</td>
                                                        <td>
                                                            @if (@item.RequestDate != null)
                                                            {
                                                                @item.RequestDate.ToShortDateString()
                                                            }

                                                        </td>



                                                    </tr>
                                                }

                                            }

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>




            </div>




        </div>
    </div>
</div>




<script src="~/Content/assets/plugins/circle-progress/circle-progress.min.js"></script>
<script src="~/Content/assets/plugins/circle-progress/Circle-function.js"></script>
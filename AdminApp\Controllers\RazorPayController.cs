﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using Razorpay.Api;

namespace AdminApp.Controllers
{
    public class RazorPayController : Controller
    {
        // GET: RazorPay
        public ActionResult RazorPayPayment()
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            Dictionary<string, object> input = new Dictionary<string, object>();
            input.Add("amount", 100); // this amount should be same as transaction amount
            input.Add("currency", "INR");
            input.Add("receipt", "12121");
            input.Add("payment_capture", 1);
            string key = "rzp_test_xig2LY8ipxmNYl";
            string secret = "2gxZBmAxeFpkzxSc63MhR98m";
            RazorpayClient client = new RazorpayClient(key, secret);            
            Razorpay.Api.Order order = client.Order.Create(input);
            string   orderId = order["id"].ToString();       

                  
            /* Prepare HTML Form and Submit to Paytm */
            String outputHtml = "";
            outputHtml += "<html>";
            outputHtml += "<head>";
            outputHtml += "<title>Razorpay.Net Sample App</title>";
            outputHtml += "</head>";
            outputHtml += "<body>";
            outputHtml += "<form method='post' action='http://localhost:64506/RazorPay/RazorPayPayment_Success'>";
            outputHtml += "<script src ='https://checkout.razorpay.com/v1/checkout.js'";
            outputHtml += " data-key = 'rzp_test_xig2LY8ipxmNYl'";
            outputHtml += " data-amount = '100'";
            outputHtml += " data-name ='Razorpay'";
            outputHtml += " data-description ='Purchase Description'";
            outputHtml += " data-order_id = "+orderId+ "";
            outputHtml += " data-image ='https://razorpay.com/favicon.png'";           
            outputHtml += " data-prefill.name ='Gaurav Kumar'";
            outputHtml += " data-prefill.email ='gaurav.kumar @example.com'";
            outputHtml += " data-prefill.contact ='9123456789'";
            outputHtml += " data-theme.color ='#F37254'";
            outputHtml += " ></script>";
            outputHtml += "<input type='hidden' name='hidden' value='Hidden Element'>";
            outputHtml += "</form></body></html>";
            TempData["RazorList"] = outputHtml;
            return View();
        }
        [HttpPost]
        public ActionResult RazorPayPayment_Success()
        {
            string paymentId = Request.Form["razorpay_payment_id"];

            Dictionary<string, object> input = new Dictionary<string, object>();
            input.Add("amount", 100); // this amount should be same as transaction amount

            string key = "rzp_test_xig2LY8ipxmNYl";
            string secret = "2gxZBmAxeFpkzxSc63MhR98m";

            RazorpayClient client = new RazorpayClient(key, secret);

            Dictionary<string, string> attributes = new Dictionary<string, string>();

            attributes.Add("razorpay_payment_id", paymentId);
            attributes.Add("razorpay_order_id", Request.Form["razorpay_order_id"]);
            attributes.Add("razorpay_signature", Request.Form["razorpay_signature"]);

            Utils.verifyPaymentSignature(attributes);

            //Refund refund = new Razorpay.Api.Payment((string)paymentId).Refund();

            //Console.WriteLine(refund["id"]);
            return View();
        }
        }
}
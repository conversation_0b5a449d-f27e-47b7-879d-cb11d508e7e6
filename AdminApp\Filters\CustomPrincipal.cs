﻿using DBLinker.Lib.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Web;

namespace AdminApp.Filters
{
    public class CustomPrincipal : IPrincipal
    {
        public IIdentity Identity { get; private set; }
        public bool IsInRole(string role)
        {
            if (roles.Any(r => role.Contains(r)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public CustomPrincipal(string Username)
        {
            this.Identity = new GenericIdentity(Username);
        }

        public string UserName { get; set; }

        public string UserEmailID { get; set; }
        public string UserFirstName { get; set; }
        public string UserLastName { get; set; }

        public Nullable<bool> Is2TfaAuthentication { get; set; }
        public int? roleId { get; set; }
        public string[] roles { get; set; }
        public List<M_Role_Menu_Mapping> MenuList { get; set; }
        public int userId { get; set; }

        public string userPhoto { get; set; }
    }

    public class CustomPrincipalSerializeModel
    {
        public int userId { get; set; }
        public string UserName { get; set; }

        public string UserEmailID { get; set; }
        public string UserFirstName { get; set; }
        public string UserLastName { get; set; }

        public Nullable<bool> Is2TfaAuthentication { get; set; }
        public int? roleId { get; set; }

        public string[] roles { get; set; }
        public List<M_Role_Menu_Mapping> MenuList { get; set; }
        public string userPhoto { get; set; }
    }
}
﻿$(document).ready(function () {
    resetForm();
    FileUploader();
    $("#DiscountCouponManager").parent().parent().find("a").next().slideToggle(500);
    $("#DiscountCouponManager").parent().parent().toggleClass('toggled');
    $("#DiscountCouponManager").find("a").addClass("selectedMenu");
    $("#dvAddUpdate").slideUp();
    // Events 
    $("#discountCopounImage").change(function () {
        if (PhotoCheck(document.getElementById("discountCopounImage"))) {
            $("#img-viwer").fadeIn("fast").attr('src', URL.createObjectURL(event.target.files[0]));
            FileUploader();
        }
    });
    $("#btnAdd").click(function () {
        resetForm();
        $("#dvAddUpdate").slideDown();
        $("#btnAdd").slideUp();
        $("#btnSave").slideDown();
        $("#btnUpdate").slideUp();
    });
    $("#btnSave").click(function () {
        if (IsValidate(false)) {
            var formData = new FormData($("#FormDiscountCouponManager")[0]);
            formData.append("discountCopounImage", $("#discountCopounImage").get(0).files[0]);
            $.ajax({
                url: 'AddDiscountCouponManager',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Saved!");
                        document.location.reload();
                    }
                    else {
                        toastr.success("Sorry Data Not Saved,please try it again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });
        }
    });
    $("#btnUpdate").click(function () {
        if (IsValidate(true)) {
            var formData = new FormData($("#FormDiscountCouponManager")[0]);
            formData.append("discountCopounImage", $("#discountCopounImage").get(0).files[0]);
            $.ajax({
                url: 'EditDiscountCouponManager',
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response > 0) {
                        toastr.success("Data Successfully Updated!", "Discount Copoun");
                        window.setTimeout(function () { location.reload() }, 1000)
                    }
                    else {
                        toastr.success("Sorry Data Not Updated,Please Try it Again !");

                    }
                },
                failure: function (response) {

                },
                error: function (response) {
                    alert(response.responseText);
                }
            });



        }
    });
    $("#btnReset").click(function () {
        $("#dvAddUpdate").slideUp();
        $("#btnAdd").slideDown();
        resetForm();
    });
    // End Events 
});
//Functions
function FileUploader() {
    var getImg = $("#img-viwer").attr("src")
    if (getImg == "#") {
        $("#img-viwer").hide();
        $("p.file-uploader-text").show();
    }
    else {
        $("#img-viwer").show();
        $("p.file-uploader-text").hide();
    }
}
function PhotoCheck(input) {
    if (input.files.length > 0) {
        for (var i = 0; i < input.files.length; i++) {
            var file = input.files[i];
            var fileType = file["type"];
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            if ($.inArray(fileType, validImageTypes) < 0) {
                toastr.clear();
                toastr.error("Please upload only JPG,PNG, GIF Image !", "Required!");
                return false;
            }
            else return true;
        }
    }
    else return "no-file";
};
function Edit(Id) {
    $.ajax({
        url: 'EditDiscountCouponManager',
        type: "Get",
        traditional: true,
        data: { id: Id },
        dataType: "json",
        success: function (data) {
            if (data != null && data != false) {
                resetForm();
                $("#PKID").val(data.PKID);
                $("#hdnOperation").val('U');
                $("#hdnStatus").val(true);

                $("#Coupon_Name").val(data.Coupon_Name);
                $("#Discount").val(data.Discount);
                $("#Discount_Coupon").val(data.Discount_Coupon);
                $("#Coupon_Remark").val(data.Coupon_Remark);
                $("#Max_Discount").val(data.Max_Discount);
                $("#Fare_When_Applied").val(data.Fare_When_Applied);

                if (data.Coupon_Last_Date != "n/a" && data.Coupon_Last_Date != null) {
                    var couponLastDate = new Date(parseInt(data.Coupon_Last_Date.substr(6)));
                    if (Date.parse(couponLastDate)) {
                        $("#Coupon_Last_Date").val(couponLastDate.getFullYear() + "-" + ("0" + (couponLastDate.getMonth() + 1)).slice(-2) + "-" + ("0" + (couponLastDate.getDate())).slice(-2));
                    }
                }
                if (data.DiscountImage != null && data.DiscountImage != "") {
                    $("#img-viwer").attr('src', '../Docs/DiscountCouponManager/' + data.DiscountImage)
                    $("#img-viwer").show();
                    $("p.file-uploader-text").hide();
                }
                else {
                    $("#img-viwer").hide();
                    $("p.file-uploader-text").show();
                }
                if (data.Is_Active != null && data.Is_Active == true) $("#IsActiveTrue").prop("checked", true);
                else $("#IsActiveFalse").prop("checked", true);
                $("#CouponMaxTimeAllowance").val(data.CouponMaxTimeAllowance);

                $("#btnSave").slideUp();
                $("#btnUpdate").slideDown();
                $("#dvAddUpdate").slideDown();
                $('html, body').animate({
                    scrollTop: $("#dvAddUpdate").offset().top
                }, 500);
            }
            else {
                toastr.success("Sorry Data Not Found ,Please Try it Again ! " + JSON.stringify(data), "Error");
            }
        },
        failure: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response), "Error");
        },
        error: function (response) {
            toastr.error("Sorry Data Not Found ,Please Try it Again !" + JSON.stringify(response.responseText), "Error");
        }
    });
}
function ActivateDeactivate(Id) {
    if (Id != "") {
        $.ajax({
            url: 'ActivateDeactivateDiscountCouponManager',
            type: "POST",
            data: { id: Id },
            dataType: "json",
            success: function (response) {
                if (response) {
                    toastr.success("Discount Coupon Status Successfully Updated!", "Discount Coupon");
                    window.setTimeout(function () { location.reload() }, 1000)
                }
                else {
                    toastr.success("Sorry Data Not Updated,Please Try it Again !");
                }
            },
            failure: function (response) {

            },
            error: function (response) {
                alert(response.responseText);
            }
        });
    }
}
function resetForm() {
    $("#PKID").val('');
    $("#Coupon_Name").val('');
    $("#Discount_Coupon").val('');
    $("#Discount").val('');
    $("#Coupon_Remark").val('');
    $("#Max_Discount").val('');
    $("#Fare_When_Applied").val('');
    $("#Coupon_Last_Date").val('');
    $("#CouponMaxTimeAllowance").val('');
    $("input[type=radio][name=IsActive]").prop("checked", false);
    $("#discountCopounImage").val('');
    $("#img-viwer").hide();
    $("p.file-uploader-text").show();
}
function IsValidate(isEdit) {
    if (isEdit) {
        if ($("#PKID").val() == "") {
            toastr.clear();
            toastr.error("Please Select Edit Copoun Again !", "Validation Error");
            return false;
        }
    }
    if ($("#Coupon_Name").val() == "") {
        toastr.clear();
        toastr.error("Please enter Coupon Name !", "Required!");
        $("#Coupon_Name").focus();
        return false;
    }
    if ($("#Discount_Coupon").val() == "") {
        toastr.clear();
        toastr.error("Please enter Discount Coupon Code !", "Required!");
        $("#Discount_Coupon").focus();
        return false;
    }
    if (!isEdit) {
        var input = document.getElementById("discountCopounImage");
        if (input.files.length == 0) {
            toastr.clear();
            toastr.error("Please select Copoun Banner Image!", "Required!");
            return false;
        };
    }

    /*Repeat Validation*/
    if ($("#Discount_Coupon").val() != "") {
        if ($("#Discount_Coupon-error").html() !== undefined) {
            toastr.clear();
            toastr.error("Discount Coupon already exists!", "Required!");
            $("#Discount_Coupon").focus();
            return false;
        }
    }
    if ($("#Discount").val() == "") {
        toastr.clear();
        toastr.error("Please enter Discount !", "Required!");
        $("#Discount").focus();
        return false;
    }
    if ($("#Max_Discount").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Max Discount!", "Required!");
        $("#Max_Discount").focus();
        return false;
    }
    if ($("#Fare_When_Applied").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Minimum Fare when Coupon Applied!", "Required!");
        $("#Fare_When_Applied").focus();
        return false;
    }
    if ($("#Coupon_Last_Date").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Copoun Expiry Date!", "Required!");
        return false;
    }
    if ($("#CouponMaxTimeAllowance").val() == "") {
        toastr.clear();
        toastr.error("Please Enter Copoun  Maximum Time Allowance For User!", "Required!");
        $("#CouponMaxTimeAllowance").focus();
        return false;
    }
    if (!$.isNumeric($("#CouponMaxTimeAllowance").val())) {
        toastr.clear();
        toastr.error("Please Enter Numeric Value in Copoun  Maximum Time Allowance!", "Required!");
        $("#CouponMaxTimeAllowance").focus();
        return false;
    }



    if ($("input[type=radio][name=Is_Active]:checked").val() == undefined) {
        toastr.clear();
        toastr.error("Please Set Dicount Copoun to active or Inactive!", "Required!");
        return false;
    }

    return true;
}
﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Lib
{
    public class ResizeImage: IResizeImage
    {
        public ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }

        public  void AddWatermark(string filename, string filename1, int type)
        {
            Bitmap bmp = new Bitmap(filename);
            Graphics canvas = Graphics.FromImage(bmp);
            if (type == 1)
            {
                canvas.DrawString("PicsLike.com", new Font("Arial", 10), new SolidBrush(Color.LightPink), new PointF(50, 50));
            }
            else if (type == 2)
            {
                canvas.DrawString("PicsLike.com", new Font("Arial", 10), new SolidBrush(Color.Blue), new PointF(50, 50));
            }
            canvas.Dispose();
            bmp.SetResolution(96, 96);
            bmp.Save(filename1);
            bmp.Dispose();
        }

        //public static void VaryQualityLevel(String filename, String dest)
        //{
        //    Bitmap bmp1 = new Bitmap(filename);
        //    ImageCodecInfo jgpEncoder = GetEncoder(ImageFormat.Jpeg);
        //    System.Drawing.Imaging.Encoder myEncoder = System.Drawing.Imaging.Encoder.Quality;
        //    EncoderParameters myEncoderParameters = new EncoderParameters(1);
        //    EncoderParameter myEncoderParameter = new EncoderParameter(myEncoder, 10);
        //    myEncoderParameters.Param[0] = myEncoderParameter;
        //    bmp1.Save(dest, jgpEncoder, myEncoderParameters);
        //    bmp1.Dispose();
        //}

        //public static ImageCodecInfo GetEncoder(ImageFormat format)
        //{
        //    ImageCodecInfo[] encoders;
        //    encoders = ImageCodecInfo.GetImageEncoders();
        //    for (int j = 0; j < encoders.Length; j++)
        //    {
        //        return encoders[j];
        //    }
        //    return null;

        //}

        public void ResizeImage1(String fullpath, String destpath, int width, int height)
        {
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm1 = new Bitmap(fs);
            Bitmap thumb1 = new Bitmap(width, height);

            System.Drawing.Graphics g1 = System.Drawing.Graphics.FromImage(thumb1);
            g1.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g1.DrawImage(bm1, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm1.Width, bm1.Height), GraphicsUnit.Pixel);
            g1.Dispose();
            thumb1.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm1.Dispose();
            thumb1.Dispose();
            fs.Close();
        }

        public void ResizeImage2(String fullpath, String destpath, int width, int height)
        {
            //height = 515;
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm2 = new Bitmap(fs);
            Bitmap thumb2 = new Bitmap(width, height);
            System.Drawing.Graphics g2 = System.Drawing.Graphics.FromImage(thumb2);
            g2.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g2.DrawImage(bm2, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm2.Width, bm2.Height), GraphicsUnit.Pixel);
            g2.Dispose();
            thumb2.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm2.Dispose();
            thumb2.Dispose();
            fs.Close();
        }

        public void ResizeImage3(String fullpath, String destpath, int width, int height)
        {
            //height = 1100;
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm3 = new Bitmap(fs);
            Bitmap thumb3 = new Bitmap(width, height);
            System.Drawing.Graphics g3 = System.Drawing.Graphics.FromImage(thumb3);
            g3.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g3.DrawImage(bm3, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm3.Width, bm3.Height), GraphicsUnit.Pixel);
            g3.Dispose();
            thumb3.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm3.Dispose();
            thumb3.Dispose();
            fs.Close();
        }
        public void ResizeByHeight(String fullpath, String destpath, int height)
        {
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm3 = new Bitmap(fs);
            var ratio = (double)height / bm3.Height;

            var newWidth = (int)(bm3.Width * ratio);
            var newHeight = (int)(bm3.Height * ratio);

            var newImage = new Bitmap(newWidth, newHeight);

            Bitmap thumb3 = new Bitmap(newWidth, height);
            System.Drawing.Graphics g3 = System.Drawing.Graphics.FromImage(thumb3);
            g3.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g3.DrawImage(bm3, new Rectangle(0, 0, newWidth, height), new Rectangle(0, 0, bm3.Width, bm3.Height), GraphicsUnit.Pixel);
            g3.Dispose();
            thumb3.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm3.Dispose();
            thumb3.Dispose();
            fs.Close();
        }
        public void cropimage(int Width, int Height, string sourceFilePath, string saveFilePath)
        {
            // variable for percentage resize 
            float percentageResize = 0;
            float percentageResizeW = 0;
            float percentageResizeH = 0;

            // variables for the dimension of source and cropped image 
            int sourceX = 0;
            int sourceY = 0;
            int destX = 0;
            int destY = 0;

            // Create a bitmap object file from source file 
            Bitmap sourceImage = new Bitmap(sourceFilePath);

            // Set the source dimension to the variables 
            int sourceWidth = sourceImage.Width;
            int sourceHeight = sourceImage.Height;

            // Calculate the percentage resize 
            percentageResizeW = ((float)Width / (float)sourceWidth);
            percentageResizeH = ((float)Height / (float)sourceHeight);

            // Checking the resize percentage 
            if (percentageResizeH < percentageResizeW)
            {
                percentageResize = percentageResizeW;
                destY = System.Convert.ToInt16((Height - (sourceHeight * percentageResize)) / 2);
            }
            else
            {
                percentageResize = percentageResizeH;
                destX = System.Convert.ToInt16((Width - (sourceWidth * percentageResize)) / 2);
            }

            // Set the new cropped percentage image
            int destWidth = (int)Math.Round(sourceWidth * percentageResize);
            int destHeight = (int)Math.Round(sourceHeight * percentageResize);

            // Create the image object 
            using (Bitmap objBitmap = new Bitmap(Width, Height))
            {
                objBitmap.SetResolution(sourceImage.HorizontalResolution, sourceImage.VerticalResolution);
                using (Graphics objGraphics = Graphics.FromImage(objBitmap))
                {
                    // Set the graphic format for better result cropping 
                    objGraphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    objGraphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    objGraphics.DrawImage(sourceImage, new Rectangle(destX, destY, destWidth, destHeight), new Rectangle(sourceX, sourceY, sourceWidth, sourceHeight), GraphicsUnit.Pixel);

                    // Save the file path, note we use png format to support png file 
                    objBitmap.Save(saveFilePath, ImageFormat.Png);
                }
            }
        }
        public  void ResizeImage4(String fullpath, String destpath, int width, int height)
        {
            //height = 1100;
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm4 = new Bitmap(fs);
            Bitmap thumb4 = new Bitmap(width, height);
            System.Drawing.Graphics g4 = System.Drawing.Graphics.FromImage(thumb4);
            g4.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g4.DrawImage(bm4, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm4.Width, bm4.Height), GraphicsUnit.Pixel);
            g4.Dispose();
            thumb4.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm4.Dispose();
            thumb4.Dispose();
            fs.Close();
        }

        public  void ResizeImage5(String fullpath, String destpath, int width, int height)
        {
            //height = 1100;
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm5 = new Bitmap(fs);
            Bitmap thumb5 = new Bitmap(width, height);
            System.Drawing.Graphics g5 = System.Drawing.Graphics.FromImage(thumb5);
            g5.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g5.DrawImage(bm5, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm5.Width, bm5.Height), GraphicsUnit.Pixel);
            g5.Dispose();
            thumb5.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm5.Dispose();
            thumb5.Dispose();
            fs.Close();
        }

        public  void ResizeImage6(String fullpath, String destpath, int width, int height)
        {
            //height = 1100;
            var fs = new FileStream(fullpath, FileMode.Open);
            Bitmap bm6 = new Bitmap(fs);
            Bitmap thumb6 = new Bitmap(width, height);
            System.Drawing.Graphics g6 = System.Drawing.Graphics.FromImage(thumb6);
            g6.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g6.DrawImage(bm6, new Rectangle(0, 0, width, height), new Rectangle(0, 0, bm6.Width, bm6.Height), GraphicsUnit.Pixel);
            g6.Dispose();
            thumb6.Save(destpath, System.Drawing.Imaging.ImageFormat.Bmp);
            bm6.Dispose();
            thumb6.Dispose();
            fs.Close();
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_Report
    {
        public int Flag { get; set; }
        public int CityFrom { get; set; }
        public int CityTo { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }

        public int BookingStatusId { get; set; }
        public string BookingId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerMobile { get; set; }
    }
}

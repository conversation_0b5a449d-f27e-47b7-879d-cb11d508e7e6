﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Common.Lib
{
    public static class KeyGenerator
    {
        public static string GetUniqueKey(int maxSize = 15)
        {
            try
            {
                char[] chars = new char[62];
                chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".ToCharArray();
                byte[] data = new byte[1];
                using (RNGCryptoServiceProvider crypto = new RNGCryptoServiceProvider())
                {
                    crypto.GetNonZeroBytes(data);
                    data = new byte[maxSize];
                    crypto.GetNonZeroBytes(data);
                }
                StringBuilder result = new StringBuilder(maxSize);
                foreach (byte b in data)
                {
                    result.Append(chars[b % (chars.Length)]);
                }
                return result.ToString();
            }
            catch (Exception)
            {

                throw;
            }
        }

        //public static string GenerateToken(UserMasterViewModel usermastertb)
        //{
        //    var IssuedOn = DateTime.Now;

        //    try
        //    {

        //        string randomnumber =
        //           string.Join(":", new string[]
        //           {

        //             Convert.ToString(usermastertb.U_Id),
        //             KeyGenerator.GetUniqueKey(),
        //             Convert.ToString(usermastertb.UserTypeID),
        //             Convert.ToString(IssuedOn.Ticks)
        //           });

        //        return EncryptionLibrary.EncryptText(randomnumber);
        //    }
        //    catch (Exception)
        //    {

        //        throw;
        //    }
        //}


     
        public static int RandomNumber(int min, int max)
        {
            Random random = new Random();
            return random.Next(min, max);
        }

        // Generate a random string with a given size    
        public static string RandomString(int size, bool lowerCase)
        {
            StringBuilder builder = new StringBuilder();
            Random random = new Random();
            char ch;
            for (int i = 0; i < size; i++)
            {
                ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(ch);
            }
            if (lowerCase)
                return builder.ToString().ToLower();
            return builder.ToString();
        }
        public static string RandomVendorIDGenerator(int length=8, bool isLower=false)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append(RandomString(length, isLower));
            return builder.ToString();
        }

      
        public static string RandomOTP(int min=999, int max=10000)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append(RandomNumber(min, max));
            return builder.ToString();
        }

        public static string GenerateOTP()
        {

            Random generator = new Random();
            string otp = generator.Next(0, 999999).ToString("D6");
            return otp;
        }
    }
}

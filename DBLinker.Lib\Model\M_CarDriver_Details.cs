﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DBLinker.Lib.Model
{
  public  class M_CarDriver_Details
    {
        public int PKID { get; set; }
        public Nullable<int> Vendor_PKID { get; set; }
        public string Vendor_Name { get; set; }
        public Nullable<int> Car_Owner_PKID { get; set; }
        public string Car_Owner_Name { get; set; }
        public Nullable<long> Car_PKID { get; set; }
        public string Car_Name { get; set; }
        public string Driver_Name { get; set; }
        public string Driver_Photo { get; set; }
        public string Driver_Photo_EditFile { get; set; }
        public Nullable<System.DateTime> Driver_DOB { get; set; }
        public string Driver_DOB_String { get; set; }
        public string Driver_FName { get; set; }
        public string Driver_MName { get; set; }
        public string Driver_LName { get; set; }
        public string Drive_Phone_1 { get; set; }
        public string Phone_1_IsWhatsup { get; set; }
        public string Drive_Phone_2 { get; set; }
        public string Driver_Email { get; set; }
        public string Driver_DL { get; set; }
        public string Driver_Aadhaar { get; set; }
        public string Driver_Father_Name { get; set; }
        public string Driver_Mother_Name { get; set; }
        public string Driver_Marrital_Status { get; set; }
        public string Driver_Gender { get; set; }
        public string Driver_Current_Address { get; set; }
        public string Driver_Permanent_Address { get; set; }
        public string Driver_Smoking_Status { get; set; }
        public string Driver_Drinking_Status { get; set; }
        public string Driver_Eating_Type { get; set; }
        public Nullable<bool> Is_Active { get; set; }
        public Nullable<System.DateTime> Created_Date { get; set; }
        public Nullable<System.DateTime> Updated_Date { get; set; }
        public Nullable<int> Created_BY { get; set; }
        public Nullable<int> Updated_BY { get; set; }
        public string Will_Owner_Drive_Car_Status { get; set; }
        public List<M_CarDriver_Docs> M_CarDriver_DocsList { get; set; }       

        public string Driver_Religion { get; set; }
        public Nullable<bool> Driver_Current_Permanent_Same { get; set; }
        public string Driver_Permanent_Address1 { get; set; }
        public string Driver_Permanent_Address2 { get; set; }
        public Nullable<int> Driver_Permanent_Address_State { get; set; }
        public Nullable<int> Driver_Permanent_Address_City { get; set; }

        public string Driver_Permanent_Address_State_Name { get; set; }
        public string Driver_Permanent_Address_City_Name { get; set; }

        public string Driver_Current_Address1 { get; set; }
        public string Driver_Current_Address2 { get; set; }
        public Nullable<int> Driver_Current_Address_State { get; set; }
        public Nullable<int> Driver_Current_Address_City { get; set; }
        public string Driver_Current_Address_State_Name { get; set; }
        public string Driver_Current_Address_City_Name { get; set; }

        public string Special_Remarking { get; set; }

    }
}

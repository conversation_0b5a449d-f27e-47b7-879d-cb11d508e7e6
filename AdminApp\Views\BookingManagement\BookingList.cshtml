﻿@model DBLinker.Lib.Model.M_Booking
@using AdminApp.Mapper;
@{
    ViewBag.Title = "Booking Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();

    // Temporary fix: If PermittedAction is null or empty, set default permissions
    if (PermittedAction == null || PermittedAction.Menu_Id == 0)
    {
        PermittedAction = new DBLinker.Lib.Model.M_Role_Menu_Mapping
        {
            Is_View = true,
            Is_Add = true,
            Is_Edit = true,
            Is_Delete = true
        };
    }
}
    <head>
       

        <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
        <style>
            .ui-autocomplete {
                min-height: 400px;
                overflow-y: auto;
            }
        </style>
    </head>
<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Manager</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Booking List</li>
                </ol>
            </div>
        </div>
        @using (Html.BeginForm())
        {

            @Html.AntiForgeryToken()

            <input type="hidden" name="hdnOperation" id="hdnOperation" />
            <input type="hidden" name="hdnStatus" id="hdnStatus" />
            @Html.HiddenFor(x => x.Is_Active)
            @Html.HiddenFor(x => x.PKID)
            <div class="row" id="dvAddUpdate">
                <div class="col-sm-12">
                    <div class="card-box">
                        <div class="card-head">
                            <header>Cab Details</header>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.City_From_Id, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>City From</span></label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CityList != null)
                                    {

                                        @Html.DropDownListFor(x => x.City_To_Id, new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>City To</span></label>
                                </div>
                            </div>

                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.TripTypeList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Trip_Type_Id, new SelectList(ViewBag.TripTypeList, "PKID", "Trip_Type"), "-- Select Trip Type --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Trip Type</span></label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.CarCategory != null)
                                    {

                                        @Html.DropDownListFor(x => x.Car_Category_Id, new SelectList(ViewBag.CarCategory, "PKID", "Car_Category"), "-- Select Car Category --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>CAR Category</span> </label>
                                </div>
                            </div>

                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.PickUp_Address, new { @class = "mdl-textfield__input" })
                                    @Html.HiddenFor(x => x.PickUpAddressLatitude)

                                    <label class="mdl-textfield__label required"><span>Pick Up Address</span></label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.DropOff_Address, new { @class = "mdl-textfield__input" })
                                    @Html.HiddenFor(x => x.PickUpAddressLongitude)
                                    <label class="mdl-textfield__label required"><span>Drop Off Address</span></label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <button type="button" style="position: absolute;bottom:20px;left:25px;" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSearchCab">Calculate Fare</button>

                            </div>

                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.Duration, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                    <label class="mdl-textfield__label"> Duration</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.Distance, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                    <label class="mdl-textfield__label"> Distance (KM)</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.Basic_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "font-weight:bold;" })
                                    <label class="mdl-textfield__label"> Basic Fare</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                    @Html.TextBoxFor(x => x.GST_Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:black;" })
                                    <label class="mdl-textfield__label"> GST Amount</label>
                                </div>
                            </div>
                            <div class="col-lg-3 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                    @Html.TextBoxFor(x => x.Fare, new { @class = "mdl-textfield__input", @readonly = "readonly", Style = "color:green;font-weight:bold;" })
                                    <label class="mdl-textfield__label"> Fare</label>

                                    @Html.HiddenFor(x => x.Coupon_Discount, new { @id = "Coupon_Discount" })
                                    @Html.HiddenFor(x => x.GST, new { @id = "GST" })

                                    @Html.HiddenFor(x => x.Basic_Fare, new { @id = "Basic_Fare" })
                                    @Html.HiddenFor(x => x.Driver_Charge, new { @id = "Driver_Charge" })
                                    @Html.HiddenFor(x => x.Toll_Charge, new { @id = "Toll_Charge" })
                                    @Html.HiddenFor(x => x.GST, new { @id = "GST" })

                                    <input type="hidden" name="hd_OldBasic_Fare" id="hd_OldBasic_Fare" />
                                    <input type="hidden" name="hd_OldGST_Fare" id="hd_OldGST_Fare" />
                                    <input type="hidden" name="hd_OldFare" id="hd_OldFare" />
                                </div>
                            </div>
                            


                           
                            

                        </div>
                        <div class="card-head">
                            <header>Booking Details</header>
                        </div>
                        <div class="card-body row">

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.completepickupaddress, new { @class = "mdl-textfield__input", @placeholder = "Pick Up Complete Address (Plot no/Colony/City/State)" })

                                    <label class="mdl-textfield__label required"><span>Pick Up Complete Address (Plot no/Colony/City/State)</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.completedropoffpaddress, new { @class = "mdl-textfield__input", @placeholder = "Drop Off Complete Address (Plot no/Colony/City/State)" })

                                    <label class="mdl-textfield__label required"><span>Drop Off Complete Address (Plot no/Colony/City/State)</span></label>
                                </div>
                            </div>


                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.PickUp_Date, new { @class = "mdl-textfield__input", type = "date" })
                                    <label class="mdl-textfield__label required"> <span>Pick Up Date</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.PickUp_Time, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label required"><span>Pick Up Time</span> </label>
                                </div>
                            </div>
                            @*<div class="col-lg-6 p-t-20">
            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                @Html.TextBoxFor(x => x.PickUp_Address, new { @class = "mdl-textfield__input" })
                <label class="mdl-textfield__label">Pick Up Address</label>
            </div>
        </div>
        <div class="col-lg-6 p-t-20">
            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                @Html.TextBoxFor(x => x.DropOff_Address, new { @class = "mdl-textfield__input" })
                <label class="mdl-textfield__label">Drop Off Address</label>
            </div>
        </div>*@
                        </div>

                        <div class="card-head">
                            <header>Personal Details</header>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Name, new { @class = "mdl-textfield__input", @onblur = "isTextValid(this);", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Name</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Mail_Id, new { @class = "mdl-textfield__input", @onblur = "isEmail(this);", @maxlength = "50" })
                                    <label class="mdl-textfield__label required"><span>Mail Id</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Mobile_No1, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label required"> <span>Mobile No 1</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Mobile_No2, new { @class = "mdl-textfield__input txtbx", @maxlength = "10" })
                                    <label class="mdl-textfield__label">Mobile No 2</label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.BookingEditRemark, new { @class = "mdl-textfield__input", @maxlength = "150" })
                                    <label class="mdl-textfield__label">Booking Edit Remark</label>
                                </div>
                            </div>
                        </div>



                        <div class="card-head">
                            <header>Payment Details</header>
                        </div>
                        <div class="card-body row">
                            <div class="card-body row">
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @if (ViewBag.PaymentMethodList != null)
                                        {

                                            @Html.DropDownListFor(x => x.Mode_Of_Payment_Id, new SelectList(ViewBag.PaymentMethodList, "PKID", "Payment_Method_Name"), "-- Select Payment Method --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                        }
                                        <label class="mdl-textfield__label">Payment Mode</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="col-lg-12 p-t-20 text-center">
                            @(PermittedAction.Is_Edit ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnUpdate'>Update</button>") : Html.Raw(""))

                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">cancel</button>
                        </div>

                    </div>
                </div>
            </div>


        }




        <div class="row">
            <div class="col-md-12">
                <div class="card card-box">
                    <div class="card-head">
                        <header>All Booking</header>
                        <div class="tools">
                            <a class="fa fa-repeat btn-color box-refresh" href="javascript:;"></a>
                            <a class="t-collapse btn-color fa fa-chevron-down" href="javascript:;"></a>
                            <a class="t-close btn-color fa fa-times" href="javascript:;"></a>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="row p-b-20">
                            <div class="col-md-6 col-sm-6 col-6">
                                <div class="btn-group">
                                    @(PermittedAction.Is_Add ? Html.Raw("<a href='../BookingManagement/NewBooking' class='btn btn-info'> Add New <i class='fa fa-plus'></i></a>") : Html.Raw("")) 
                                </div>
                            </div>

                        </div>
                        <div class="table-scrollable">
                            <table class="table table-hover table-checkable order-column full-width" id="example4">
                                <thead>
                                    <tr>
                                        <th width="15%">Booking ID</th>
                                        <th width="12%">City From</th>
                                        <th width="12%">City To</th>
                                        <th width="10%">Trip Type</th>
                                        @*<th width="10%">Category</th>*@
                                        <th width="13%">Pick up Date</th>
                                        <th width="13%">Booking Date</th>
                                        <th width="25%">Booking Status</th>
                                        <th width="10%" style="text-align:center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>



                                    @if (ViewBag.BookingList != null)
                                    {
                                        foreach (var item in ViewBag.BookingList)
                                        {

                                    <tr class="odd gradeX">
                                        <input type="hidden" id="<EMAIL>" value="@item.Duration" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Distance" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Is_Active" />
                                        <input type="hidden" id="<EMAIL>" value="@item.City_From_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.City_To_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Trip_Type_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Car_Category_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.GST_Fare" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Fare" />

                                        <input type="hidden" id="<EMAIL>" value="@item.Basic_Fare" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Driver_Charge" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Toll_Charge" />
                                        <input type="hidden" id="<EMAIL>" value="@item.GST" />

                                        <input type="hidden" id="<EMAIL>" value="@item.PickUp_Address" />
                                        <input type="hidden" id="<EMAIL>" value="@item.DropOff_Address" />
                                        <input type="hidden" id="<EMAIL>" value="@item.BookingEditRemark" />

                                        <input type="hidden" id="<EMAIL>" value="@item.PickUpAddressLatitude" />
                                        <input type="hidden" id="<EMAIL>" value="@item.PickUpAddressLongitude" />

                                        <input type="hidden" id="<EMAIL>" value="@item.completepickupaddress" />
                                        <input type="hidden" id="<EMAIL>" value="@item.completedropoffpaddress" />


                                        @if (@item.PickUp_Date != null)
                                        {
                                            <input type="hidden" id="<EMAIL>" value="@item.PickUp_Date.ToShortDateString()" />
                                        }

                                        <input type="hidden" id="<EMAIL>" value="@item.PickUp_Time" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Name" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Mobile_No1" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Mobile_No2" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Mail_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Mode_Of_Payment_Id" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Vendor_PKID" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Car_PKID" />
                                        <input type="hidden" id="<EMAIL>" value="@item.Booking_Status_Id" />
                                        <td>@item.Booking_Id</td>
                                        <td>@item.City_From</td>
                                        <td>@item.City_To</td>
                                        <td>@item.Trip_Type</td>
                                        @*<td>@item.Car_Category</td>*@
                                        <td>
                                            @if (@item.PickUp_Date != null)
                                            {
                                                @item.PickUp_Date.ToShortDateString()
                                            }
                                            else
                                            {
                                                <text>N/A</text>
                                            }
                                        </td>
                                        <td>
                                            @if (@item.Booking_Date != null)
                                            {
                                                @item.Booking_Date.ToShortDateString()
                                            }
                                            else
                                            {
                                                <text>N/A</text>
                                            }
                                        </td>
                                        <td>
                                            @item.Booking_Status

                                            @if (@item.Booking_Status_Id == 4 || @item.Booking_Status_Id == 5 || @item.Booking_Status_Id == 6)
                                            {
                                                <div class="tooltipBooking">
                                                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                    <span class="tooltiptext">  @item.Booking_Remark</span>
                                                </div>
                                            }

                                        </td>
                                        <td class="valigntop">
                                            <div class="btn-group">
                                                <button class="btn btn-xs deepPink-bgcolor dropdown-toggle no-margin" type="button" data-toggle="dropdown" aria-expanded="false">
                                                    Actions
                                                    <i class="fa fa-angle-down"></i>
                                                </button>
                                                <ul class="dropdown-menu" role="menu">

                                                    <li>
                                                        @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='ActivateDeactivate(" + item.PKID+ ")' title='Enable/Disable'><i class='material-icons'>check</i> Enable/Disable</a>") : Html.Raw("")) 
                                                    </li>
                                                    <li>
                                                        @(PermittedAction.Is_Edit ? Html.Raw("<a onclick='Edit(" + item.PKID + ")' title='Edit'><i class='material-icons'>mode_edit</i> Edit</a>") : Html.Raw("")) 
                                                    </li>
                                                    <li>
                                                        @(PermittedAction.Is_Edit ? Html.Raw("<a href='../BookingManagement/AssignCab?id="+ QueryStringEncoding.EncryptString(Convert.ToString(item.PKID), "Booking") + "' title='Action Against Current Booking'><i class='material-icons'>build</i> Action On Booking</a>") : Html.Raw("")) 
                                                    </li>
                                                    <li>
                                                        @(PermittedAction.Is_View ? Html.Raw("<a href='../BookingManagement/ViewBooking?id=" + QueryStringEncoding.EncryptString(Convert.ToString(item.PKID), "Booking") + "' title='View Booking Info'><i class='material-icons'>remove_red_eye</i> View</a>") : Html.Raw(""))
                                                    </li>
                                                    @if (@item.Booking_Status_Id == 6 && PermittedAction.Is_View)
                                                    {
                                                        <li> 
                                                            <a href="../CommonrdlcReport.aspx?ReportID=2&BookingId=@item.Booking_Id" target="_blank" title="View Booking Invoice">
                                                                <i class="material-icons">description</i>
                                                                Generate Invoice
                                                            </a>
                                                        </li>
                                                    }


                                                </ul>
                                            </div>
                                        </td>

                                    </tr>
                                        }

                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>


<script src="~/Scripts/CrudFile/BookingList.js"></script>

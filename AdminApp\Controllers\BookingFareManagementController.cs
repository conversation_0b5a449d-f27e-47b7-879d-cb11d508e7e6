using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using AdminApp.Mapper;
using AdminApp.Services;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
namespace AdminApp.Controllers
{
    [RoleBaseAuthentication]
    public class BookingFareManagementController : Controller
    {
        // GET: BookingFareManagert

        RLTDBContext entity;
        IGenericRepository<RLT_DISCOUNT_COUPON> DiscountCouponRepo;
        IGenericRepository<RLT_BOOKING_RULES> BookingRulesRepo;
        IGenericRepository<RLT_BOOKING_FARE> bookingFareRepo;
        ITwoFactorAuth auth;
        IResizeImage imageResize;
        ObjectMapper map;
        public BookingFareManagementController(ITwoFactorAuth _auth, IResizeImage _imageResize,
            IGenericRepository<RLT_DISCOUNT_COUPON> _DiscountCouponRepo, IGenericRepository<RLT_BOOKING_RULES> _BookingRulesRepo, IGenericRepository<RLT_BOOKING_FARE> _bookingFareRepo)
        {

            imageResize = _imageResize;
            auth = _auth;
            DiscountCouponRepo = _DiscountCouponRepo;
            BookingRulesRepo = _BookingRulesRepo;
            bookingFareRepo = _bookingFareRepo;
            map = new ObjectMapper();
            entity = new RLTDBContext();

        }


        #region Discount Coupon Manager

        public ActionResult DiscountCouponManager()
        {
            var CouponList = DiscountCouponRepo.GetAll().ToList();
            ViewBag.CouponList = CouponList;
            var model = new M_Discount_Coupon();
            return View(model);
        }

        [HttpPost]
        [RoleBaseAuthentication("DiscountCouponManager", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult AddDiscountCouponManager(RLT_DISCOUNT_COUPON obj, HttpPostedFileBase discountCopounImage)
        {
            RLT_DISCOUNT_COUPON Coupon = new RLT_DISCOUNT_COUPON();
            if (discountCopounImage != null)
            {
                string path = Guid.NewGuid() + "_" + discountCopounImage.FileName;
                if (path.Length > 249)
                    path = path.Remove(37, (path.Length - 249));
                string Savepath = Server.MapPath("~/Docs/DiscountCouponManager/") + path;
                discountCopounImage.SaveAs(Savepath);
                Coupon.DiscountImage = path;
            }
            Coupon.Discount_Coupon = obj.Discount_Coupon;
            Coupon.Discount = obj.Discount;
            Coupon.Coupon_Name = obj.Coupon_Name;
            Coupon.Coupon_Remark = obj.Coupon_Remark;
            Coupon.Max_Discount = obj.Max_Discount;
            Coupon.Fare_When_Applied = obj.Fare_When_Applied;
            Coupon.Coupon_Last_Date = obj.Coupon_Last_Date;
            Coupon.CouponMaxTimeAllowance = obj.CouponMaxTimeAllowance;
            Coupon.Is_Active = obj.Is_Active != null ? obj.Is_Active : false;
            Coupon.Created_Date = DateTime.Now;
            Coupon.Created_By = UserServices.getCurrentUserId();
            DiscountCouponRepo.Add(Coupon);
            DiscountCouponRepo.Save(); 
            return Json(Coupon.PKID, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("DiscountCouponManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditDiscountCouponManager(int id = 0)
        {
            var data = entity.RLT_DISCOUNT_COUPON.Where(x => x.PKID == id).FirstOrDefault();
            return data != null ? Json(data, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("DiscountCouponManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditDiscountCouponManager(RLT_DISCOUNT_COUPON obj, HttpPostedFileBase discountCopounImage)
        {
            RLT_DISCOUNT_COUPON retriveCoupon = entity.RLT_DISCOUNT_COUPON.Where(x => x.PKID == obj.PKID).FirstOrDefault();
            if (discountCopounImage != null)
            {
                string path = Guid.NewGuid() + "_" + discountCopounImage.FileName;
                if (path.Length > 249) path = path.Remove(37, (path.Length - 249));
                string Savepath = Server.MapPath("~/Docs/DiscountCouponManager/") + path;
                discountCopounImage.SaveAs(Savepath);
                retriveCoupon.DiscountImage = path;
            }
            retriveCoupon.Discount_Coupon = obj.Discount_Coupon;
            retriveCoupon.Discount = obj.Discount;
            retriveCoupon.Coupon_Name = obj.Coupon_Name;
            retriveCoupon.Coupon_Remark = obj.Coupon_Remark;
            retriveCoupon.Max_Discount = obj.Max_Discount;
            retriveCoupon.Fare_When_Applied = obj.Fare_When_Applied;
            retriveCoupon.Coupon_Last_Date = obj.Coupon_Last_Date;
            retriveCoupon.CouponMaxTimeAllowance = obj.CouponMaxTimeAllowance;
            retriveCoupon.Is_Active = obj.Is_Active != null ? obj.Is_Active : false;
            retriveCoupon.Updated_Date = DateTime.Now;
            retriveCoupon.Updated_By = UserServices.getCurrentUserId();
            DiscountCouponRepo.Edit(retriveCoupon);
            DiscountCouponRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("DiscountCouponManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActivateDeactivateDiscountCouponManager(int id = 0)
        {
            var data = entity.RLT_DISCOUNT_COUPON.Where(x => x.PKID == id).FirstOrDefault();
            if (data != null)
            {
                data.Updated_By = UserServices.getCurrentUserId();
                data.Updated_Date = DateTime.Now;
                data.Is_Active = data.Is_Active != null ? data.Is_Active == true ? false : true : false;
            }
            return Json(entity.SaveChanges() > 0 ? true : false, JsonRequestBehavior.AllowGet);
        }

        #endregion End Discount Coupon Manager

        #region Booking Rules Manager 
        public ActionResult BookingRulesManager()
        {

            var TripTypeList = entity.RLT_TRIP_TYPES.ToList();
            ViewBag.TripTypeList = TripTypeList;

            var CarCategory = entity.RLT_CAR_CATEGORY.ToList().Select(c => new { PKID = c.PKID, Car_Category = c.Car_Category_Abbr + " (" + c.Car_Category_Name + ")" });
            ViewBag.CarCategory = CarCategory;

            var BookingRulesList = (from a in entity.RLT_BOOKING_RULES
                                    join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID
                                    join e in entity.RLT_CAR_CATEGORY on a.Car_Category_Id equals e.PKID
                                    select new M_BookingRules
                                    {
                                        PKID = a.PKID,
                                        Trip_Type = d.Trip_Type,
                                        Trip_Type_Id = a.Trip_Type_Id,
                                        Car_Category_Id = a.Car_Category_Id,
                                        Car_Category = e.Car_Category_Abbr,
                                        Distance_From = a.Distance_From,
                                        Distance_To = a.Distance_To,
                                        NewDistance = a.NewDistance,
                                        Is_Active = a.Is_Active

                                    }).ToList();
            ViewBag.BookingRulesList = BookingRulesList;
            
            return View(BookingRulesList);
        }

        [HttpPost]
        [RoleBaseAuthentication("BookingRulesManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult AddBookingRules(M_BookingRules m_BookingRules)
        {
            RLT_BOOKING_RULES bookingRules = new RLT_BOOKING_RULES();
            bookingRules.Car_Category_Id = m_BookingRules.Car_Category_Id;
            bookingRules.Trip_Type_Id = m_BookingRules.Trip_Type_Id;
            bookingRules.Distance_From = m_BookingRules.Distance_From;
            bookingRules.Distance_To = m_BookingRules.Distance_To;
            bookingRules.NewDistance = m_BookingRules.NewDistance;
            bookingRules.Is_Active = false;
            bookingRules.Created_By = UserServices.getCurrentUserId();
            bookingRules.Created_Date = DateTime.Now;
            entity.RLT_BOOKING_RULES.Add(bookingRules);
            entity.SaveChanges();
            return Json(bookingRules.PKID, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("BookingRulesManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditBookingRules(int Id = 0)
        {
            var data = entity.RLT_BOOKING_RULES.Where(x => x.PKID == Id).FirstOrDefault(); 
            return data != null ? Json(data, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("BookingRulesManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditBookingRules(M_BookingRules bokkingRules)
        {
            var retriveBookingRules = entity.RLT_BOOKING_RULES.Where(x => x.PKID == bokkingRules.PKID).FirstOrDefault();
            retriveBookingRules.Car_Category_Id = bokkingRules.Car_Category_Id;
            retriveBookingRules.Trip_Type_Id = bokkingRules.Trip_Type_Id;
            retriveBookingRules.Distance_From = bokkingRules.Distance_From;
            retriveBookingRules.Distance_To = bokkingRules.Distance_To;
            retriveBookingRules.NewDistance = bokkingRules.NewDistance;
            retriveBookingRules.Updated_By = UserServices.getCurrentUserId();
            retriveBookingRules.Updated_Date = DateTime.Now;
            entity.SaveChanges();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("BookingRulesManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveBookingRules(int Id = 0)
        {
            var retriveBookingRules = entity.RLT_BOOKING_RULES.Where(x => x.PKID == Id).FirstOrDefault();
            retriveBookingRules.Updated_By = UserServices.getCurrentUserId();
            retriveBookingRules.Updated_Date = DateTime.Now;
            retriveBookingRules.Is_Active = retriveBookingRules.Is_Active != null
                                            ? retriveBookingRules.Is_Active == true ? false : true : false;
            int result = entity.SaveChanges() > 0 ? 1 : 0;
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion Booking Rules Manager

        #region Fare Manager 
        public ActionResult FareManager()
        {
            var CityList = entity.RLT_CITY.ToList();
            ViewBag.CityList = CityList;

            var TripTypeList = entity.RLT_TRIP_TYPES.ToList();
            ViewBag.TripTypeList = TripTypeList;

            var CarCategory = entity.RLT_CAR_CATEGORY.ToList().Select(c => new { PKID = c.PKID, Car_Category = c.Car_Category_Abbr + " (" + c.Car_Category_Name + ")" });
            ViewBag.CarCategory = CarCategory;


            var FareList = (from a in entity.RLT_BOOKING_FARE
                            join b in entity.RLT_CITY on a.City_From equals b.PKID
                            join c in entity.RLT_CITY on a.City_To equals c.PKID
                            join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID
                            join e in entity.RLT_CAR_CATEGORY on a.Car_Category_Id equals e.PKID
                            select new M_BookingFareList
                            {
                                PKID = a.PKID,
                                City_From_Id = a.City_From,
                                City_From = b.City_Name,
                                City_To = c.City_Name,
                                City_To_Id = a.City_To,
                                Trip_Type = d.Trip_Type,
                                Trip_Type_Id = a.Trip_Type_Id,
                                Car_Category = e.Car_Category_Abbr,
                                Car_Category_Id = a.Car_Category_Id,
                                Basic_Fare = a.Basic_Fare,
                                Driver_Charge = a.Driver_Charge,
                                Toll_Charge = a.Toll_Charge,
                                Total_Fare = a.Total_Fare,
                                Final_Fare = a.Final_Fare,
                                GST = a.GST,
                                GST_Amount = a.GST_Amount,
                                Is_Active = a.Is_Active,
                                Remark = a.Remark

                            }).ToList();


            ViewBag.FareList = FareList;
            var model = new RLT_BOOKING_FARE();
            return View(model);
        }

        [HttpPost]
        [RoleBaseAuthentication("FareManager", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult AddFareManager(RLT_BOOKING_FARE Fare)
        {
            Fare.Created_By = UserServices.getCurrentUserId();
            Fare.Created_Date = DateTime.Now;
            Fare.Is_Active = false;
            bookingFareRepo.Add(Fare);
            bookingFareRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("FareManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditFareManager(int Id = 0)
        {
            var data = entity.RLT_BOOKING_FARE.Where(x => x.PKID == Id).FirstOrDefault();
            return data != null ? Json(data, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("FareManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditFareManager(RLT_BOOKING_FARE Fare)
        {
            var retriveFare = entity.RLT_BOOKING_FARE.Where(x => x.PKID == Fare.PKID).FirstOrDefault();
            Fare.Updated_By = UserServices.getCurrentUserId();
            Fare.Updated_Date = DateTime.Now;
            Fare.Created_By = retriveFare.Created_By;
            Fare.Created_Date = retriveFare.Created_Date;
            bookingFareRepo.Edit(Fare);
            bookingFareRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("FareManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveFareManager(int Id = 0)
        {
            var retriveFare = entity.RLT_BOOKING_FARE.Where(x => x.PKID == Id).FirstOrDefault();
            retriveFare.Updated_By = UserServices.getCurrentUserId();
            retriveFare.Updated_Date = DateTime.Now;
            retriveFare.Is_Active = retriveFare.Is_Active != null ? retriveFare.Is_Active == true ? false : true : false;
            bookingFareRepo.Edit(retriveFare);
            bookingFareRepo.Save();
            return Json(1, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("FareManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult FareManagerView()
        {
            M_BookingFareList BookingFareDetails = new M_BookingFareList();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "FareManager");
                int FareId = Convert.ToInt16(DecData);

                BookingFareDetails = (from a in entity.RLT_BOOKING_FARE
                                      join b in entity.RLT_CITY on a.City_From equals b.PKID
                                      join c in entity.RLT_CITY on a.City_To equals c.PKID
                                      join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID
                                      join e in entity.RLT_CAR_CATEGORY on a.Car_Category_Id equals e.PKID
                                      where a.PKID == FareId
                                      select new M_BookingFareList
                                      {
                                          City_From = b.City_Name,
                                          City_To = c.City_Name,
                                          Trip_Type = d.Trip_Type,
                                          Car_Category = e.Car_Category_Abbr,
                                          Basic_Fare = a.Basic_Fare,
                                          Driver_Charge = a.Driver_Charge,
                                          Toll_Charge = a.Toll_Charge,
                                          Total_Fare = a.Total_Fare,
                                          Final_Fare = a.Final_Fare,
                                          GST = a.GST,
                                          GST_Amount = a.GST_Amount,
                                          Remark = a.Remark

                                      }).FirstOrDefault();
            }
            return View(BookingFareDetails);

        }
        #endregion FareManager

    }
}
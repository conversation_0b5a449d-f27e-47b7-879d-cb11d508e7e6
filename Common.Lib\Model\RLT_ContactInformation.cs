﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Lib.Model
{
    public  class RLT_ContactInformation
    {
        public int PKID { get; set; }
        public string Address { get; set; }
        public string SalesTelNo { get; set; }
        public string BusinessEmailNo { get; set; }
        public string WorkingHours { get; set; }
        public string GPSCordinationLatitude { get; set; }
        public string GPSCordinationLongitude { get; set; }
        public Nullable<bool> isActive { get; set; }
        public Nullable<System.DateTime> CreateDate { get; set; }
        public string SalesEmailNo { get; set; }
        public string BusinessTelNo { get; set; }
        public string GoogleMapLink { get; set; }
        public string FBLink { get; set; }
        public string TwitterLink { get; set; }
        public string InstagramLink { get; set; }
        public string PinsterLink { get; set; }
        public string GooglePlusLink { get; set; }
    }
}

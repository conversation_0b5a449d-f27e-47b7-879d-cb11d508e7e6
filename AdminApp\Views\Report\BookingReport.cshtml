﻿
@{
    ViewBag.Title = "Booking Report";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PermittedAction = AdminApp.Services.RoleBaseAuthentication.getPermittedAction();
}

<script type="text/javascript">
    $(document).ready(function () {
        $("#BookingReport").parent().parent().find("a").next().slideToggle(500);
        $("#BookingReport").parent().parent().toggleClass('toggled');
        $("#BookingReport").find("a").addClass("selectedMenu");


        $("#btnSearch").click(function () {
              var URL = "../CommonrdlcReport.aspx?ReportID=1";
            if ($('#FromCityId').val() != "")
                URL = URL + "&CityF=" + $('#FromCityId').val(); 
            if ($('#ToCityId').val() != "")
                URL = URL + "&CityT=" + $('#ToCityId').val(); 
            if ($('#FromDate').val() != "")
                URL = URL + "&FromD=" + $('#FromDate').val(); 
            if ($('#ToDate').val() != "")
                URL = URL + "&ToD=" + $('#ToDate').val(); 
            if ($('#BookingStatusId').val() != "")
                URL = URL + "&BookingStatusId=" + $('#BookingStatusId').val(); 
            if ($('#BookingId').val() != "")
                URL = URL + "&BookingId=" + $('#BookingId').val(); 
            if ($('#CustomerName').val() != "")
                URL = URL + "&CustomerName=" + $('#CustomerName').val(); 
            if ($('#CustomerMobile').val() != "")
                URL = URL + "&CustomerMobile=" + $('#CustomerMobile').val(); 

            window.open(URL);

        });
        $("#btnReset").click(function () {

            $("#FromCityId").val('');
        });

    });
    
     
    
</script>

<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Report</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">Booking Report</li>
                </ol>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header>Booking Report</header>
                        <button id="panel-button"
                                class="mdl-button mdl-js-button mdl-button--icon pull-right"
                                data-upgraded=",MaterialButton">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
                            data-mdl-for="panel-button">
                            <li class="mdl-menu__item"><i class="material-icons">assistant_photo</i>Action</li>
                            <li class="mdl-menu__item"><i class="material-icons">print</i>Another action</li>
                            <li class="mdl-menu__item"><i class="material-icons">favorite</i>Something else here</li>
                        </ul>
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @if (ViewBag.CityList != null)
                                {

                                    @Html.DropDownList("FromCityId", new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                }
                                <label class="mdl-textfield__label"><span>City From</span></label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @if (ViewBag.CityList != null)
                                {

                                    @Html.DropDownList("ToCityId", new SelectList(ViewBag.CityList, "PKID", "City_Name"), "-- Select City --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                }
                                <label class="mdl-textfield__label"><span>City To</span></label>
                            </div>
                        </div>

                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("FromDate", "", new { @class = "mdl-textfield__input", type = "date" })
                                <label class="mdl-textfield__label required"><span>From Date</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("ToDate", "", new { @class = "mdl-textfield__input", type = "date" })
                                <label class="mdl-textfield__label"><span>To Date</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @if (ViewBag.BookingStatusList != null)
                                {

                                    @Html.DropDownList("BookingStatusId", new SelectList(ViewBag.BookingStatusList, "PKID", "BOOKING_STATUS"), "-- Select Booking Status --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                }
                                <label class="mdl-textfield__label"><span>Booking Status</span></label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("BookingId", "", new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Booking Id</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("CustomerName", "", new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Customer Name</span> </label>
                            </div>
                        </div>
                        <div class="col-lg-3 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBox("CustomerMobile", "", new { @class = "mdl-textfield__input", @maxlength = "50" })
                                <label class="mdl-textfield__label">Customer Mobile</label>
                            </div>
                        </div>




                        <div class="col-lg-12 p-t-20 text-center">
                            @(PermittedAction.Is_View ? Html.Raw("<button type='button' class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink' id='btnSearch'>Search</button>") : Html.Raw(""))
                            <button type="reset" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 btn-default" id="btnReset">Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>
</div>

